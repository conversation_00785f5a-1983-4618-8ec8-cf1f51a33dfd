(()=>{"use strict";var e={n:r=>{var o=r&&r.__esModule?()=>r.default:()=>r;return e.d(o,{a:o}),o},d:(r,o)=>{for(var t in o)e.o(o,t)&&!e.o(r,t)&&Object.defineProperty(r,t,{enumerable:!0,get:o[t]})},o:(e,r)=>Object.prototype.hasOwnProperty.call(e,r),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},r={};e.r(r),e.d(r,{CurrencyContext:()=>b,CurrencyFactory:()=>c,default:()=>g,getCurrencyData:()=>l,getFilteredCurrencyInstance:()=>S,localiseMonetaryValue:()=>p,unformatLocalisedMonetaryValue:()=>m});const o=window.wp.element,t=window.wp.htmlEntities,n=window.wp.i18n,a=window.wc.number,i=window.wp.deprecated;var s=e.n(i);const c=function(e){let r;function i(e){const r=e.replace(/<!--[\s\S]*?(-->|$)/g,"").replace(/<(script|style)[^>]*>[\s\S]*?(<\/\1>|$)/gi,"").replace(/<\/?[a-z][\s\S]*?(>|$)/gi,"");return r!==e?i(r):r}function c(e){if(e.priceFormat)return i(e.priceFormat.toString());switch(e.symbolPosition){case"left":return"%1$s%2$s";case"right":return"%2$s%1$s";case"left_space":return"%1$s %2$s";case"right_space":return"%2$s %1$s"}return"%1$s%2$s"}function l(e){const o={code:"USD",symbol:"$",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:2,...e};let n=o.precision;null===n?(console.warn("Currency precision is null"),n=NaN):"string"==typeof n&&(n=parseInt(n,10)),r={code:o.code.toString(),symbol:(0,t.decodeEntities)(o.symbol.toString()),symbolPosition:o.symbolPosition.toString(),decimalSeparator:o.decimalSeparator.toString(),priceFormat:c(o),thousandSeparator:o.thousandSeparator.toString(),precision:n}}function u(e,o=!1){const t=(0,a.numberFormat)(r,e);if(""===t)return t;const{priceFormat:i,symbol:s,code:c}=r;return(0,n.sprintf)(i,o?c:s,t)}return l(e),{getCurrencyConfig:()=>({...r}),getDataForCountry:function(e,r={},o={}){const n=r[e];if(!n)return{};const a=o[n.currency_code];return a?{code:n.currency_code,symbol:(0,t.decodeEntities)(a),symbolPosition:n.currency_pos,thousandSeparator:n.thousand_sep,decimalSeparator:n.decimal_sep,precision:n.num_decimals}:{}},setCurrency:l,formatAmount:u,formatCurrency:function(e){return s()("Currency().formatCurrency",{version:"5.0.0",alternative:"Currency().formatAmount",plugin:"WooCommerce",hint:"`formatAmount` accepts the same arguments as formatCurrency"}),u(e)},getPriceFormat:c,formatDecimal(e){if("number"!=typeof e&&(e=parseFloat(e)),Number.isNaN(e))return 0;const{precision:o}=r;return Math.round(e*Math.pow(10,o))/Math.pow(10,o)},formatDecimalString(e){if("number"!=typeof e&&(e=parseFloat(e)),Number.isNaN(e))return"";const{precision:o}=r;return e.toFixed(o)},render:e=>("number"!=typeof e&&(e=parseFloat(e)),e<0?(0,o.createElement)("span",{className:"is-negative"},u(e)):u(e))}};function l(){return s()("getCurrencyData",{version:"3.1.0",alternative:"CurrencyFactory.getDataForCountry",plugin:"WooCommerce Admin",hint:"Pass in the country, locale data, and symbol info to use getDataForCountry"}),{US:{code:"USD",symbol:"$",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:2},EU:{code:"EUR",symbol:"€",symbolPosition:"left",thousandSeparator:".",decimalSeparator:",",precision:2},IN:{code:"INR",symbol:"₹",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:2},GB:{code:"GBP",symbol:"£",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:2},BR:{code:"BRL",symbol:"R$",symbolPosition:"left",thousandSeparator:".",decimalSeparator:",",precision:2},VN:{code:"VND",symbol:"₫",symbolPosition:"right",thousandSeparator:".",decimalSeparator:",",precision:1},ID:{code:"IDR",symbol:"Rp",symbolPosition:"left",thousandSeparator:".",decimalSeparator:",",precision:0},BD:{code:"BDT",symbol:"৳",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:0},PK:{code:"PKR",symbol:"₨",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:2},RU:{code:"RUB",symbol:"₽",symbolPosition:"right",thousandSeparator:" ",decimalSeparator:",",precision:2},TR:{code:"TRY",symbol:"₺",symbolPosition:"left",thousandSeparator:".",decimalSeparator:",",precision:2},MX:{code:"MXN",symbol:"$",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:2},CA:{code:"CAD",symbol:"$",symbolPosition:"left",thousandSeparator:",",decimalSeparator:".",precision:2}}}const u=e=>e.replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),p=(e,r)=>{if("number"==typeof r)return(0,a.numberFormat)(e,r);if("string"==typeof r){const o=u(e.decimalSeparator),t=u(e.thousandSeparator),n=new RegExp(`^\\s*(\\d+|\\d{1,3}(?:${t}\\d{3})*)(?:${o}\\d+)?\\s*$`);return r.replace(n,(r=>{const o=(0,a.parseNumber)(e,r);return(0,a.numberFormat)(e,o)}))}return r},m=(e,r)=>{if(!r)throw new Error("Input value is undefined");if(Number.isFinite(r))return r;if("string"!=typeof r)throw new Error("Input value is not a number or a numeric string");if(r.includes("[")&&r.includes("]"))throw new Error("Input value contains formula");if(!new RegExp(`^\\s*[0-9${u(e.thousandSeparator)}${u(e.decimalSeparator)}]+\\s*$`).test(r))throw new Error("Input value contains non-numeric characters and is not a formula");if(r.split(e.decimalSeparator).length>2||r.includes(e.thousandSeparator)&&r.includes(e.decimalSeparator)&&r.indexOf(e.decimalSeparator)<=r.indexOf(e.thousandSeparator))throw new Error("Invalid decimal separator");const o=r.replace(new RegExp(u(e.thousandSeparator),"g"),"").replace(e.decimalSeparator,".");return Number(o)},d=window.wp.hooks,y=(0,window.wc.wcSettings.getSetting)("currency"),f=c(y),S=e=>{const r=f.getCurrencyConfig(),o=(0,d.applyFilters)("woocommerce_admin_report_currency",r,e);return c(o)},b=(0,o.createContext)(f),g=c;(window.wc=window.wc||{}).currency=r})();