{"name": "woocommerce/add-to-cart-with-options-variation-selector-attribute-options", "title": "Variation Selector: Attribute Options (Beta)", "description": "Display the attribute options associated with a variable product.", "category": "woocommerce-product-elements", "keywords": ["WooCommerce"], "ancestor": ["woocommerce/add-to-cart-with-options-variation-selector-attribute"], "attributes": {"style": {"type": "string", "enum": ["pills", "dropdown"], "default": "pills"}}, "textdomain": "woocommerce", "apiVersion": 3, "$schema": "https://schemas.wp.org/trunk/block.json", "supports": {"inserter": false, "interactivity": true}, "usesContext": ["woocommerce/attributeId", "woocommerce/attributeName", "woocommerce/attributeTerms"], "viewScriptModule": "woocommerce/add-to-cart-with-options-variation-selector-attribute-options", "style": "file:../woocommerce/add-to-cart-with-options-variation-selector-attribute-options-style.css"}