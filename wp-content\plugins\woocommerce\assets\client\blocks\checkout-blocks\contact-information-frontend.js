"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3398],{5299:(e,t,o)=>{o.d(t,{A:()=>c});var s=o(7723);const c=({defaultTitle:e=(0,s.__)("Step","woocommerce"),defaultDescription:t=(0,s.__)("Step description text.","woocommerce"),defaultShowStepNumber:o=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:o}})},8868:(e,t,o)=>{o.r(t),o.d(t,{default:()=>N});var s=o(4921),c=o(1616),r=o(4656),a=o(7143),n=o(7594),i=o(4199),l=o(7723),d=o(7792),u=o(7052),h=o(8696),m=o(5703),p=o(8331),w=o(814),g=o(7827),k=o(6087),_=o(790);const S=()=>{const[e,t]=(0,k.useState)(0),{customerPassword:o}=(0,a.useSelect)((e=>({customerPassword:e(n.checkoutStore).getCustomerPassword()})),[]),{__internalSetCustomerPassword:s}=(0,a.useDispatch)(n.checkoutStore),{setValidationErrors:c,clearValidationError:i}=(0,a.useDispatch)(n.validationStore);return(0,_.jsx)(r.ValidatedTextInput,{type:"password",label:(0,l.__)("Create a password","woocommerce"),className:"wc-block-components-address-form__password",value:o,required:!0,errorId:"account-password",onChange:t=>{s(t),t?e<2?c({"account-password":{message:(0,l.__)("Please create a stronger password","woocommerce"),hidden:!0}}):i("account-password"):c({"account-password":{message:(0,l.__)("Please enter a valid password","woocommerce"),hidden:!0}})},feedback:(0,_.jsx)(g.Ay,{password:o,onChange:e=>t(e)})})},C="wc-guest-checkout-notice",b=()=>{const{shouldCreateAccount:e}=(0,a.useSelect)((e=>({shouldCreateAccount:e(n.checkoutStore).getShouldCreateAccount()}))),{__internalSetShouldCreateAccount:t,__internalSetCustomerPassword:o}=(0,a.useDispatch)(n.checkoutStore),s=(0,m.getSetting)("checkoutAllowsGuest",!1),c=(0,m.getSetting)("checkoutAllowsSignup",!1),i=s&&c,d=!(0,m.getSetting)("generatePassword",!1)&&(i&&e||!s);return s||i||d?(0,_.jsxs)(_.Fragment,{children:[s&&(0,_.jsx)("p",{id:C,className:"wc-block-checkout__guest-checkout-notice",children:(0,l.__)("You are currently checking out as a guest.","woocommerce")}),i&&(0,_.jsx)(r.CheckboxControl,{className:"wc-block-checkout__create-account",label:(0,l.sprintf)(/* translators: Store name */ /* translators: Store name */
(0,l.__)("Create an account with %s","woocommerce"),(0,m.getSetting)("siteTitle","")),checked:e,onChange:e=>{t(e),o("")}}),d&&(0,_.jsx)(S,{})]}):null},f=()=>{const{additionalFields:e,customerId:t}=(0,a.useSelect)((e=>{const t=e(n.checkoutStore);return{additionalFields:t.getAdditionalFields(),customerId:t.getCustomerId()}})),{setAdditionalFields:o}=(0,a.useDispatch)(n.checkoutStore),{billingAddress:s,setEmail:c}=(0,d.C)(),{dispatchCheckoutEvent:i}=(0,u.y)(),l={email:s.email,...e};return(0,_.jsxs)(_.Fragment,{children:[(0,_.jsx)(r.StoreNoticesContainer,{context:h.tG.CONTACT_INFORMATION}),(0,_.jsx)(w.l,{id:"contact",addressType:"contact",ariaDescribedBy:C,onChange:e=>{const{email:t,...s}=e;c(t),i("set-email-address"),o(s)},values:l,fields:p.fO,children:!t&&(0,_.jsx)(b,{})})]})},x={...(0,o(5299).A)({defaultTitle:(0,l.__)("Contact information","woocommerce"),defaultDescription:(0,l.__)("We'll use this email to send you details and updates about your order.","woocommerce")}),className:{type:"string",default:""},lock:{type:"object",default:{remove:!0,move:!0}}},A=`${p.aW}?redirect_to=${encodeURIComponent(window.location.href)}`,j=()=>{const e=(0,a.useSelect)((e=>e(n.checkoutStore).getCustomerId()));return!(0,m.getSetting)("checkoutShowLoginReminder",!0)||e?null:(0,_.jsx)("a",{className:"wc-block-checkout__login-prompt",href:A,children:(0,l.__)("Log in","woocommerce")})},N=(0,c.withFilteredAttributes)(x)((({title:e,description:t,children:o,className:c})=>{const l=(0,a.useSelect)((e=>e(n.checkoutStore).isProcessing())),{showFormStepNumbers:d}=(0,i.O)();return(0,_.jsxs)(r.FormStep,{id:"contact-fields",disabled:l,className:(0,s.A)("wc-block-checkout__contact-fields",c),title:e,description:t,showStepNumber:d,stepHeadingContent:()=>(0,_.jsx)(j,{}),children:[(0,_.jsx)(f,{}),o]})}))}}]);