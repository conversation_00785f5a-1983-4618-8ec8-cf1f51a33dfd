"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[1631],{8084:(t,a,s)=>{s.r(a),s.d(a,{default:()=>l});var e=s(4656),o=s(910),r=s(5460),c=s(790);const l=({className:t=""})=>{const{cartTotals:a}=(0,r.V)();if(!parseFloat(a.total_fees)&&!parseFloat(a.total_discount)&&!parseFloat(a.total_shipping))return null;const s=(0,o.getCurrencyFromPriceResponse)(a);return(0,c.jsx)(e.TotalsWrapper,{className:t,children:(0,c.jsx)(e.Subtotal,{currency:s,values:a})})}}}]);