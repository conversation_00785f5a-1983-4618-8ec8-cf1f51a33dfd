"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[6945],{8546:(e,c,a)=>{a.r(c),a.d(c,{default:()=>r});var s=a(7723),l=a(749),o=a(5460),n=a(1e3),p=a(3932),t=a(790);const r=({className:e})=>{const{cartNeedsShipping:c,shippingRates:a}=(0,o.V)();return c&&(0,p.m3)(a)?(0,t.jsx)(n.TotalsWrapper,{className:e,children:(0,t.jsx)(l.w7,{label:(0,s.__)("Shipping","woocommerce"),placeholder:(0,t.jsx)("span",{className:"wc-block-components-shipping-placeholder__value",children:(0,s.__)("Calculated at checkout","woocommerce")})})}):null}}}]);