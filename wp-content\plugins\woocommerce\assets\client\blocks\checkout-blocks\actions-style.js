"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[3982],{64908:(t,e,c)=>{c.d(e,{w:()=>a});var o=c(47594),s=c(47143),r=c(79530),n=c(28814);const a=()=>{const{isCalculating:t,isBeforeProcessing:e,isProcessing:c,isAfterProcessing:a,isComplete:i,hasError:l}=(0,s.useSelect)((t=>{const e=t(o.checkoutStore);return{isCalculating:e.isCalculating(),isBeforeProcessing:e.isBeforeProcessing(),isProcessing:e.isProcessing(),isAfterProcessing:e.isAfterProcessing(),isComplete:e.isComplete(),hasError:e.hasError()}})),{activePaymentMethod:u,isExpressPaymentMethodActive:p}=(0,s.useSelect)((t=>{const e=t(o.paymentStore);return{activePaymentMethod:e.getActivePaymentMethod(),isExpressPaymentMethodActive:e.isExpressPaymentMethodActive()}})),{onSubmit:d}=(0,r.E)(),{paymentMethods:h={}}=(0,n.m)(),b=c||a||e,m=i&&!l;return{paymentMethodButtonLabel:(h[u]||{}).placeOrderButtonLabel,onSubmit:d,isCalculating:t,isDisabled:c||p,waitingForProcessing:b,waitingForRedirect:m}}},63681:(t,e,c)=>{c.r(e),c.d(e,{default:()=>y});var o=c(41616),s=c(4921),r=c(15703),n=c(425),a=c(64908),i=c(2328),l=c(58034),u=c(56427),p=c(89874),d=c(70910),h=c(14656),b=c(10790);const m=({label:t,fullWidth:e=!1,showPrice:c=!1,priceSeparator:o="·"})=>{const{onSubmit:r,isCalculating:n,isDisabled:m,waitingForProcessing:k,waitingForRedirect:w}=(0,a.w)(),{cartTotals:g}=(0,i.V)(),P=(0,d.getCurrencyFromPriceResponse)(g),x=(0,b.jsxs)("div",{className:"wc-block-components-checkout-place-order-button__text",children:[t,c&&(0,b.jsxs)(b.Fragment,{children:[(0,b.jsx)("style",{children:`.wp-block-woocommerce-checkout-actions-block {\n\t\t\t\t\t\t\t.wc-block-components-checkout-place-order-button__separator {\n\t\t\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\t\t\tcontent: "${o}";\n\t\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}`}),(0,b.jsx)("div",{className:"wc-block-components-checkout-place-order-button__separator"}),(0,b.jsx)("div",{className:"wc-block-components-checkout-place-order-button__price",children:(0,b.jsx)(h.FormattedMonetaryAmount,{value:g.total_price,currency:P})})]})]});return(0,b.jsxs)(p.A,{className:(0,s.A)("wc-block-components-checkout-place-order-button",{"wc-block-components-checkout-place-order-button--full-width":e},{"wc-block-components-checkout-place-order-button--loading":k||w}),onClick:r,disabled:n||m||k||w,children:[k&&(0,b.jsx)(h.Spinner,{}),w&&(0,b.jsx)(u.Icon,{className:"wc-block-components-checkout-place-order-button__icon",icon:l.A}),x]})};var k=c(68696),w=c(71e3),g=c(80426);c(67805);var P=c(42270),x=c(60602);const y=(0,o.withFilteredAttributes)({...P.A,...x.attributes})((({cartPageId:t,showReturnToCart:e,className:c,placeOrderButtonLabel:o,returnToCartButtonLabel:i,priceSeparator:l})=>{const{paymentMethodButtonLabel:u}=(0,a.w)(),p=(0,w.applyCheckoutFilter)({filterName:"placeOrderButtonLabel",defaultValue:u||o||g.H}),d=c?.includes("is-style-with-price")||!1;return(0,b.jsxs)("div",{className:(0,s.A)("wc-block-checkout__actions",c),children:[(0,b.jsx)(h.StoreNoticesContainer,{context:k.tG.CHECKOUT_ACTIONS}),(0,b.jsxs)("div",{className:"wc-block-checkout__actions_row",children:[e&&(0,b.jsx)(n.A,{href:(0,r.getSetting)("page-"+t,!1),children:i}),d&&(0,b.jsx)("style",{children:`.wp-block-woocommerce-checkout-actions-block {\n\t\t\t\t\t\t.wc-block-components-checkout-place-order-button__separator {\n\t\t\t\t\t\t\t&::after {\n\t\t\t\t\t\t\t\tcontent: "${l}";\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t}\n\t\t\t\t\t}`}),(0,b.jsx)(m,{label:p,fullWidth:!e,showPrice:d,priceSeparator:l})]})]})}))}}]);