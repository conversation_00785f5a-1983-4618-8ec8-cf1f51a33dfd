"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3574],{7445:(t,s,e)=>{e.r(s),e.d(s,{default:()=>d});var o=e(9525),n=e(5460),r=e(3135),a=e(790);const c=({className:t})=>{const{cartNeedsPayment:s}=(0,n.V)();return s?(0,a.jsx)("div",{className:t,children:(0,a.jsx)(r.A,{})}):null};var u=e(9030),l=e(5370);const d=t=>{const s=(0,o.N)(l.attributes,t),{showButtonStyles:e,buttonHeight:n,buttonBorderRadius:r}=s;return(0,a.jsx)(u.W.Provider,{value:{showButtonStyles:e,buttonHeight:n,buttonBorderRadius:r},children:(0,a.jsx)(c,{})})}}}]);