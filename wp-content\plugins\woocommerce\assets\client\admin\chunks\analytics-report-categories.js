"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[4409],{74767:(e,t,r)=>{r.d(t,{A:()=>m});var o=r(86087),s=r(66087),a=r(56427),c=r(18537),i=r(98846),n=r(96476),l=r(39793);class m extends o.Component{getCategoryAncestorIds(e,t){const r=[];let o=e.parent;for(;o;)r.unshift(o),o=t.get(o).parent;return r}getCategoryAncestors(e,t){const r=this.getCategoryAncestorIds(e,t);if(r.length)return 1===r.length?t.get((0,s.first)(r)).name+" › ":2===r.length?t.get((0,s.first)(r)).name+" › "+t.get((0,s.last)(r)).name+" › ":t.get((0,s.first)(r)).name+" … "+t.get((0,s.last)(r)).name+" › "}render(){const{categories:e,category:t,query:r}=this.props,o=(0,n.getPersistedQuery)(r);return t?(0,l.jsxs)("div",{className:"woocommerce-table__breadcrumbs",children:[(0,c.decodeEntities)(this.getCategoryAncestors(t,e)),(0,l.jsx)(i.Link,{href:(0,n.getNewPath)(o,"/analytics/categories",{filter:"single_category",categories:t.id}),type:"wc-admin",children:(0,c.decodeEntities)(t.name)})]}):(0,l.jsx)(a.Spinner,{})}}},3541:(e,t,r)=>{r.r(t),r.d(t,{default:()=>I});var o=r(86087),s=r(27723),a=r(52619),c=r(47143),i=r(27752),n=r(33958);const{addCesSurveyForAnalytics:l}=(0,c.dispatch)(i.STORE_KEY),m=(0,a.applyFilters)("woocommerce_admin_categories_report_charts",[{key:"items_sold",label:(0,s.__)("Items sold","woocommerce"),order:"desc",orderby:"items_sold",type:"number"},{key:"net_revenue",label:(0,s.__)("Net sales","woocommerce"),order:"desc",orderby:"net_revenue",type:"currency"},{key:"orders_count",label:(0,s.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),d=(0,a.applyFilters)("woocommerce_admin_category_report_advanced_filters",{filters:{},title:(0,s._x)("Categories match <select/> filters","A sentence describing filters for Categories. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")}),u=[{label:(0,s.__)("All categories","woocommerce"),value:"all"},{label:(0,s.__)("Single category","woocommerce"),value:"select_category",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_category",chartMode:"item-comparison",path:["select_category"],settings:{type:"categories",param:"categories",getLabels:n.aG,labels:{placeholder:(0,s.__)("Type to search for a category","woocommerce"),button:(0,s.__)("Single Category","woocommerce")}}}]},{label:(0,s.__)("Comparison","woocommerce"),value:"compare-categories",chartMode:"item-comparison",settings:{type:"categories",param:"categories",getLabels:n.aG,labels:{helpText:(0,s.__)("Check at least two categories below to compare","woocommerce"),placeholder:(0,s.__)("Search for categories to compare","woocommerce"),title:(0,s.__)("Compare Categories","woocommerce"),update:(0,s.__)("Compare","woocommerce")},onClick:l}}];Object.keys(d.filters).length&&u.push({label:(0,s.__)("Advanced filters","woocommerce"),value:"advanced"});const g=(0,a.applyFilters)("woocommerce_admin_categories_report_filters",[{label:(0,s.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:u}]);var _=r(29491),p=r(66087),y=r(96476),h=r(98846),b=r(43577),w=r(40314),f=r(94111),C=r(74767),v=r(97605),S=r(39793);class k extends o.Component{constructor(e){super(e),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,s.__)("Category","woocommerce"),key:"category",required:!0,isSortable:!0,isLeftAligned:!0},{label:(0,s.__)("Items sold","woocommerce"),key:"items_sold",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Net sales","woocommerce"),key:"net_revenue",isSortable:!0,isNumeric:!0},{label:(0,s.__)("Products","woocommerce"),key:"products_count",isSortable:!0,isNumeric:!0},{label:(0,s.__)("Orders","woocommerce"),key:"orders_count",isSortable:!0,isNumeric:!0}]}getRowsContent(e){const{render:t,formatDecimal:r,getCurrencyConfig:o}=this.context,{categories:s,query:a}=this.props;if(!s)return[];const c=o();return(0,p.map)(e,(e=>{const{category_id:o,items_sold:i,net_revenue:n,products_count:l,orders_count:m}=e,d=s.get(o),u=(0,y.getPersistedQuery)(a);return[{display:(0,S.jsx)(C.A,{query:a,category:d,categories:s}),value:d&&d.name},{display:(0,b.formatValue)(c,"number",i),value:i},{display:t(n),value:r(n)},{display:d&&(0,S.jsx)(h.Link,{href:(0,y.getNewPath)(u,"/analytics/categories",{filter:"single_category",categories:d.id}),type:"wc-admin",children:(0,b.formatValue)(c,"number",l)}),value:l},{display:(0,b.formatValue)(c,"number",m),value:m}]}))}getSummary(e,t=0){const{items_sold:r=0,net_revenue:o=0,orders_count:a=0}=e,{formatAmount:c,getCurrencyConfig:i}=this.context,n=i();return[{label:(0,s._n)("Category","Categories",t,"woocommerce"),value:(0,b.formatValue)(n,"number",t)},{label:(0,s._n)("Item sold","Items sold",r,"woocommerce"),value:(0,b.formatValue)(n,"number",r)},{label:(0,s.__)("Net sales","woocommerce"),value:c(o)},{label:(0,s._n)("Order","Orders",a,"woocommerce"),value:(0,b.formatValue)(n,"number",a)}]}render(){const{advancedFilters:e,filters:t,isRequesting:r,query:o}=this.props,a={helpText:(0,s.__)("Check at least two categories below to compare","woocommerce"),placeholder:(0,s.__)("Search by category name","woocommerce")};return(0,S.jsx)(v.A,{compareBy:"categories",endpoint:"categories",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["items_sold","net_revenue","orders_count"],isRequesting:r,itemIdField:"category_id",query:o,searchBy:"categories",labels:a,tableQuery:{orderby:o.orderby||"items_sold",order:o.order||"desc",extended_info:!0},title:(0,s.__)("Categories","woocommerce"),columnPrefsKey:"categories_report_columns",filters:t,advancedFilters:e})}}k.contextType=f.CurrencyContext;const x=(0,_.compose)((0,c.withSelect)(((e,t)=>{const{isRequesting:r,query:o}=t;if(r||o.search&&(!o.categories||!o.categories.length))return{};const{getItems:s,getItemsError:a,isResolving:c}=e(w.itemsStore),i={per_page:-1};return{categories:s("categories",i),isError:Boolean(a("categories",i)),isRequesting:c("getItems",["categories",i])}})))(k);var q=r(95272),A=r(55737),R=r(68224),j=r(94413),N=r(88711);class F extends o.Component{getChartMeta(){const{query:e}=this.props,t="compare-categories"===e.filter&&e.categories&&e.categories.split(",").length>1,r="single_category"===e.filter&&!!e.categories,o=t||r?"item-comparison":"time-comparison";return{isSingleCategoryView:r,itemsLabel:r?(0,s.__)("%d products","woocommerce"):(0,s.__)("%d categories","woocommerce"),mode:o}}render(){const{isRequesting:e,query:t,path:r}=this.props,{mode:s,itemsLabel:a,isSingleCategoryView:c}=this.getChartMeta(),i={...t};return"item-comparison"===s&&(i.segmentby=c?"product":"category"),(0,S.jsxs)(o.Fragment,{children:[(0,S.jsx)(N.A,{query:t,path:r,filters:g,advancedFilters:d,report:"categories"}),(0,S.jsx)(R.A,{charts:m,endpoint:"products",limitProperties:c?["products","categories"]:["categories"],query:i,selectedChart:(0,q.A)(t.chart,m),filters:g,advancedFilters:d,report:"categories"}),(0,S.jsx)(A.A,{charts:m,filters:g,advancedFilters:d,mode:s,endpoint:"products",limitProperties:c?["products","categories"]:["categories"],path:r,query:i,isRequesting:e,itemsLabel:a,selectedChart:(0,q.A)(t.chart,m)}),c?(0,S.jsx)(j.A,{isRequesting:e,query:i,baseSearchQuery:{filter:"single_category"},hideCompare:c,filters:g,advancedFilters:d}):(0,S.jsx)(x,{isRequesting:e,query:t,filters:g,advancedFilters:d})]})}}const I=F},94413:(e,t,r)=>{r.d(t,{A:()=>S});var o=r(27723),s=r(86087),a=r(29491),c=r(18537),i=r(47143),n=r(66087),l=r(96476),m=r(98846),d=r(43577),u=r(15703),g=r(40314),_=r(94111),p=r(74767),y=r(43128),h=r(97605),b=r(56109),w=r(39793);const f=(0,b.Qk)("manageStock","no"),C=(0,b.Qk)("stockStatuses",{});class v extends s.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,o.__)("Product title","woocommerce"),key:"product_name",required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,o.__)("SKU","woocommerce"),key:"sku",hiddenByDefault:!0,isSortable:!0},{label:(0,o.__)("Items sold","woocommerce"),key:"items_sold",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,o.__)("Net sales","woocommerce"),screenReaderLabel:(0,o.__)("Net sales","woocommerce"),key:"net_revenue",required:!0,isSortable:!0,isNumeric:!0},{label:(0,o.__)("Orders","woocommerce"),key:"orders_count",isSortable:!0,isNumeric:!0},{label:(0,o.__)("Category","woocommerce"),key:"product_cat"},{label:(0,o.__)("Variations","woocommerce"),key:"variations",isSortable:!0},"yes"===f?{label:(0,o.__)("Status","woocommerce"),key:"stock_status"}:null,"yes"===f?{label:(0,o.__)("Stock","woocommerce"),key:"stock",isNumeric:!0}:null].filter(Boolean)}getRowsContent(e=[]){const{query:t}=this.props,r=(0,l.getPersistedQuery)(t),{render:s,formatDecimal:a,getCurrencyConfig:i}=this.context,g=i();return(0,n.map)(e,(e=>{const{product_id:i,items_sold:n,net_revenue:_,orders_count:h}=e,b=e.extended_info||{},{category_ids:v,low_stock_amount:S,manage_stock:k,sku:x,stock_status:q,stock_quantity:A,variations:R=[]}=b,j=(0,c.decodeEntities)(b.name),N=(0,l.getNewPath)(r,"/analytics/orders",{filter:"advanced",product_includes:i}),F=(0,l.getNewPath)(r,"/analytics/products",{filter:"single_product",products:i}),{categories:I}=this.props,P=v&&I&&v.map((e=>I.get(e))).filter(Boolean)||[],V=(0,y.n)(q,A,S)?(0,w.jsx)(m.Link,{href:(0,u.getAdminLink)("post.php?action=edit&post="+i),type:"wp-admin",children:(0,o._x)("Low","Indication of a low quantity","woocommerce")}):C[q];return[{display:(0,w.jsx)(m.Link,{href:F,type:"wc-admin",children:j}),value:j},{display:x,value:x},{display:(0,d.formatValue)(g,"number",n),value:n},{display:s(_),value:a(_)},{display:(0,w.jsx)(m.Link,{href:N,type:"wc-admin",children:h}),value:h},{display:(0,w.jsxs)("div",{className:"woocommerce-table__product-categories",children:[P[0]&&(0,w.jsx)(p.A,{category:P[0],categories:I}),P.length>1&&(0,w.jsx)(m.Tag,{label:(0,o.sprintf)((0,o._x)("+%d more","categories","woocommerce"),P.length-1),popoverContents:P.map((e=>(0,w.jsx)(p.A,{category:e,categories:I,query:t},e.id)))})]}),value:P.map((e=>e.name)).join(", ")},{display:(0,d.formatValue)(g,"number",R.length),value:R.length},"yes"===f?{display:k?V:(0,o.__)("N/A","woocommerce"),value:k?C[q]:null}:null,"yes"===f?{display:k?(0,d.formatValue)(g,"number",A):(0,o.__)("N/A","woocommerce"),value:A}:null].filter(Boolean)}))}getSummary(e){const{products_count:t=0,items_sold:r=0,net_revenue:s=0,orders_count:a=0}=e,{formatAmount:c,getCurrencyConfig:i}=this.context,n=i();return[{label:(0,o._n)("Product","Products",t,"woocommerce"),value:(0,d.formatValue)(n,"number",t)},{label:(0,o._n)("Item sold","Items sold",r,"woocommerce"),value:(0,d.formatValue)(n,"number",r)},{label:(0,o.__)("Net sales","woocommerce"),value:c(s)},{label:(0,o._n)("Order","Orders",a,"woocommerce"),value:(0,d.formatValue)(n,"number",a)}]}render(){const{advancedFilters:e,baseSearchQuery:t,filters:r,hideCompare:s,isRequesting:a,query:c}=this.props,i={helpText:(0,o.__)("Check at least two products below to compare","woocommerce"),placeholder:(0,o.__)("Search by product name or SKU","woocommerce")};return(0,w.jsx)(h.A,{compareBy:s?void 0:"products",endpoint:"products",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["products_count","items_sold","net_revenue","orders_count"],itemIdField:"product_id",isRequesting:a,labels:i,query:c,searchBy:"products",baseSearchQuery:t,tableQuery:{orderby:c.orderby||"items_sold",order:c.order||"desc",extended_info:!0,segmentby:c.segmentby},title:(0,o.__)("Products","woocommerce"),columnPrefsKey:"products_report_columns",filters:r,advancedFilters:e})}}v.contextType=_.CurrencyContext;const S=(0,a.compose)((0,i.withSelect)(((e,t)=>{const{query:r,isRequesting:o}=t;if(o||r.search&&(!r.products||!r.products.length))return{};const{getItems:s,getItemsError:a,isResolving:c}=e(g.itemsStore),i={per_page:-1};return{categories:s("categories",i),isError:Boolean(a("categories",i)),isRequesting:c("getItems",["categories",i])}})))(v)}}]);