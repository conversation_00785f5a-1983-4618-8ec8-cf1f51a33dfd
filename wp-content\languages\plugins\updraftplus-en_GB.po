# Translation of Plugins - UpdraftPlus: WP Backup &amp; Migration Plugin - Stable (latest release) in English (UK)
# This file is distributed under the same license as the Plugins - UpdraftPlus: WP Backup &amp; Migration Plugin - Stable (latest release) package.
msgid ""
msgstr ""
"PO-Revision-Date: 2024-07-03 11:56:42+0000\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Plural-Forms: nplurals=2; plural=n != 1;\n"
"X-Generator: GlotPress/4.0.1\n"
"Language: en_GB\n"
"Project-Id-Version: Plugins - UpdraftPlus: WP Backup &amp; Migration Plugin - Stable (latest release)\n"

#: templates/wp-admin/settings/upload-backups-modal.php:17
msgid "already uploaded"
msgstr "already uploaded"

#: templates/wp-admin/settings/tab-addons.php:121
msgid "Anonymise personal data in your database backups."
msgstr "Anonymise personal data in your database backups."

#: templates/wp-admin/settings/tab-addons.php:119
#: templates/wp-admin/settings/tab-addons.php:120
msgid "Anonymisation functions"
msgstr "Anonymisation functions"

#: methods/s3.php:1414
msgid "Failure: No endpoint details were given."
msgstr "Failure: No endpoint details were given."

#: admin.php:993
msgid "Exit full-screen"
msgstr "Exit full-screen"

#: admin.php:992 admin.php:5254
msgid "Full-screen"
msgstr "Full-screen"

#: admin.php:1177
msgid "The number of restore options that will be sent exceeds the configured maximum in your PHP configuration (max_input_vars)."
msgstr "The number of restore options that will be sent exceeds the configured maximum in your PHP configuration (max_input_vars)."

#: methods/s3generic.php:206
msgid "Virtual-host style"
msgstr "Virtual-host style"

#: methods/s3generic.php:205
msgid "Path style"
msgstr "Path style"

#: methods/s3generic.php:203 methods/s3generic.php:209
msgid "(Read more)"
msgstr "(Read more)"

#: methods/s3generic.php:203
msgid "Read more about bucket access style"
msgstr "Read more about bucket access style"

#: methods/s3generic.php:202
msgid "Bucket access style"
msgstr "Bucket access style"

#: backup.php:4050
msgid "two unsuccessful attempts were made to include it, and it will now be omitted from the backup"
msgstr "two unsuccessful attempts were made to include it, and it will now be omitted from the backup"

#: backup.php:4046
msgid "a second attempt is being made (upon further failure it will be skipped)"
msgstr "a second attempt is being made (upon further failure it will be skipped)"

#: admin.php:4377
msgid "(the asterisk character matches zero or more characters)"
msgstr "(the asterisk character matches zero or more characters)"

#: admin.php:1106
msgid "Please enter part of the file name"
msgstr "Please enter part of the file name"

#: admin.php:1101
msgid "Please select a folder in which the files/directories you would like to exclude are located"
msgstr "Please select a folder in which the files/directories you would like to exclude are located"

#: templates/wp-admin/advanced/site-info.php:42
msgid "UpdraftClone image:"
msgstr "UpdraftClone image:"

#: templates/wp-admin/settings/exclude-modal.php:93
msgid "Add exclusion rule"
msgstr "Add exclusion rule"

#: templates/wp-admin/settings/exclude-modal.php:89
msgid "at the end of their names"
msgstr "at the end of their names"

#: templates/wp-admin/settings/exclude-modal.php:88
msgid "anywhere in their names"
msgstr "anywhere in their names"

#: templates/wp-admin/settings/exclude-modal.php:87
msgid "at the beginning of their names"
msgstr "at the beginning of their names"

#: templates/wp-admin/settings/exclude-modal.php:85
msgid "these characters"
msgstr "these characters"

#: templates/wp-admin/settings/exclude-modal.php:83
msgid "All files/directories containing "
msgstr "All files/directories containing "

#: templates/wp-admin/settings/exclude-modal.php:76
msgid "Select the folder in which the files or sub-directories you would like to exclude are located"
msgstr "Select the folder in which the files or sub-directories you would like to exclude are located"

#: templates/wp-admin/settings/exclude-modal.php:71
msgid "All files/directories containing the given characters in their names"
msgstr "All files/directories containing the given characters in their names"

#: templates/wp-admin/settings/exclude-modal.php:18
msgid "Files/Directories containing the given characters in their names"
msgstr "Files/Directories containing the given characters in their names"

#: templates/wp-admin/notices/horizontal-notice.php:25
msgid "Never"
msgstr "Never"

#: templates/wp-admin/notices/horizontal-notice.php:22
msgid "Maybe later"
msgstr "Maybe later"

#: templates/wp-admin/notices/horizontal-notice.php:18
msgid "Ok, you deserve it"
msgstr "Ok, you deserve it"

#: includes/updraftplus-notices.php:136
msgid "Team Updraft"
msgstr "Team Updraft"

#: includes/updraftplus-notices.php:136
msgid "Thank you so much!"
msgstr "Thank you so much!"

#: includes/updraftplus-notices.php:136
msgid "here"
msgstr "here"

#: admin.php:1037
msgid "Loading..."
msgstr "Loading..."

#: admin.php:6398
msgid "The following remote storage (%s) have only been partially configured, if you are having problems you can try to manually authorise at the UpdraftPlus settings page."
msgstr "The following remote storage (%s) have only been partially configured, if you are having problems you can try to manually authorise at the UpdraftPlus settings page."

#: admin.php:6392
msgid "The following remote storage (%s) have only been partially configured, manual authorization is not supported with this remote storage, please try again and if the problem persists contact support."
msgstr "The following remote storage (%s) have only been partially configured, manual authorisation is not supported with this remote storage, please try again and if the problem persists contact support."

#: methods/backup-module.php:745
msgid "Complete manual authentication"
msgstr "Complete manual authentication"

#: methods/backup-module.php:743
msgid "%s authentication code:"
msgstr "%s authentication code:"

#: methods/backup-module.php:742
msgid "To complete manual authentication, at the orange UpdraftPlus authentication screen select the \"Having problems authenticating?\" link, then copy and paste the code given here."
msgstr "To complete manual authentication, at the orange UpdraftPlus authentication screen select the \"Having problems authenticating?\" link, then copy and paste the code given here."

#: methods/backup-module.php:741
msgid "If you are having problems authenticating with %s you can manually authorize here."
msgstr "If you are having problems authenticating with %s you can manually authorise here."

#: methods/backup-module.php:740
msgid "%s authentication:"
msgstr "%s authentication:"

#: includes/class-commands.php:807
msgid "Missing instance id:"
msgstr "Missing instance ID:"

#: includes/class-commands.php:796
msgid "Missing authentication data:"
msgstr "Missing authentication data:"

#: includes/class-commands.php:767
msgid "Manual authentication is not available for this remote storage method"
msgstr "Manual authentication is not available for this remote storage method"

#: admin.php:5222
msgid "This may prevent the restore procedure from being able to proceed."
msgstr "This may prevent the restore procedure from being able to proceed."

#: admin.php:5222
msgid "Warning: If you can still read these words after the page finishes loading, then there is a JavaScript or jQuery problem in the site."
msgstr "Warning: If you can still read these words after the page finishes loading, then there is a JavaScript or jQuery problem in the site."

#: class-updraftplus.php:5533
msgid "Therefore, affected tables on the current site which already exist will not be replaced by default, to avoid corrupting them (you can review this in the list of tables below)."
msgstr "Therefore, affected tables on the current site which already exist will not be replaced by default, to avoid corrupting them (you can review this in the list of tables below)."

#: class-updraftplus.php:5531
msgid "Therefore it is advised that you take a fresh backup on the source site, using a later version."
msgstr "Therefore it is advised that you take a fresh backup on the source site, using a later version."

#: class-updraftplus.php:5531 class-updraftplus.php:5533
msgid "This backup was created on a previous UpdraftPlus version (%s) which did not correctly backup tables with composite primary keys (such as the term_relationships table, which records tags and product attributes)."
msgstr "This backup was created on a previous UpdraftPlus version (%s) which did not correctly backup tables with composite primary keys (such as the term_relationships table, which records tags and product attributes)."

#: includes/class-backup-history.php:133
msgid "Or, if they are in remote storage, you can connect that remote storage (in the \"Settings\" tab), save your settings, and use the \"Rescan remote storage\" link."
msgstr "Or, if they are in remote storage, you can connect that remote storage (in the \"Settings\" tab), save your settings, and use the \"Rescan remote storage\" link."

#: includes/class-backup-history.php:133
msgid "If you have an existing backup that you wish to upload and restore from, then please use the \"Upload backup files\" link above."
msgstr "If you have an existing backup that you wish to upload and restore from, then please use the \"Upload backup files\" link above."

#: admin.php:1172
msgid "is not"
msgstr "is not"

#: admin.php:1168
msgid "is"
msgstr "is"

#: admin.php:1162
msgid "Day of the month"
msgstr "Day of the month"

#: admin.php:1158
msgid "Day of the week"
msgstr "Day of the week"

#: admin.php:1152
msgid "if all of the following conditions are matched:"
msgstr "if all of the following conditions are matched:"

#: admin.php:1148
msgid "if any of the following conditions are matched:"
msgstr "if any of the following conditions are matched:"

#: admin.php:1144
msgid "on every backup"
msgstr "on every backup"

#: class-updraftplus.php:5483
msgid "This backup is of a site with an empty table prefix, which WordPress does not officially support; the results may be unreliable."
msgstr "This backup is of a site with an empty table prefix, which WordPress does not officially support; the results may be unreliable."

#: admin.php:6502
msgid "The download link is broken or the backup file is no longer available"
msgstr "The download link is broken or the backup file is no longer available"

#: admin.php:6500
msgid "The download link is broken, you may have clicked the link from untrusted source"
msgstr "The download link is broken, you may have clicked the link from an untrusted source"

#: admin.php:6485
msgid "Due to the restriction, some settings can be automatically adjusted, disabled or not available."
msgstr "Due to the restriction, some settings can be automatically adjusted, disabled or not available."

#: admin.php:6483
msgid "Your website is hosted with %s (%s)."
msgstr "Your website is hosted with %s (%s)."

#: admin.php:1138
msgid "Your hosting provider only allows you to take one incremental backup per day."
msgstr "Your hosting provider only allows you to take one incremental backup per day."

#: admin.php:1137 class-updraftplus.php:3320
msgid "Your hosting provider only allows you to take one backup per month."
msgstr "Your hosting provider only allows you to take one backup per month."

#: admin.php:1137 class-updraftplus.php:3320
msgid "You have reached the monthly limit for the number of backups you can create at this time."
msgstr "You have reached the monthly limit for the number of backups you can create at this time."

#: admin.php:1137 admin.php:1138 class-updraftplus.php:3317
#: class-updraftplus.php:3320
msgid "Please contact your hosting company (%s) if you require further support."
msgstr "Please contact your hosting company (%s) if you require further support."

#: class-updraftplus.php:3317
msgid " Your hosting provider only allows you to take one incremental backup per day."
msgstr " Your hosting provider only allows you to take one incremental backup per day."

#: admin.php:1138 class-updraftplus.php:3317
msgid "You have reached the daily limit for the number of incremental backups you can create at this time."
msgstr "You have reached the daily limit for the number of incremental backups you can create at this time."

#: templates/wp-admin/advanced/site-info.php:69
msgid "Current SQL mode:"
msgstr "Current SQL mode:"

#: backup.php:1848
msgid "Failed to backup database table:"
msgstr "Failed to backup database table:"

#: backup.php:1693
msgid "Failed to open directory for reading:"
msgstr "Failed to open directory for reading:"

#: templates/wp-admin/settings/form-contents.php:285
msgid "Your email backup and a report will be sent to"
msgstr "Your email backup and a report will be sent to"

#: admin.php:1126
msgid "Restoring stored routine: %s"
msgstr "Restoring stored routine: %s"

#: includes/updraftclone/temporary-clone-dash-notice.php:52
msgid "Warning: You have no clone tokens remaining and either no subscriptions or no subscription that will renew before the clone expiry date."
msgstr "Warning: you have no clone tokens remaining, and either no subscriptions, or no subscription that will renew before the clone expiry date."

#: class-updraftplus.php:4013
msgid "UpdraftPlus on %s"
msgstr "UpdraftPlus on %s"

#: class-updraftplus.php:5552
msgid "Include all tables not listed below"
msgstr "Include all tables not listed below"

#: admin.php:1136
msgid "Warning: if you continue, you will add all backups stored in the configured remote storage directory (whichever site they were created by)."
msgstr "Warning: if you continue, you will add all backups stored in the configured remote storage directory (whichever site they were created by)."

#: includes/class-remote-send.php:768
msgid "The list of existing sites has been removed"
msgstr "The list of existing sites has been removed"

#: includes/class-remote-send.php:764
msgid "There was an error while trying to remove the list of existing sites."
msgstr "There was an error while trying to remove the list of existing sites."

#: includes/class-remote-send.php:699
msgid "Clear list of existing sites"
msgstr "Clear list of existing sites"

#: includes/class-remote-send.php:695
msgid "Add a site"
msgstr "Add a site"

#: admin.php:5640
msgid "The following remote storage options are configured."
msgstr "The following remote storage options are configured."

#: admin.php:5637
msgid "No remote storage locations with valid options found."
msgstr "No remote storage locations with valid options found."

#: admin.php:944
msgid "You have chosen to send this backup to remote storage, but no remote storage locations have been selected"
msgstr "You have chosen to send this backup to remote storage, but no remote storage locations have been selected"

#: admin.php:962 admin.php:2343
#: templates/wp-admin/settings/downloading-and-restoring.php:21
#: templates/wp-admin/settings/tab-backups.php:21
#: templates/wp-admin/settings/tab-backups.php:44
msgid "Existing backups"
msgstr "Existing backups"

#: class-updraftplus.php:5236
msgid "This is older than the server which you are now restoring onto (version %s)."
msgstr "This is older than the server which you are now restoring onto (version %s)."

#: class-updraftplus.php:710
msgid "Under Maintenance"
msgstr "Under Maintenance"

#: admin.php:1133
msgid "Missing pages:"
msgstr "Missing pages:"

#: restorer.php:3710
msgid "Skipping table %s: user has chosen not to restore this table"
msgstr "Skipping table %s: user has chosen not to restore this table"

#: restorer.php:2447
msgid "An error occurred while attempting to set a new value to the MySQL global log_bin_trust_function_creators variable %s"
msgstr "An error occurred while attempting to set a new value to the MySQL global log_bin_trust_function_creators variable %s"

#: restorer.php:2440
msgid "An error occurred while attempting to retrieve the MySQL global log_bin_trust_function_creators variable %s"
msgstr "An error occurred while attempting to retrieve the MySQL global log_bin_trust_function_creators variable %s"

#: admin.php:1134
msgid "Please check the error log for more details"
msgstr "Please check the error log for more details"

#: templates/wp-admin/settings/existing-backups-table.php:154
msgid "Show all backups..."
msgstr "Show all backups..."

#: templates/wp-admin/settings/existing-backups-table.php:154
msgid "Show more backups..."
msgstr "Show more backups..."

#: central/translations-central.php:80
msgid "Expected parameter(s) missing."
msgstr "Expected parameter(s) missing."

#: central/translations-central.php:79
msgid "Default template"
msgstr "Default template"

#: class-updraftplus.php:5541
msgid "If you do not want to restore all your database tables, then choose some to exclude here."
msgstr "If you do not want to restore all your database tables, then choose some to exclude here."

#: class-updraftplus.php:5236
msgid "You should only proceed if you have checked and are confident (or willing to risk) that your plugins/themes/etc are compatible with the new %s version."
msgstr "You should only proceed if you have checked and are confident (or willing to risk) that your plugins/themes/etc are compatible with the new %s version."

#: includes/updraftclone/temporary-clone-dash-notice.php:47
msgid "%d token"
msgid_plural "%d tokens"
msgstr[0] "%d token"
msgstr[1] "%d tokens"

#: restorer.php:4060
msgid "The Database connection has been closed and cannot be reopened."
msgstr "The database connection has been closed and cannot be reopened."

#: central/translations-central.php:78
msgid "Unattached"
msgstr "Unattached"

#: central/translations-central.php:77
msgid "Selected media has been deleted successfully."
msgstr "Selected media has been deleted successfully."

#: central/translations-central.php:76
msgid "Failed to delete selected media."
msgstr "Failed to delete selected media."

#: central/translations-central.php:75
msgid "Media has been detached from post."
msgstr "Media has been detached from post."

#: central/translations-central.php:74
msgid "Failed to detach media."
msgstr "Failed to detach media."

#: central/translations-central.php:73
msgid "Media has been attached to post."
msgstr "Media has been attached to post."

#: central/translations-central.php:72
msgid "Failed to attach media."
msgstr "Failed to attach media."

#: templates/wp-admin/settings/existing-backups-table.php:166
msgid "Use ctrl / cmd + press to select several items, or ctrl / cmd + shift + press to select all in between"
msgstr "Use ctrl / cmd + press to select several items, or ctrl / cmd + shift + press to select all in between"

#: includes/updraftclone/temporary-clone-dash-notice.php:36
msgid "%s from now"
msgstr "%s from now"

#: admin.php:1112
msgid "You have not selected a restore path for your chosen backups"
msgstr "You have not selected a restore path for your chosen backups"

#: methods/updraftvault.php:547
msgid "Follow this link for help"
msgstr "Follow this link for help"

#: methods/updraftvault.php:544
msgid "Connect to your %s"
msgstr "Connect to your %s"

#: methods/updraftvault.php:542
msgid "Please enter your %s password"
msgstr "Please enter your %s password"

#: methods/updraftvault.php:540
msgid "Please enter your %s email address"
msgstr "Please enter your %s email address"

#: methods/updraftvault.php:536
msgid "Back to other %s options"
msgstr "Back to other %s options"

#: methods/updraftvault.php:512 methods/updraftvault.php:513
#: methods/updraftvault.php:514
msgid "Start %s Subscription"
msgstr "Start %s Subscription"

#: methods/updraftvault.php:516
msgid "Start %s Trial"
msgstr "Start %s Trial"

#: methods/updraftvault.php:527 methods/updraftvault.php:528
#: methods/updraftvault.php:529 methods/updraftvault.php:530
msgid "Start a %s UpdraftVault Subscription"
msgstr "Start a %s UpdraftVault Subscription"

#: methods/updraftvault.php:505
msgid "Read more about %s here."
msgstr "Read more about %s here."

#: methods/updraftvault.php:502
msgid "Connect to your %s account"
msgstr "Connect to your %s account"

#. translators: %s: UpdraftPlus product name
#: templates/wp-admin/settings/tab-addons.php:39
msgid "Get %s here"
msgstr "Get %s here"

#: templates/wp-admin/settings/existing-backups-table.php:165
msgid "Deselect all backups"
msgstr "Deselect all backups"

#: templates/wp-admin/settings/existing-backups-table.php:164
msgid "Select all backups"
msgstr "Select all backups"

#: templates/wp-admin/settings/existing-backups-table.php:163
msgid "Delete selected backups"
msgstr "Delete selected backups"

#: templates/wp-admin/settings/form-contents.php:76
msgid "Retain this many scheduled database backups"
msgstr "Retain this many scheduled database backups"

#: templates/wp-admin/settings/form-contents.php:42
msgid "Retain this many scheduled file backups"
msgstr "Retain this many scheduled file backups"

#. translators: %s: Backup entity
#: templates/wp-admin/settings/file-backup-exclude.php:20
msgid "Add an exclusion rule for %s"
msgstr "Add an exclusion rule for %s"

#: restorer.php:3713
msgid "Skipping table %s: already restored on a prior run; next table to restore: %s"
msgstr "Skipping table: %s already restored on a prior run; next table to restore: %s"

#: restorer.php:890
msgid "Could not delete old path."
msgstr "Could not delete old path."

#: admin.php:5254
msgid "Activity log"
msgstr "Activity log"

#: admin.php:5247
msgid "Cleaning"
msgstr "Cleaning"

#: admin.php:5238
msgid "Verifying"
msgstr "Verifying"

#: admin.php:5235
msgid "Restoration progress:"
msgstr "Restoration progress:"

#: admin.php:1132
msgid "Restore error:"
msgstr "Restore error:"

#: admin.php:1131
msgid "Attempts by the browser to contact the website failed."
msgstr "Attempts by the browser to contact the website failed."

#: admin.php:1130
msgid "Preparing backup files"
msgstr "Preparing backup files"

#: admin.php:1129
msgid "Downloading backup files if needed"
msgstr "Downloading backup files if needed"

#: admin.php:1128
msgid "Begun"
msgstr "Begun"

#: admin.php:1124
msgid "Restoring %s1 files out of %s2"
msgstr "Restoring %s1 files out of %s2"

#: admin.php:1123
msgid "no recent activity; will offer resumption after: %d seconds"
msgstr "no recent activity; will offer resumption after: %d seconds"

#: admin.php:1122
msgid "last activity: %d seconds ago"
msgstr "last activity: %d seconds ago"

#: admin.php:1127 admin.php:5248
msgid "Finished"
msgstr "Finished"

#: templates/wp-admin/settings/delete-and-restore-modals.php:69
msgid "Choose the components to restore:"
msgstr "Choose the components to restore:"

#: admin.php:5225 templates/wp-admin/settings/delete-and-restore-modals.php:36
msgid "UpdraftPlus Restoration"
msgstr "UpdraftPlus Restoration"

#: central/translations-central.php:69
msgid "Unable to connect to the filesystem"
msgstr "Unable to connect to the filesystem"

#: restorer.php:2604
msgid "Found and replaced existing table foreign key constraints as the table prefix has changed."
msgstr "Found and replaced existing table foreign key constraints as the table prefix has changed."

#: admin.php:6242
msgid "An empty WordPress install"
msgstr "An empty WordPress install"

#: admin.php:1119
msgid "credentials"
msgstr "credentials"

#: admin.php:6241
msgid "This current site"
msgstr "This current site"

#: admin.php:6239
msgid "Clone:"
msgstr "Clone:"

#: admin.php:4659
msgid "(%d archive(s) in set, total %s)."
msgstr "(%d archive(s) in set, total %s)."

#: admin.php:1117
msgid "Try it - 1 month for $1!"
msgstr "Try it - 1 month for $1!"

#: includes/updraftplus-tour.php:189
msgid "Otherwise, you can try UpdraftVault for 1 month for only $1!"
msgstr "Otherwise, you can try UpdraftVault for 1 month for only $1!"

#: includes/updraftplus-tour.php:188
msgid "If you have a valid Premium license, you get 1GB of storage included."
msgstr "If you have a valid Premium licence, you get 1GB of storage included."

#: includes/updraftplus-tour.php:135
msgid "Try UpdraftVault for 1 month for only $1!"
msgstr "Try UpdraftVault for 1 month for only $1!"

#. translators: %s: site URL with trailing slash
#: includes/updraftplus-clone.php:108
msgid "Clone of %s"
msgstr "Clone of %s"

#: admin.php:686
msgid "Dismiss notice"
msgstr "Dismiss notice"

#: admin.php:674
msgid "dismiss notice"
msgstr "dismiss notice"

#: admin.php:674
msgid "go here to learn more"
msgstr "go here to learn more"

#: templates/wp-admin/settings/tab-addons.php:330
msgid "More great plugins by the Updraft Team"
msgstr "More great plugins by the Updraft Team"

#: admin.php:674
msgid "You can test upgrading your site on an instant copy using UpdraftClone credits"
msgstr "You can test upgrading your site on an instant copy using UpdraftClone credits"

#: admin.php:686
msgid "You can test running your site on a different PHP (or WordPress) version using UpdraftClone credits."
msgstr "You can test running your site on a different PHP (or WordPress) version using UpdraftClone credits."

#: methods/updraftvault.php:511
msgid "Start Subscription"
msgstr "Start Subscription"

#: methods/updraftvault.php:515
msgid "Start Trial"
msgstr "Start Trial"

#: methods/updraftvault.php:523
msgid "%s month %s trial"
msgstr "%s month %s trial"

#: methods/updraftvault.php:518
msgid "with the option of"
msgstr "with the option of"

#: templates/wp-admin/settings/form-contents.php:341
msgid "The higher the value, the more server resources are required to create the archive."
msgstr "The higher the value, the more server resources are required to create the archive."

#: methods/ftp.php:184
msgid "upload failed"
msgstr "upload failed"

#: methods/cloudfiles.php:140 methods/googledrive.php:1319
#: methods/googledrive.php:1325
msgid "Error: Failed to open local file"
msgstr "Error: Failed to open local file"

#: methods/cloudfiles.php:113 methods/cloudfiles.php:358
#: methods/cloudfiles.php:370
msgid "error - failed to create and access the container"
msgstr "error - failed to create and access the container"

#: methods/cloudfiles.php:157 methods/cloudfiles.php:199
#: methods/openstack-base.php:81
msgid "Error: Failed to upload"
msgstr "Error: Failed to upload"

#: methods/cloudfiles.php:221
msgid "error - failed to re-assemble chunks"
msgstr "error - failed to re-assemble chunks"

#: methods/cloudfiles.php:228 methods/cloudfiles.php:229
msgid "error - failed to upload file"
msgstr "error - failed to upload file"

#: methods/updraftvault.php:499
msgid "Need to get space?"
msgstr "Need to get space?"

#: methods/updraftvault.php:501
msgid "Already got space?"
msgstr "Already got space?"

#: methods/s3.php:184 methods/s3.php:185 methods/s3.php:196 methods/s3.php:197
msgid "Error: Failed to initialise"
msgstr "Error: Failed to initialise"

#: admin.php:3459
msgid "Add this website to UpdraftCentral (remote, centralised control) - free for up to 5 sites."
msgstr "Add this website to UpdraftCentral (remote, centralised control) - free for up to 5 sites."

#: admin.php:3459
msgid "Learn more about UpdraftCentral"
msgstr "Learn more about UpdraftCentral"

#: restorer.php:1280
msgid "The directory does not exist, and the attempt to create it failed"
msgstr "The directory does not exist, and the attempt to create it failed"

#: methods/cloudfiles.php:105 methods/cloudfiles.php:109
#: methods/cloudfiles.php:302 methods/cloudfiles.php:350
#: methods/cloudfiles.php:354
msgid "authentication failed"
msgstr "authentication failed"

#: methods/ftp.php:157 methods/ftp.php:325
msgid "login failure"
msgstr "log in failure"

#: admin.php:1082
msgid "Login successful; reloading information."
msgstr "Log in successful; reloading information."

#: admin.php:1087
msgid "Verifying one-time password..."
msgstr "Verifying one-time password..."

#: admin.php:6407 admin.php:6409
msgid "You have requested saving to remote storage (%s), but without entering any settings for that storage."
msgstr "You have requested saving to remote storage (%s), but without entering any settings for that storage."

#: templates/wp-admin/settings/tab-addons.php:63
msgid "Follow this link to the installation instructions (particularly step 1)."
msgstr "Follow this link to the installation instructions (particularly step 1)."

#: class-updraftplus.php:3808
msgid "Incomplete"
msgstr "Incomplete"

#: templates/wp-admin/settings/tab-addons.php:62
msgid "You successfully purchased UpdraftPremium."
msgstr "You successfully purchased UpdraftPremium."

#: class-updraftplus.php:2516
msgid "The backup is being aborted for a repeated failure to progress."
msgstr "The backup is being aborted for a repeated failure to progress."

#: templates/wp-admin/settings/tab-addons.php:134
msgid "Allows you to only backup changes to your files (such as a new image) that have been made to your site since the last backup."
msgstr "Allows you to only backup changes to your files (such as a new image) that have been made to your site since the last backup."

#: restorer.php:386
msgid "Your WordPress install has old directories from its state before you restored/migrated (technical information: these are suffixed with -old)."
msgstr "Your WordPress installation has old directories from its state before you restored/migrated (technical information: these are suffixed with -old)."

#: includes/class-filesystem-functions.php:559
msgid "Unzip progress: %d out of %d files"
msgstr "Unzip progress: %d out of %d files"

#: admin.php:1110
msgid "File backup options"
msgstr "File backup options"

#: includes/class-wpadmin-commands.php:218
msgid "This backup set contains incremental backups of your files; please select the time you wish to restore your files to"
msgstr "This backup set contains incremental backups of your files; please select the time to which you wish to restore your files"

#: includes/class-filesystem-functions.php:875
msgid "Could not copy file."
msgstr "Could not copy file."

#: includes/class-filesystem-functions.php:871
msgid "Could not extract file from archive."
msgstr "Could not extract file from archive."

#: includes/class-filesystem-functions.php:796
msgid "Could not create directory."
msgstr "Could not create directory."

#: includes/class-filesystem-functions.php:731
#: includes/class-filesystem-functions.php:809
msgid "Could not retrieve file from archive."
msgstr "Could not retrieve file from archive."

#: includes/class-filesystem-functions.php:720
#: includes/class-filesystem-functions.php:727
msgid "Incompatible Archive."
msgstr "Incompatible Archive."

#: includes/class-filesystem-functions.php:467
msgid "Could not access filesystem."
msgstr "Could not access filesystem."

#: templates/wp-admin/settings/tab-addons.php:132
#: templates/wp-admin/settings/tab-addons.php:133
msgid "Incremental backups"
msgstr "Incremental backups"

#: templates/wp-admin/settings/take-backup.php:90
msgid "Perform a backup"
msgstr "Perform a backup"

#: templates/wp-admin/settings/take-backup.php:55
msgid "Add changed files (incremental backup) ..."
msgstr "Add changed files (incremental backup) ..."

#: templates/wp-admin/settings/backupnow-modal.php:24
#: templates/wp-admin/settings/backupnow-modal.php:79
msgid "Find out more about incremental backups here."
msgstr "Find out more about incremental backups here."

#. translators: %s: "UpdraftPlus Premiums" as the product name that has
#. incremental backups feature
#: templates/wp-admin/settings/backupnow-modal.php:20
msgid "Incremental backups are a feature of %s (upgrade by following this link)."
msgstr "Incremental backups are a feature of %s (upgrade by following this link)."

#: templates/wp-admin/settings/backupnow-modal.php:13
msgid "Take an incremental backup"
msgstr "Take an incremental backup"

#: templates/wp-admin/settings/backupnow-modal.php:12
msgid "Take a new backup"
msgstr "Take a new backup"

#: templates/wp-admin/settings/delete-and-restore-modals.php:36
msgid "Restore files from"
msgstr "Restore files from"

#: methods/dropbox.php:599
msgid "%s logo"
msgstr "%s logo"

#: admin.php:6225
msgid "Clone region:"
msgstr "Clone region:"

#: templates/wp-admin/settings/form-contents.php:384
msgid "Read more about Easy Updates Manager"
msgstr "Read more about Easy Updates Manager"

#: templates/wp-admin/settings/file-backup-exclude.php:11
msgid "Confirm change"
msgstr "Confirm change"

#: templates/wp-admin/settings/file-backup-exclude.php:11
msgid "Edit"
msgstr "Edit"

#: templates/wp-admin/settings/exclude-settings-modal/exclude-panel-submit.php:3
#: templates/wp-admin/settings/file-backup-exclude.php:22
msgid "Add an exclusion rule"
msgstr "Add an exclusion rule"

#: templates/wp-admin/settings/exclude-modal.php:62
msgid "Type a file prefix"
msgstr "Type a file prefix"

#: templates/wp-admin/settings/exclude-modal.php:59
#: templates/wp-admin/settings/exclude-modal.php:61
msgid "All files beginning with these characters"
msgstr "All files beginning with these characters"

#: templates/wp-admin/settings/exclude-modal.php:50
msgid "Type an extension like zip"
msgstr "Type an extension like .zip"

#: templates/wp-admin/settings/exclude-modal.php:15
msgid "All files beginning with given characters"
msgstr "All files beginning with given characters"

#: templates/wp-admin/settings/exclude-modal.php:12
#: templates/wp-admin/settings/exclude-modal.php:47
#: templates/wp-admin/settings/exclude-modal.php:49
msgid "All files with this extension"
msgstr "All files with this extension"

#: templates/wp-admin/settings/exclude-modal.php:9
#: templates/wp-admin/settings/exclude-modal.php:25
msgid "File/directory"
msgstr "File/directory"

#: templates/wp-admin/settings/exclude-modal.php:6
msgid "Select a way to exclude files or directories from the backup"
msgstr "Select a way to exclude files or directories from the backup"

#: templates/wp-admin/settings/exclude-modal.php:2
msgid "Exclude files/directories"
msgstr "Exclude files/directories"

#: restorer.php:3402
msgid "Found SET NAMES %s, but changing to %s as suggested by WPDB::determine_charset()."
msgstr "Found SET NAMES %s, but changing to %s as suggested by WPDB::determine_charset()."

#: restorer.php:2954 restorer.php:3005
msgid "Your database user does not have permission to drop tables"
msgstr "Your database user does not have permission to drop tables"

#: templates/wp-admin/settings/temporary-clone.php:59
msgid "Or, use an UpdraftClone key"
msgstr "Or, use an UpdraftClone key"

#: templates/wp-admin/settings/form-contents.php:384
msgid "Ask WordPress to automatically update UpdraftPlus when it finds an available update."
msgstr "Ask WordPress to automatically update UpdraftPlus when it finds an available update."

#: templates/wp-admin/settings/form-contents.php:383
msgid "Automatic updates"
msgstr "Automatic updates"

#: templates/wp-admin/settings/exclude-modal.php:31
msgid "Select a file/folder which you would like to exclude"
msgstr "Select a File/Folder which you would like to exclude"

#: admin.php:4377
msgid "Exclude these from"
msgstr "Exclude these from"

#: admin.php:3448
msgid "Ask WordPress to update UpdraftPlus automatically when an update is available"
msgstr "Ask WordPress to update UpdraftPlus automatically when an update is available"

#: admin.php:1108
msgid "UpdraftClone key is required."
msgstr "UpdraftClone key is required."

#: admin.php:1107
msgid "The exclusion rule which you are trying to add already exists"
msgstr "The exclusion rule which you are trying to add already exists"

#: admin.php:1105
msgid "Please enter a valid file name prefix"
msgstr "Please enter a valid file name prefix"

#: admin.php:1103
msgid "Please enter a valid file extension"
msgstr "Please enter a valid file extension"

#: admin.php:1102
msgid "Please enter a file extension, like zip"
msgstr "Please enter a file extension, like .zip"

#: admin.php:1099
msgid "Are you sure you want to remove this exclusion rule?"
msgstr "Are you sure you want to remove this exclusion rule?"

#: admin.php:1097
msgid "The preparation of the clone data has been aborted."
msgstr "The preparation of the clone data has been aborted."

#: includes/updraftclone/temporary-clone-status.php:422
msgid "your UpdraftPlus.com account"
msgstr "your UpdraftPlus.com account"

#: includes/updraftclone/temporary-clone-status.php:422
msgid "You can check the progress here or in %s"
msgstr "You can check the progress here or in %s"

#: includes/updraftclone/temporary-clone-status.php:422
msgid "Your UpdraftClone is still setting up."
msgstr "Your UpdraftClone is still setting up."

#: includes/updraftclone/temporary-clone-status.php:376
msgid "%s archives remain"
msgstr "%s archives remain"

#: includes/updraftclone/temporary-clone-status.php:376
msgid "The site data has all been received, and its import has begun."
msgstr "The site data has all been received, and its import has begun."

#: includes/updraftclone/temporary-clone-status.php:367
msgid "WordPress installed; now awaiting the site data to be sent."
msgstr "WordPress installed; now awaiting the site data to be sent."

#: includes/updraftclone/temporary-clone-status.php:94
msgid "Clone ready"
msgstr "Clone ready"

#: includes/updraftclone/temporary-clone-status.php:86
msgid "Site data has been deployed"
msgstr "Site data has been deployed"

#: includes/updraftclone/temporary-clone-status.php:84
#: includes/updraftclone/temporary-clone-status.php:346
msgid "Deploying site data"
msgstr "Deploying site data"

#: includes/updraftclone/temporary-clone-status.php:75
msgid "Site data received"
msgstr "Site data received"

#: includes/updraftclone/temporary-clone-status.php:73
#: includes/updraftclone/temporary-clone-status.php:343
msgid "Receiving site data"
msgstr "Receiving site data"

#: includes/updraftclone/temporary-clone-status.php:66
#: includes/updraftclone/temporary-clone-status.php:340
msgid "WordPress installed"
msgstr "WordPress installed"

#: admin.php:6331
msgid "Your clone has started, network information is not yet available but will be displayed here and at your updraftplus.com account once it is ready."
msgstr "Your clone has started, network information is not yet available but will be displayed here and at your updraftplus.com account once it is ready."

#: admin.php:1104
msgid "Please enter characters that begin the filename which you would like to exclude"
msgstr "Please enter characters that begin the filename which you would like to exclude"

#: includes/updraftclone/temporary-clone-status.php:423
msgid "To read FAQs/documentation about UpdraftClone, go here."
msgstr "To read FAQ's/Documentation about UpdraftClone, go here."

#: admin.php:1100
msgid "Please select a file/folder which you would like to exclude"
msgstr "Please select a File/Folder which you would like to exclude"

#: templates/wp-admin/advanced/site-info.php:122
msgid "log results to console"
msgstr "log results to console"

#: templates/wp-admin/settings/temporary-clone.php:58
#: templates/wp-admin/settings/temporary-clone.php:78
msgid "I accept the UpdraftClone terms and conditions"
msgstr "I accept the UpdraftClone Terms and Conditions"

#: templates/wp-admin/settings/temporary-clone.php:15
#: templates/wp-admin/settings/temporary-clone.php:44
msgid "Create a temporary clone on our servers (UpdraftClone)"
msgstr "Create a temporary clone on our servers (UpdraftClone)"

#: templates/wp-admin/settings/migrator-no-migrator.php:6
msgid "Migrate (create a copy of a site on hosting you control)"
msgstr "Migrate (create a copy of a site on hosting you control)"

#: class-updraftplus.php:5545
#: templates/wp-admin/settings/existing-backups-table.php:15
#: templates/wp-admin/settings/existing-backups-table.php:58
msgid "Select All"
msgstr "Select All"

#: templates/wp-admin/advanced/wipe-settings.php:14
msgid "Reset tour"
msgstr "Reset tour"

#: methods/dropbox.php:336 methods/dropbox.php:351
msgid "failed to upload file to %s (see log file for more)"
msgstr "failed to upload file to %s (see log file for more)"

#: templates/wp-admin/advanced/wipe-settings.php:13
msgid "Press this button to take a tour of the plugin."
msgstr "Press this button to take a tour of the plugin."

#: templates/wp-admin/settings/temporary-clone.php:46
msgid "To create a temporary clone you need credit in your account."
msgstr "To create a temporary clone you need credit in your account."

#: templates/wp-admin/settings/temporary-clone.php:37
msgid "If you want, test upgrading to a different PHP or WP version."
msgstr "If you want, test upgrading to a different PHP or WP version."

#: templates/wp-admin/settings/temporary-clone.php:37
msgid "Flexible"
msgstr "Flexible"

#: templates/wp-admin/settings/temporary-clone.php:36
msgid "Takes just the time needed to create a backup and send it."
msgstr "Takes just the time needed to create a backup and send it."

#: templates/wp-admin/settings/temporary-clone.php:36
msgid "Fast"
msgstr "Fast"

#: templates/wp-admin/settings/temporary-clone.php:35
msgid "One VPS (Virtual Private Server) per clone, shared with nobody."
msgstr "One VPS (Virtual Private Server) per clone, shared with nobody."

#: templates/wp-admin/settings/temporary-clone.php:35
msgid "Secure"
msgstr "Secure"

#: templates/wp-admin/settings/temporary-clone.php:34
msgid "Runs on capacity from a leading cloud computing provider."
msgstr "Runs on capacity from a leading cloud computing provider."

#: templates/wp-admin/settings/temporary-clone.php:34
msgid "Reliable"
msgstr "Reliable"

#: templates/wp-admin/settings/temporary-clone.php:33
msgid "Easy"
msgstr "Easy"

#: templates/wp-admin/settings/temporary-clone.php:27
#: templates/wp-admin/settings/temporary-clone.php:46
msgid "You can buy UpdraftClone tokens from our shop, here."
msgstr "You can buy UpdraftClone tokens from our shop, here."

#: templates/wp-admin/settings/temporary-clone.php:27
msgid "Read FAQs here."
msgstr "Read FAQ's here."

#: methods/dropbox.php:216
msgid "error: %s (see log file for more)"
msgstr "error: %s (see log file for more)"

#: methods/dreamobjects.php:52
msgid "Closing 1st October 2018"
msgstr "Closing 1st October 2018"

#: includes/updraftplus-tour.php:256
msgid "Take Tour"
msgstr "Take Tour"

#: includes/updraftplus-tour.php:206
msgid "Log in here to enable all the features you have access to."
msgstr "Log in here to enable all the features to which you have access."

#: includes/updraftplus-tour.php:205
msgid "Connect to updraftplus.com"
msgstr "Connect to updraftplus.com"

#: includes/updraftplus-tour.php:126
msgid "Choose the schedule that you want your backups to run on."
msgstr "Choose the schedule that you want your backups to run on."

#: includes/updraftplus-tour.php:125
msgid "Choose your backup schedule"
msgstr "Choose your backup schedule"

#: includes/updraftplus-tour.php:121
msgid "Congratulations! Your first backup is running."
msgstr "Congratulations! Your first backup is running."

#: includes/updraftplus-tour.php:117 includes/updraftplus-tour.php:122
msgid "Go to settings"
msgstr "Go to settings"

#: includes/updraftplus-tour.php:116 includes/updraftplus-tour.php:121
msgctxt "Translators: %s is a bold tag."
msgid "But to avoid server-wide threats backup regularly to remote cloud storage in %s settings %s"
msgstr "But to avoid server-wide threats, backup regularly to remote cloud storage in %s settings %s"

#: includes/updraftplus-tour.php:116
msgid "Press here to run a manual backup."
msgstr "Press here to run a manual backup."

#: includes/updraftplus-tour.php:115 includes/updraftplus-tour.php:120
msgid "Creating your first backup"
msgstr "Creating your first backup"

#: includes/updraftplus-tour.php:112
msgid "Select what you want to backup"
msgstr "Select what you want to backup"

#: includes/updraftplus-tour.php:111
msgid "Manual backup options"
msgstr "Manual backup options"

#: includes/updraftplus-tour.php:108
msgctxt "updraftplus"
msgid "To make a simple backup to your server, press this button. Or to setup regular backups and remote storage, go to %s settings %s"
msgstr "To make a simple backup to your server, press this button. Or to set up regular backups and remote storage, go to %s settings %s"

#: includes/updraftplus-tour.php:107
msgid "Your first backup"
msgstr "Your first backup"

#: includes/updraftplus-tour.php:103
msgid "Press here to start!"
msgstr "Press here to start!"

#: includes/updraftplus-tour.php:100
msgid "the world’s most trusted backup plugin!"
msgstr "The world’s most trusted backup plugin!"

#: includes/updraftplus-tour.php:100
msgid "Welcome to UpdraftPlus"
msgstr "Welcome to UpdraftPlus"

#: admin.php:1053
msgid "The backup was aborted"
msgstr "The backup was aborted"

#: admin.php:1113 includes/updraftplus-tour.php:132
#: includes/updraftplus-tour.php:184
msgid "Try UpdraftVault!"
msgstr "Try UpdraftVault!"

#: includes/updraftplus-tour.php:99
msgid "UpdraftPlus settings"
msgstr "UpdraftPlus settings"

#: includes/updraftplus-tour.php:96
msgid "End tour"
msgstr "End tour"

#: includes/updraftplus-tour.php:95
msgid "Skip this step"
msgstr "Skip this step"

#: includes/updraftplus-tour.php:94
msgid "Back"
msgstr "Back"

#: includes/updraftclone/temporary-clone-dash-notice.php:47
msgid "Manage your clones"
msgstr "Manage your clones"

#: includes/updraftclone/temporary-clone-dash-notice.php:46
msgid "Your clone will renew on:"
msgstr "Your clone will renew on:"

#: includes/updraftclone/temporary-clone-dash-notice.php:32
msgid "Unable to get renew date"
msgstr "Unable to get renew date"

#: admin.php:6327
msgid "Dashboard:"
msgstr "Dashboard:"

#: admin.php:6326
msgid "Front page:"
msgstr "Front page:"

#: admin.php:6325
msgid "Your clone has started and will be available at the following URLs once it is ready."
msgstr "Your clone has started and will be available at the following URL's once it is ready."

#: includes/class-commands.php:1098
msgid "manage"
msgstr "manage"

#: includes/class-commands.php:1098
msgid "Current clones"
msgstr "Current clones"

#: includes/class-commands.php:1078
msgid "You can buy more temporary clone tokens here."
msgstr "You can buy more temporary clone tokens here."

#: class-updraftplus.php:3706
msgid "Your clone will now deploy this data to re-create your site."
msgstr "Your clone will now deploy this data to re-create your site."

#: admin.php:6279
msgid "Forbid non-administrators to login to WordPress on your clone"
msgstr "Forbid non-administrators to log in to WordPress on your clone"

#: includes/updraftplus-tour.php:169
msgctxt "Translators: UpdraftVault is a product name and should not be translated."
msgid "To get started with UpdraftVault, select one of the options below:"
msgstr "To get started with UpdraftVault, select one of the options below:"

#: includes/updraftplus-tour.php:165 includes/updraftplus-tour.php:198
#: includes/updraftplus-tour.php:209
msgid "Finish"
msgstr "Finish"

#: includes/updraftplus-tour.php:162
msgid "UpdraftPlus Premium has many more exciting features!"
msgstr "UpdraftPlus Premium has many more exciting features!"

#: includes/updraftplus-tour.php:161
msgid "UpdraftPlus Premium and addons"
msgstr "UpdraftPlus Premium and Add-ons"

#: includes/updraftplus-tour.php:159 includes/updraftplus-tour.php:196
#: includes/updraftplus-tour.php:203
msgid "Thank you for taking the tour."
msgstr "Thank you for taking the tour."

#: includes/updraftplus-tour.php:154
msgid "Do you have a few more WordPress sites you want to backup? If yes you can save hours by controlling all your backups in one place from UpdraftCentral."
msgstr "Do you have a few more WordPress sites you want to backup? If yes, you can save hours by controlling all your backups in one place from UpdraftCentral."

#: includes/updraftplus-tour.php:153
msgid "Control all your backups in one place"
msgstr "Control all your backups in one place"

#: includes/updraftplus-tour.php:148
msgid "Congratulations, your settings have successfully been saved."
msgstr "Congratulations, your settings have successfully been saved."

#: includes/updraftplus-tour.php:144
msgid "Press here to save your settings."
msgstr "Press here to save your settings."

#: includes/updraftplus-tour.php:143 includes/updraftplus-tour.php:147
msgid "Save"
msgstr "Save"

#: includes/updraftplus-tour.php:140
msgid "Look through the other settings here, making any changes you’d like."
msgstr "Look through the other settings here, making any changes you’d like."

#: includes/updraftplus-tour.php:139
msgid "More settings"
msgstr "More settings"

#: admin.php:1115 includes/updraftplus-tour.php:134
#: includes/updraftplus-tour.php:162 includes/updraftplus-tour.php:186
#: templates/wp-admin/settings/temporary-clone.php:27
msgid "Find out more here."
msgstr "Find out more here."

#: admin.php:1114 includes/updraftplus-tour.php:133
#: includes/updraftplus-tour.php:185
msgid "UpdraftVault is our remote storage which works seamlessly with UpdraftPlus."
msgstr "UpdraftVault is our remote storage which works seamlessly with UpdraftPlus."

#: templates/wp-admin/settings/tab-addons.php:30
msgid "WooCommerce plugins"
msgstr "WooCommerce plugins"

#: templates/wp-admin/settings/tab-addons.php:29
msgid "Other great plugins"
msgstr "Other great plugins"

#. translators: %s: Remote storage name
#: templates/wp-admin/settings/existing-backups-table.php:86
msgid "Remote storage: %s"
msgstr "Remote storage: %s"

#: methods/addon-base-v2.php:361
msgid "Failed: We were not able to place a file in that directory - please check your credentials."
msgstr "Failed: We were not able to place a file in that directory - please check your credentials."

#: methods/addon-base-v2.php:253
msgid "Failed to download"
msgstr "Failed to download"

#: methods/addon-base-v2.php:239 methods/addon-base-v2.php:259
msgid "Failed to download %s"
msgstr "Failed to download %s"

#: methods/addon-base-v2.php:222
msgid "This storage method does not allow downloading"
msgstr "This storage method does not allow downloading"

#: methods/addon-base-v2.php:138
msgid "failed to list files"
msgstr "Failed to list files"

#: methods/addon-base-v2.php:100 methods/addon-base-v2.php:105
msgid "Failed to upload %s"
msgstr "Failed to upload %s"

#: admin.php:6329 admin.php:6332
msgid "You can find your temporary clone information in your updraftplus.com account here."
msgstr "You can find your temporary clone information in your updraftplus.com account here."

#: class-updraftplus.php:5442
msgid "Choose a default for each table"
msgstr "Choose a default for each table"

#: templates/wp-admin/settings/tab-addons.php:305
msgid "Premium / Find out more"
msgstr "Premium -- Find out more"

#: admin.php:3782
msgid "Sending files to remote site"
msgstr "Sending files to remote site"

#: admin.php:3776
msgid "Clone server being provisioned and booted (can take several minutes)"
msgstr "Clone server being provisioned and booted (can take several minutes)"

#: admin.php:762 admin.php:5225 includes/updraftplus-notices.php:28
msgid "Backup"
msgstr "Backup"

#: admin.php:3258
msgid "Backup / Restore"
msgstr "Backup/Restore"

#: templates/wp-admin/settings/migrator-no-migrator.php:13
msgid "More information here."
msgstr "More information here."

#: restorer.php:3408
msgid "Requested character set (%s) is not present - changing to %s."
msgstr "Requested character set (%s) is not present - changing to %s."

#: includes/updraftclone/temporary-clone-user-notice.php:32
msgid "Allow only administrators to log in"
msgstr "Allow only administrators to log in"

#: includes/updraftclone/temporary-clone-user-notice.php:31
msgid "You can forbid non-admins logins to this cloned site by checking the checkbox below"
msgstr "You can forbid non-admin logins to this cloned site by checking the checkbox below"

#: includes/updraftclone/temporary-clone-user-notice.php:30
msgid "UpdraftPlus temporary clone user login settings:"
msgstr "UpdraftPlus temporary clone user login settings:"

#: includes/updraftclone/temporary-clone-dash-notice.php:44
msgid "Welcome to your UpdraftClone (temporary clone)"
msgstr "Welcome to your UpdraftClone (temporary clone)"

#: includes/updraftclone/temporary-clone-dash-notice.php:43
msgid "Refresh connection"
msgstr "Refresh connection"

#: includes/class-remote-send.php:603
msgid "The entered key was the wrong length - please try again."
msgstr "The key entered was the wrong length - please try again."

#: includes/class-remote-send.php:588
msgid "key"
msgstr "key"

#: includes/class-remote-send.php:563
msgid "You must copy and paste this key on the sending site now - it cannot be shown again."
msgstr "You must copy and paste this key on the sending site now - it cannot be shown again."

#: includes/class-remote-send.php:546
msgid "A key with this name already exists; you must use a unique name."
msgstr "A key with this name already exists; you must use a unique name."

#: includes/class-remote-send.php:471
msgid "Also send this backup to the active remote storage locations"
msgstr "Also send this backup to the active remote storage locations"

#: includes/class-remote-send.php:437 methods/googledrive.php:1526
msgid "For longer help, including screenshots, follow this link."
msgstr "For longer help, including screenshots, follow this link."

#: includes/class-remote-send.php:437
msgid "If sending directly from site to site does not work for you, then there are three other methods - please try one of these instead."
msgstr "If sending directly from site to site does not work for you, then there are three other methods - please try one of these instead."

#: includes/class-remote-send.php:360
msgid "site not found"
msgstr "site not found"

#: includes/class-remote-send.php:327
msgid "Backup data will be sent to:"
msgstr "Backup data will be sent to:"

#: admin.php:4514 templates/wp-admin/settings/backupnow-modal.php:70
#: templates/wp-admin/settings/existing-backups-table.php:70
#: templates/wp-admin/settings/existing-backups-table.php:73
msgid "Only allow this backup to be deleted manually (i.e. keep it even if retention limits are hit)."
msgstr "Only allow this backup to be deleted manually (i.e. keep it even if retention limits are hit)."

#: includes/class-remote-send.php:605 includes/class-remote-send.php:607
#: includes/class-remote-send.php:611
msgid "The entered key was corrupt - please try again."
msgstr "The key entered was corrupt - please try again."

#: includes/class-remote-send.php:730
msgid "Existing keys"
msgstr "Existing keys"

#: includes/class-remote-send.php:721
msgid "No keys to allow remote sites to send backup data here have yet been created."
msgstr "No keys to allow remote sites to send backup data here have yet been created."

#: includes/class-remote-send.php:671
msgid "No receiving sites have yet been added."
msgstr "No receiving sites have yet been added."

#: includes/class-remote-send.php:640
msgid "It is for sending backups to the following site: "
msgstr "It is for sending backups to the following site: "

#: includes/class-remote-send.php:640
msgid "The key was successfully added."
msgstr "The key was successfully added."

#: includes/class-remote-send.php:616
msgid "The entered key does not belong to a remote site (it belongs to this one)."
msgstr "The entered key does not belong to a remote site (it belongs to this one)."

#: admin.php:770 admin.php:3259
msgid "Migrate / Clone"
msgstr "Migrate/Clone"

#: includes/class-commands.php:1149
msgid "The creation of your data for creating the clone should now begin."
msgstr "The creation of your data for creating the clone should now begin."

#: updraftplus.php:126
msgid "Every hour"
msgstr "Every hour"

#: admin.php:3507 includes/class-commands.php:1092
#: includes/class-commands.php:1147 includes/class-commands.php:1149
#: methods/backup-module.php:746
#: templates/wp-admin/settings/temporary-clone.php:85
#: templates/wp-admin/settings/updraftcentral-connect.php:74
msgid "Processing"
msgstr "Processing"

#: templates/wp-admin/settings/updraftcentral-connect.php:73
msgid "Connect to UpdraftCentral Cloud"
msgstr "Connect to UpdraftCentral Cloud"

#: templates/wp-admin/settings/updraftcentral-connect.php:46
msgid "Login or register with this email address"
msgstr "Log in or register with this e-mail address"

#: templates/wp-admin/settings/updraftcentral-connect.php:34
msgid "If not, then choose your details and a new account will be registered."
msgstr "If not, then choose your details and a new account will be registered."

#: templates/wp-admin/settings/updraftcentral-connect.php:33
msgid "If you already have an updraftplus.com account, then enter the details below."
msgstr "If you already have an updraftplus.com account, then enter the details below."

#: templates/wp-admin/settings/updraftcentral-connect.php:29
msgid "Login or register for UpdraftCentral Cloud"
msgstr "Log in or register for UpdraftCentral Cloud"

#: templates/wp-admin/settings/updraftcentral-connect.php:20
msgid "Go here to connect it."
msgstr "Go here to connect it."

#: templates/wp-admin/settings/updraftcentral-connect.php:20
msgid "Or if you prefer to self-host, then you can get the self-hosted version here."
msgstr "Or if you prefer to self-host, then you can get the self-hosted version here."

#: templates/wp-admin/settings/updraftcentral-connect.php:17
msgid "Connect this site to UpdraftCentral Cloud"
msgstr "Connect this site to UpdraftCentral Cloud"

#: templates/wp-admin/settings/updraftcentral-connect.php:12
msgid "Backup, update and manage all your WordPress sites from one dashboard"
msgstr "Backup, update and manage all your WordPress sites from one dashboard"

#: methods/dropbox.php:601 methods/googledrive.php:1535
msgid "this privacy policy"
msgstr "This Privacy Policy"

#: methods/dropbox.php:601 methods/googledrive.php:1535
msgid "Please read %s for use of our %s authorization app (none of your backup data is sent to us)."
msgstr "Please read %s for use of our %s authorisation app (none of your backup data is sent to us)."

#. translators: %s: The name of the missing encryption module.
#: includes/class-updraftplus-encryption.php:149
msgid "Without it, encryption will be a lot slower."
msgstr "Without it, encryption will be a lot slower."

#. translators: %s: The name of the missing encryption module.
#: includes/class-updraftplus-encryption.php:149
msgid "Your web-server does not have the %s module installed."
msgstr "Your web-server does not have the %s module installed."

#: includes/class-commands.php:1091
msgid "Create clone"
msgstr "Create clone"

#: includes/class-commands.php:1077 includes/class-commands.php:1135
msgid "Available temporary clone tokens:"
msgstr "Available temporary clone tokens:"

#: admin.php:3436
msgid "Forgotten your details?"
msgstr "Forgotten your details?"

#: admin.php:3368
msgid "Not yet got an account (it's free)? Go get one!"
msgstr "Not yet got an account (it's free)? Go get one!"

#: admin.php:1093
msgid "For future control of all your UpdraftCentral connections, go to the \"Advanced Tools\" tab."
msgstr "For future control of all your UpdraftCentral connections, go to the \"Advanced Tools\" tab."

#: admin.php:1092
msgid "You can also close this wizard."
msgstr "You can also close this wizard."

#: admin.php:1091
msgid "You need to read and accept the UpdraftCentral Cloud data and privacy policies before you can proceed."
msgstr "You need to read and accept the UpdraftCentral Cloud Data and Privacy Policies before you can proceed."

#: admin.php:1090
msgid "Please wait while you are redirected to UpdraftCentral Cloud."
msgstr "Please wait while you are redirected to UpdraftCentral Cloud."

#: admin.php:1089
msgid "Please wait while the system generates and registers an encryption key for your website with UpdraftCentral Cloud."
msgstr "Please wait while the system generates and registers an encryption key for your website with UpdraftCentral Cloud."

#: admin.php:1088
msgid "Perhaps you would want to login instead."
msgstr "Perhaps you would want to log in instead."

#: admin.php:1086
msgid "Trouble connecting? Try using an alternative method in the advanced security options."
msgstr "Trouble connecting? Try using an alternative method in the advanced security options."

#: admin.php:1085
msgid "An email is required and needs to be in a valid format."
msgstr "An e-mail is required and needs to be in a valid format."

#: admin.php:1084
msgid "Both email and password fields are required."
msgstr "Both e-mail and password fields are required."

#: admin.php:1083
msgid "Registration successful."
msgstr "Registration successful."

#: admin.php:1081 admin.php:1083
msgid "Please follow this link to open %s in a new window."
msgstr "Please follow this link to open %s in a new window."

#: admin.php:1081
msgid "Login successful."
msgstr "Login successful."

#: admin.php:6261 admin.php:6305
msgid "(current version)"
msgstr "(current version)"

#: admin.php:6186
msgid "The file %s has a \"byte order mark\" (BOM) at its beginning."
msgid_plural "The files %s have a \"byte order mark\" (BOM) at their beginning."
msgstr[0] "The file %s has a \"byte order mark\" (BOM) at its beginning."
msgstr[1] "The files %s have a \"byte order mark\" (BOM) at their beginning."

#: admin.php:4282
msgid "press here"
msgstr "press here"

#: admin.php:3485 templates/wp-admin/settings/updraftcentral-connect.php:56
msgid "One Time Password (check your OTP app to get this password)"
msgstr "One Time Password (check your OTP app to get this password)"

#: admin.php:1078 templates/wp-admin/settings/updraftcentral-connect.php:9
msgid "UpdraftCentral Cloud"
msgstr "UpdraftCentral Cloud"

#: templates/wp-admin/settings/upload-backups-modal.php:4
msgid "Select the remote storage destinations you want to upload this backup set to"
msgstr "Select the remote storage destinations to which you want to upload this backup set"

#: templates/wp-admin/settings/upload-backups-modal.php:3
msgid "Upload backup"
msgstr "Upload backup"

#: admin.php:4811
msgid "After pressing this button, you can select where to upload your backup from a list of your currently saved remote storage locations"
msgstr "After pressing this button, you can select where to upload your backup from a list of your currently saved remote storage locations"

#: admin.php:1077
msgid "Please specify the Microsoft OneDrive folder name, not the URL."
msgstr "Please specify the Microsoft OneDrive folder name, not the URL."

#: admin.php:1076
msgid "(already uploaded)"
msgstr "(already uploaded)"

#: admin.php:1075
msgid "You must select at least one remote storage destination to upload this backup set to."
msgstr "You must select at least one remote storage destination to which to upload this backup set."

#: admin.php:1074
msgid "Local backup upload has started; please check the log file to see the upload progress"
msgstr "Local backup upload has started; please check the log file to see the upload progress"

#: admin.php:990 admin.php:4812
msgid "Upload"
msgstr "Upload"

#: admin.php:608
msgid "Are you sure you want to dismiss all UpdraftPlus news forever?"
msgstr "Are you sure you want to dismiss all UpdraftPlus news forever?"

#: admin.php:607
msgid "Dismiss all UpdraftPlus news"
msgstr "Dismiss all UpdraftPlus news"

#: admin.php:606
msgid "UpdraftPlus News"
msgstr "UpdraftPlus News"

#: admin.php:938
msgid "Only email the database backup"
msgstr "Only e-mail the database backup"

#: templates/wp-admin/settings/tab-addons.php:210
msgid "WP CLI"
msgstr "WP CLI"

#: templates/wp-admin/settings/tab-addons.php:211
msgid "WP-CLI support"
msgstr "WP-CLI support"

#: templates/wp-admin/settings/tab-addons.php:212
msgid "WP-CLI commands to take, list and delete backups."
msgstr "WP-CLI commands to take, list and delete backups."

#: templates/wp-admin/notices/thanks-for-using-main-dash.php:4
msgid "Thank you for installing UpdraftPlus!"
msgstr "Thank you for installing UpdraftPlus!"

#: templates/wp-admin/advanced/lock-admin.php:14
msgid "This functionality has been disabled by the site administrator."
msgstr "This functionality has been disabled by the site administrator."

#: restorer.php:898
msgid "Failed to read from the working directory."
msgstr "Failed to read from the working directory."

#: restorer.php:897
msgid "Failed to find a manifest file in the backup."
msgstr "Failed to find a manifest file in the backup."

#: restorer.php:896
msgid "Failed to read the manifest file from backup."
msgstr "Failed to read the manifest file from backup."

#: admin.php:5827
msgid "Remote storage method and instance id are required for authentication."
msgstr "Remote storage method and instance id are required for authentication."

#: methods/backup-module.php:629 methods/dropbox.php:609
msgid "Ensure you are logged into the correct account before continuing."
msgstr "Ensure you are logged into the correct account before continuing."

#: options.php:53
msgid "(Nothing has been logged yet)"
msgstr "(Nothing has been logged yet)"

#: admin.php:5823
msgid "authentication error"
msgstr "authentication error"

#: admin.php:1073
msgid "Currently disabled"
msgstr "Currently disabled"

#: admin.php:1072
msgid "Currently enabled"
msgstr "Currently enabled"

#: templates/wp-admin/settings/tab-addons.php:54
msgid "If you have purchased from UpdraftPlus.Com, then follow this link to the installation instructions (particularly step 1)."
msgstr "If you have purchased from UpdraftPlus.Com, then follow this link to the installation instructions (particularly step 1)."

#: templates/wp-admin/settings/tab-addons.php:54
msgid "You are currently using the free version of UpdraftPlus."
msgstr "You are currently using the free version of UpdraftPlus."

#: templates/wp-admin/settings/tab-addons.php:44
msgid "Get it here"
msgstr "Get it here"

#: templates/wp-admin/settings/existing-backups-table.php:84
msgid "remote site"
msgstr "remote site"

#: restorer.php:2648
msgid "Requested table collation (%1$s) is not present - changing to %2$s."
msgid_plural "Requested table collations (%1$s) are not present - changing to %2$s."
msgstr[0] "Requested table collation (%1$s) is not present - changing to %2$s."
msgstr[1] "Requested table collations (%1$s) are not present - changing to %2$s."

#: class-updraftplus.php:5419
msgid "Your chosen replacement collation"
msgstr "Your chosen replacement collation"

#: class-updraftplus.php:5396
msgid "You can choose another suitable collation instead and continue with the restoration (at your own risk)."
msgstr "You can choose another suitable collation instead and continue with the restoration (at your own risk)."

#: class-updraftplus.php:5396
msgid "The database server that this WordPress site is running on doesn't support the collation (%s) used in the database which you are trying to import."
msgid_plural "The database server that this WordPress site is running on doesn't support multiple collations (%s) used in the database which you are trying to import."
msgstr[0] "The database server that this WordPress site is running on doesn't support the collation (%s) used in the database which you are trying to import."
msgstr[1] "The database server that this WordPress site is running on doesn't support multiple collations (%s) used in the database which you are trying to import."

#: central/translations-central.php:43
msgid "URL for the site of your UpdraftCentral dashboard"
msgstr "URL for the site of your UpdraftCentral dashboard"

#: central/translations-central.php:42
msgid "Enter the URL where your self-hosted install of UpdraftCentral is located:"
msgstr "Enter the URL where your self-hosted install of UpdraftCentral is located:"

#: central/translations-central.php:41
msgid "A website where you have installed %s"
msgstr "A website where you have installed %s"

#: central/translations-central.php:40
msgid "Self-hosted dashboard"
msgstr "Self-hosted dashboard"

#: central/translations-central.php:21
msgid "At your UpdraftCentral dashboard you should press the \"Add Site\" button then paste the key in the input box."
msgstr "At your UpdraftCentral dashboard you should press the \"Add Site\" button then paste the key in the input box."

#: restorer.php:2589
msgid "Requested table character set (%s) is not present - changing to %s."
msgstr "Requested table character set (%s) is not present - changing to %s."

#: class-updraftplus.php:5371
msgid "Your chosen character set to use instead:"
msgstr "Your chosen character set to use instead:"

#: class-updraftplus.php:5361
msgid "You can choose another suitable character set instead and continue with the restoration at your own risk."
msgstr "You can choose another suitable character set instead and continue with the restoration at your own risk."

#: class-updraftplus.php:5361
msgid "The database server that this WordPress site is running on doesn't support the character set (%s) which you are trying to import."
msgid_plural "The database server that this WordPress site is running on doesn't support the character sets (%s) which you are trying to import."
msgstr[0] "The database server that this WordPress site is running on doesn't support the character set (%s) which you are trying to import."
msgstr[1] "The database server that this WordPress site is running on doesn't support the character sets (%s) which you are trying to import."

#: class-updraftplus.php:5186
msgid "This backup set is of this site, but at the time of the backup you were using %s, whereas the site now uses %s."
msgstr "This backup set is of this site, but at the time of the backup you were using %s, whereas the site now uses %s."

#: central/translations-central.php:68
msgid "Create another key"
msgstr "Create another key"

#: central/translations-central.php:45
msgid "UpdraftCentral dashboard connection details"
msgstr "UpdraftCentral dashboard connection details"

#: central/translations-central.php:44 includes/updraftplus-tour.php:93
#: templates/wp-admin/settings/delete-and-restore-modals.php:115
msgid "Next"
msgstr "Next"

#: central/translations-central.php:39
msgid "an account"
msgstr "an account"

#: central/translations-central.php:38
msgid "i.e. if you have %s there"
msgstr "i.e. if you have %s there"

#: central/translations-central.php:37
msgid "Connect this site to an UpdraftCentral dashboard found at..."
msgstr "Connect this site to an UpdraftCentral dashboard found at..."

#: central/translations-central.php:34
msgid "Manage existing keys (%d)..."
msgstr "Manage existing keys (%d)..."

#: central/translations-central.php:27
msgid "There are no UpdraftCentral dashboards that can currently control this site."
msgstr "There are no UpdraftCentral dashboards that can currently control this site."

#: central/translations-central.php:23
msgid "You can now control this site via your UpdraftCentral dashboard at %s."
msgstr "You can now control this site via your UpdraftCentral dashboard at %s."

#: central/translations-central.php:22
msgid "Detailed instructions for this can be found at %s"
msgstr "Detailed instructions for this can be found at %s"

#: central/translations-central.php:20
msgid "You now need to copy the key below and enter it at your %s."
msgstr "You now need to copy the key below and enter it at your %s."

#: central/translations-central.php:19
msgid "UpdraftCentral key created successfully"
msgstr "UpdraftCentral key created successfully"

#: admin.php:1065 central/translations-central.php:87
msgid "Please enter a valid URL e.g http://example.com"
msgstr "Please enter a valid URL e.g http://example.com"

#: admin.php:1064 central/translations-central.php:86
msgid "Please enter the URL where your UpdraftCentral dashboard is hosted."
msgstr "Please enter the URL where your UpdraftCentral dashboard is hosted."

#: backup.php:609 backup.php:2883 class-updraftplus.php:2571
#: class-updraftplus.php:2640 includes/class-search-replace.php:291
#: includes/class-storage-methods-interface.php:378 restorer.php:736
msgid "A PHP exception (%s) has occurred: %s"
msgstr "A PHP exception (%s) has occurred: %s"

#: backup.php:615 backup.php:2892 class-updraftplus.php:2580
#: class-updraftplus.php:2647 includes/class-search-replace.php:298
#: includes/class-storage-methods-interface.php:387 restorer.php:748
msgid "A PHP fatal error (%s) has occurred: %s"
msgstr "A PHP fatal error (%s) has occurred: %s"

#: methods/googledrive.php:1543
msgid "To de-authorize UpdraftPlus (all sites) from accessing your Google Drive, follow this link to your Google account settings."
msgstr "To de-authorise UpdraftPlus (all sites) from accessing your Google Drive, follow this link to your Google account settings."

#: methods/backup-module.php:700 methods/dropbox.php:608
#: methods/googledrive.php:1542
msgid "Follow this link to remove these settings for %s."
msgstr "Follow this link to remove these settings for %s."

#: includes/class-wpadmin-commands.php:638
msgid "archive"
msgstr "archive"

#: includes/class-wpadmin-commands.php:632
msgid "WordPress Core"
msgstr "WordPress Core"

#: includes/class-wpadmin-commands.php:629
msgid "Extra database"
msgstr "Extra database"

#: admin.php:4658
msgid "Press here to download or browse"
msgstr "Press here to download or browse"

#: admin.php:1666 admin.php:1676
msgid "Error: invalid path"
msgstr "Error: invalid path"

#: admin.php:1391
msgid "An error occurred when fetching storage module options: "
msgstr "An error occurred when fetching storage module options: "

#: admin.php:1062
msgid "Loading log file"
msgstr "Loading log file"

#: admin.php:1060
msgid "Search"
msgstr "Search"

#: admin.php:1059
msgid "Select a file to view information about it"
msgstr "Select a file to view information about it"

#: admin.php:1058
msgid "Browsing zip file"
msgstr "Browsing zip file"

#: admin.php:1024
msgid "With UpdraftPlus Premium, you can directly download individual files from here."
msgstr "With UpdraftPlus Premium, you can directly download individual files from here."

#: admin.php:968
msgid "Browse contents"
msgstr "Browse contents"

#: restorer.php:3113
msgid "Skipped tables:"
msgstr "Skipped tables:"

#: templates/wp-admin/settings/backupnow-modal.php:8
msgid "All WordPress tables will be backed up."
msgstr "All WordPress tables will be backed up."

#: admin.php:1057
msgid "Further information may be found in the browser JavaScript console, and the server PHP error logs."
msgstr "Further information may be found in the browser JavaScript console, and the server PHP error logs."

#: admin.php:1057
msgid "That you are attempting to upload a zip file previously created by UpdraftPlus."
msgstr "That you are attempting to upload a zip file previously created by UpdraftPlus."

#: admin.php:1057
msgid "The available memory on the server."
msgstr "The available memory on the server."

#: admin.php:1057
msgid "Any settings in your .htaccess or web.config file that affects the maximum upload or post size."
msgstr "Any settings in your .htaccess or web.config file that affects the maximum upload or post size."

#: admin.php:1056
msgid "HTTP code:"
msgstr "HTTP code:"

#: templates/wp-admin/settings/backupnow-modal.php:8
msgid "With UpdraftPlus Premium, you can choose to backup non-WordPress tables, backup only specified tables, and backup other databases too."
msgstr "With UpdraftPlus Premium, you can choose to backup non-WordPress tables, backup only specified tables, and backup other databases too."

#: admin.php:943
msgid "You have chosen to backup a database, but no tables have been selected"
msgstr "You have chosen to backup a database, but no tables have been selected"

#: templates/wp-admin/settings/tab-addons.php:335
#: templates/wp-admin/settings/tab-addons.php:341
#: templates/wp-admin/settings/tab-addons.php:347
#: templates/wp-admin/settings/tab-addons.php:353
msgid "Find out more"
msgstr "Find out more"

#: templates/wp-admin/settings/tab-addons.php:304
msgid "UpdraftPlus has its own embedded storage option, providing a zero-hassle way to download, store and manage all your backups from one place."
msgstr "UpdraftPlus has its own embedded storage option, providing a zero-hassle way to download, store and manage all your backups from one place."

#: methods/updraftvault.php:122 templates/wp-admin/settings/tab-addons.php:301
msgid "UpdraftVault"
msgstr "UpdraftVault"

#: templates/wp-admin/settings/tab-addons.php:290
msgid "Lock access to UpdraftPlus via a password so you choose which admin users can access backups."
msgstr "Lock access to UpdraftPlus via a password so you choose which admin users can access backups."

#: templates/wp-admin/settings/tab-addons.php:275
#: templates/wp-admin/settings/tab-addons.php:276
msgid "Importer"
msgstr "Importer"

#: templates/wp-admin/settings/tab-addons.php:264
msgid "Tidy things up for clients and remove all adverts for our other products."
msgstr "Tidy things up for clients and remove all adverts for our other products."

#: templates/wp-admin/settings/tab-addons.php:262
#: templates/wp-admin/settings/tab-addons.php:263
msgid "No ads"
msgstr "No ads"

#: templates/wp-admin/settings/tab-addons.php:251
msgid "Sophisticated reporting and emailing capabilities."
msgstr "Sophisticated reporting and emailing capabilities."

#: templates/wp-admin/settings/tab-addons.php:223
#: templates/wp-admin/settings/tab-addons.php:224
msgid "More database options"
msgstr "More database options"

#: templates/wp-admin/settings/tab-addons.php:199
msgid "Set exact times to create or delete backups."
msgstr "Set exact times to create or delete backups."

#: templates/wp-admin/settings/tab-addons.php:197
#: templates/wp-admin/settings/tab-addons.php:198
msgid "Backup time and scheduling"
msgstr "Backup time and scheduling"

#: templates/wp-admin/settings/tab-addons.php:185
msgid "Network / multisite"
msgstr "Network / multisite"

#: templates/wp-admin/settings/tab-addons.php:184
msgid "Network and multisite"
msgstr "Network and multisite"

#: templates/wp-admin/settings/tab-addons.php:277
msgid "Some backup plugins can't restore a backup, so Premium allows you to restore backups from other plugins."
msgstr "Some backup plugins can't restore a backup, so Premium allows you to restore backups from other plugins."

#: templates/wp-admin/settings/tab-addons.php:225
msgid "Encrypt your sensitive databases (e.g. customer information or passwords); Backup external databases too."
msgstr "Encrypt your sensitive databases (e.g. customer information or passwords); Backup external databases too."

#: templates/wp-admin/settings/tab-addons.php:186
msgid "Backup WordPress multisites (i.e, networks), securely."
msgstr "Backup WordPress multisites (i.e, networks), securely."

#: templates/wp-admin/settings/tab-addons.php:173
msgid "Backup WordPress core and non-WP files and databases."
msgstr "Backup WordPress core and non-WP files and databases."

#: templates/wp-admin/settings/tab-addons.php:160
msgid "Automatically backs up your website before any updates to plugins, themes and WordPress core."
msgstr "Automatically backs up your website before any updates to plugins, themes and WordPress core."

#: templates/wp-admin/settings/tab-addons.php:158
#: templates/wp-admin/settings/tab-addons.php:159
msgid "Pre-update backups"
msgstr "Pre-update backups"

#: templates/wp-admin/settings/tab-addons.php:147
msgid "Provides expert help and support from the developers whenever you need it."
msgstr "Provides expert help and support from the developers whenever you need it."

#: templates/wp-admin/settings/tab-addons.php:146
msgid "Fast, personal support"
msgstr "Fast, personal support"

#: templates/wp-admin/settings/tab-addons.php:108
msgid "UpdraftPlus Migrator clones your WordPress site and moves it to a new domain directly and simply."
msgstr "UpdraftPlus Migrator clones your WordPress site and moves it to a new domain directly and simply."

#: templates/wp-admin/settings/tab-addons.php:107
msgid "Cloning and migration"
msgstr "Cloning and migration"

#: templates/wp-admin/settings/tab-addons.php:106
msgid "Migrator"
msgstr "Migrator"

#: templates/wp-admin/settings/tab-addons.php:237
msgid "Additional and enhanced remote storage locations"
msgstr "Additional and enhanced remote storage locations"

#: templates/wp-admin/settings/tab-addons.php:236
msgid "Additional storage"
msgstr "Additional storage"

#: includes/updraftplus-tour.php:129 includes/updraftplus-tour.php:181
#: templates/wp-admin/settings/tab-addons.php:93
msgid "Remote storage"
msgstr "Remote storage"

#: templates/wp-admin/settings/tab-addons.php:88
#: templates/wp-admin/settings/tab-addons.php:322
msgid "Upgrade now"
msgstr "Upgrade now"

#: templates/wp-admin/settings/tab-addons.php:85
#: templates/wp-admin/settings/tab-addons.php:319
msgid "Installed"
msgstr "Installed"

#: templates/wp-admin/settings/tab-addons.php:75
msgid "Free"
msgstr "Free"

#: admin.php:605
msgid "UpdraftPlus"
msgstr "UpdraftPlus"

#: templates/wp-admin/settings/form-contents.php:224
msgid "Recommended: optimize your database with WP-Optimize."
msgstr "Recommended: optimise your database with WP-Optimize."

#: templates/wp-admin/notices/button-label.php:14
msgid "Read more"
msgstr "Read more"

#: includes/updraftplus-notices.php:190
msgid "After you've backed up your database, we recommend you install our WP-Optimize plugin to streamline it for better website performance."
msgstr "After you've backed up your database, we recommend you install our WP-Optimize plugin to streamline it for better website performance."

#: templates/wp-admin/settings/tab-addons.php:94
msgid "Backup to remote storage locations"
msgstr "Backup to remote storage locations"

#: templates/wp-admin/advanced/wipe-settings.php:12
msgid "UpdraftPlus Tour"
msgstr "UpdraftPlus Tour"

#: central/modules/comments.php:359
msgid "Spam"
msgstr "Spam"

#: central/modules/comments.php:358
msgid "Trash"
msgstr "Bin"

#: central/modules/comments.php:357
msgid "Hold or Unapprove"
msgstr "Hold or Unapprove"

#: central/modules/comments.php:356
msgid "Approve"
msgstr "Approve"

#: central/modules/comments.php:351
msgid "Pings"
msgstr "Pings"

#: central/modules/comments.php:350
msgid "Comments"
msgstr "Comments"

#: templates/wp-admin/notices/autobackup-notice.php:6
#: templates/wp-admin/notices/horizontal-notice.php:8
#: templates/wp-admin/notices/horizontal-notice.php:38
msgid "notice image"
msgstr "notice image"

#: templates/wp-admin/notices/button-label.php:12
msgid "Go there"
msgstr "Go there"

#: templates/wp-admin/notices/button-label.php:8
msgid "Sign up"
msgstr "Sign up"

#: templates/wp-admin/notices/button-label.php:6
msgid "Get Premium"
msgstr "Get Premium"

#: templates/wp-admin/notices/button-label.php:4
msgid "Get UpdraftCentral"
msgstr "Get UpdraftCentral"

#: includes/updraftplus-notices.php:128
msgid "Control all your WordPress installations from one place using UpdraftCentral remote site management!"
msgstr "Control all your WordPress installations from one place using UpdraftCentral remote site management!"

#: includes/updraftplus-notices.php:127
msgid "Do you use UpdraftPlus on multiple sites?"
msgstr "Do you use UpdraftPlus on multiple sites?"

#: includes/updraftplus-notices.php:118
msgid "UpdraftCentral is a highly efficient way to manage, update and backup multiple websites from one place."
msgstr "UpdraftCentral is a highly efficient way to manage, update and backup multiple websites from one place."

#: includes/updraftplus-notices.php:117
msgid "Introducing UpdraftCentral"
msgstr "Introducing UpdraftCentral"

#: templates/wp-admin/settings/tab-addons.php:302
msgid "UpdraftVault storage"
msgstr "UpdraftVault storage"

#: templates/wp-admin/advanced/site-info.php:109
msgid "Apache modules"
msgstr "Apache modules"

#: restorer.php:3080
msgid "Backup of: %s"
msgstr "Backup of: %s"

#: backup.php:2111
msgid "If not, you will need to either remove data from this table, or contact your hosting company to request more resources."
msgstr "If not, you will need to either remove data from this table, or contact your hosting company to request more resources."

#: templates/wp-admin/settings/take-backup.php:85
msgid "You have selected a remote storage option which has an authorization step to complete:"
msgstr "You have selected a remote storage option which has an authorisation step to complete:"

#: templates/wp-admin/settings/take-backup.php:84
msgid "Remote storage authentication"
msgstr "Remote storage authentication"

#: admin.php:2281
msgid "Remote files deleted:"
msgstr "Remote files deleted:"

#: admin.php:2280
msgid "Local files deleted:"
msgstr "Local files deleted:"

#: admin.php:1055
msgid "remote files deleted"
msgstr "remote files deleted"

#: admin.php:104
msgid "template not found"
msgstr "template not found"

#: methods/backup-module.php:637
msgid "Follow this link to authorize access to your %s account (you will not be able to backup to %s without it)."
msgstr "Follow this link to authorise access to your %s account (you will not be able to backup to %s without it)."

#: restorer.php:2650
msgid "Processing table (%s)"
msgstr "Processing table (%s)"

#: methods/dropbox.php:886
msgid "%s de-authentication"
msgstr "%s de-authentication"

#: methods/dropbox.php:610
msgid "You must add the following as the authorised redirect URI in your Dropbox console (under \"API Settings\") when asked"
msgstr "You must add the following as the authorised redirect URI in your Dropbox console (under \"API Settings\") when asked"

#: templates/wp-admin/advanced/lock-admin.php:8
msgid "Lock access to the UpdraftPlus settings page"
msgstr "Lock access to the UpdraftPlus settings page"

#: templates/wp-admin/advanced/tools-menu.php:24
msgid "Site size"
msgstr "Site size"

#: includes/migrator-lite.php:244 includes/migrator-lite.php:291
#: templates/wp-admin/advanced/search-replace.php:7
#: templates/wp-admin/advanced/tools-menu.php:20
msgid "Search / replace database"
msgstr "Search / replace database"

#: includes/updraftplus-notices.php:121 includes/updraftplus-notices.php:131
#: includes/updraftplus-tour.php:151
msgid "UpdraftCentral"
msgstr "UpdraftCentral"

#: templates/wp-admin/advanced/tools-menu.php:12
#: templates/wp-admin/settings/tab-addons.php:288
#: templates/wp-admin/settings/tab-addons.php:289
msgid "Lock settings"
msgstr "Lock settings"

#: templates/wp-admin/advanced/site-info.php:5
#: templates/wp-admin/advanced/tools-menu.php:8
msgid "Site information"
msgstr "Site information"

#: admin.php:1051
msgid "Complete"
msgstr "Complete"

#. translators: %s: The name of the missing add-on.
#: includes/class-commands.php:445
msgid "%s add-on not found"
msgstr "%s add-on not found"

#: templates/wp-admin/advanced/export-settings.php:15
msgid "Import settings"
msgstr "Import settings"

#: templates/wp-admin/advanced/export-settings.php:9
msgid "Export settings"
msgstr "Export settings"

#: templates/wp-admin/advanced/export-settings.php:7
msgid "including any passwords"
msgstr "including any passwords"

#: templates/wp-admin/advanced/export-settings.php:5
#: templates/wp-admin/advanced/tools-menu.php:36
msgid "Export / import settings"
msgstr "Export / import settings"

#: templates/wp-admin/settings/delete-and-restore-modals.php:66
msgid "or to restore manually"
msgstr "or to restore manually"

#: admin.php:3057
msgid "To fix this problem go here."
msgstr "To fix this problem go here."

#: admin.php:1050
msgid "Do you want to carry out the import?"
msgstr "Do you want to carry out the import?"

#: admin.php:1049
msgid "Which was exported on:"
msgstr "Which was exported on:"

#: admin.php:1048
msgid "This will import data from:"
msgstr "This will import data from:"

#: admin.php:1047
msgid "Importing..."
msgstr "Importing..."

#: admin.php:1043
msgid "You have not yet selected a file to import."
msgstr "You have not yet selected a file to import."

#: admin.php:1026
msgid "Your export file will be of your displayed settings, not your saved ones."
msgstr "Your export file will be of your displayed settings, not your saved ones."

#: admin.php:1010 central/translations-central.php:84
msgid "your PHP install lacks the openssl module; as a result, this can take minutes; if nothing has happened by then, then you should either try a smaller key size, or ask your web hosting company how to enable this PHP module on your setup."
msgstr "your PHP install lacks the openssl module; as a result, this can take minutes. If nothing has happened by then, you should either try a smaller key size, or ask your web hosting company how to enable this PHP module on your setup."

#: admin.php:3057
msgid "OptimizePress 2.0 encodes its contents, so search/replace does not work."
msgstr "OptimizePress 2.0 encodes its contents, so search/replace does not work."

#: methods/s3.php:1457
msgid "The AWS access key looks to be wrong (valid %s access keys begin with \"AK\")"
msgstr "The AWS access key looks to be wrong (valid %s access keys begin with \"AK\")"

#. translators: %s: Remote storage
#: templates/wp-admin/settings/form-contents.php:117
msgid "Backup using %s?"
msgstr "Backup using %s?"

#: methods/s3.php:155
msgid "No settings were found - please go to the Settings tab and check your settings"
msgstr "No settings were found - please go to the Settings tab and check your settings"

#: central/translations-central.php:57
msgid "This is useful if the dashboard webserver cannot be contacted with incoming traffic by this website (for example, this is the case if this website is hosted on the public Internet, but the UpdraftCentral dashboard is on localhost, or on an Intranet, or if this website has an outgoing firewall), or if the dashboard website does not have a SSL certificate."
msgstr "This is useful if the dashboard webserver cannot be contacted with incoming traffic by this website (for example, this is the case if this website is hosted on the public Internet, but the UpdraftCentral dashboard is on localhost, or on an Intranet, or if this website has an outgoing firewall), or if the dashboard website does not have an SSL certificate."

#: central/translations-central.php:56
msgid "More information..."
msgstr "More information..."

#: central/translations-central.php:55
msgid "Use the alternative method for making a connection with the dashboard."
msgstr "Use the alternative method for making a connection with the dashboard."

#: backup.php:3158
msgid "Failed to open directory (check the file permissions and ownership): %s"
msgstr "Failed to open directory (check the file permissions and ownership): %s"

#: central/translations-central.php:53
msgid "recommended"
msgstr "recommended"

#: central/translations-central.php:54
msgid "slower, strongest"
msgstr "slower, strongest"

#: central/translations-central.php:50
msgid "%s bytes"
msgstr "%s bytes"

#: central/translations-central.php:52
msgid "faster (possibility for slow PHP installs)"
msgstr "faster (possibility for slow PHP installs)"

#: central/translations-central.php:51
msgid "easy to break, fastest"
msgstr "easy to break, fastest"

#: central/translations-central.php:49
msgid "%s bits"
msgstr "%s bits"

#: central/translations-central.php:48
msgid "Encryption key size:"
msgstr "Encryption key size:"

#: central/translations-central.php:32
msgid "Key size: %d bits"
msgstr "Key size: %d bits"

#: central/translations-central.php:30
msgid "Public key was sent to:"
msgstr "Public key was sent to:"

#: methods/ftp.php:438
msgid "login"
msgstr "Login"

#: central/translations-central.php:61
msgid "UpdraftCentral (Remote Control)"
msgstr "UpdraftCentral (Remote Control)"

#: central/translations-central.php:60
msgid "View recent UpdraftCentral log events"
msgstr "View recent UpdraftCentral log events"

#: methods/ftp.php:466
msgid "This is sometimes caused by a firewall - try turning off SSL in the expert settings, and testing again."
msgstr "This is sometimes caused by a firewall - try turning off SSL in the expert settings, and testing again."

#. translators: %s: Approximate email size limit in MB
#: methods/email.php:125
msgid "Be aware that mail servers tend to have size limits; typically around %s MB; backups larger than any limits will likely not arrive."
msgstr "Be aware that mail servers tend to have size limits; typically around %s MB; backups larger than any limits will likely not arrive."

#: central/translations-central.php:47
msgid "Enter any description"
msgstr "Enter any description"

#: central/translations-central.php:46
msgid "Description"
msgstr "Description"

#: central/translations-central.php:33
msgid "Delete..."
msgstr "Delete..."

#: central/translations-central.php:31
msgid "Created:"
msgstr "Created:"

#: central/translations-central.php:29
msgid "Access this site as user:"
msgstr "Access this site as user:"

#: central/translations-central.php:36
msgid "Details"
msgstr "Details"

#: central/translations-central.php:35
msgid "Key description"
msgstr "Key description"

#: central/translations-central.php:18
msgid "An invalid URL was entered"
msgstr "An invalid URL was entered"

#: central/translations-central.php:15
msgid "This connection appears to already have been made."
msgstr "This connection appears to already have been made."

#: central/translations-central.php:14
msgid "You must visit this link in the same browser and login session as you created the key in."
msgstr "You must visit this link in the same browser and login session as you created the key in."

#: central/translations-central.php:12
msgid "You must visit this URL in the same browser and login session as you created the key in."
msgstr "You must visit this URL in the same browser and login session as you created the key in."

#: central/translations-central.php:11
msgid "You are not logged into this WordPress site in your web browser."
msgstr "You are not logged into this WordPress site in your web browser."

#: central/translations-central.php:10
msgid "The key referred to was unknown."
msgstr "The key referred to was unknown."

#: central/translations-central.php:9
msgid "A new UpdraftCentral connection has not been made."
msgstr "A new UpdraftCentral connection has not been made."

#: central/translations-central.php:8
msgid "An UpdraftCentral connection has been made successfully."
msgstr "An UpdraftCentral connection has been made successfully."

#: central/translations-central.php:7
msgid "UpdraftCentral Connection"
msgstr "UpdraftCentral Connection"

#: methods/cloudfiles.php:518
msgid "Cloud Files"
msgstr "Cloud Files"

#: templates/wp-admin/settings/form-contents.php:105
msgid "(tap on an icon to select or unselect)"
msgstr "(tap on an icon to select or unselect)"

#: class-updraftplus.php:675 class-updraftplus.php:757
msgid "The given file was not found, or could not be read."
msgstr "The given file was not found, or could not be read."

#: admin.php:1025
msgid "You should save your changes to ensure that they are used for making your backup."
msgstr "You should save your changes to ensure that they are used for making your backup."

#: admin.php:1017 central/translations-central.php:83
msgid "Please enter a valid URL"
msgstr "Please enter a valid URL"

#: admin.php:950 central/translations-central.php:81
msgid "Fetching..."
msgstr "Fetching..."

#: admin.php:5770
msgid "Your settings have been saved."
msgstr "Your settings have been saved."

#: admin.php:4562
msgid "Total backup size:"
msgstr "Total backup size:"

#. translators: %s: Item to be removed
#: templates/wp-admin/settings/delete-and-restore-modals.php:19
msgid "these backup sets"
msgstr "these backup sets"

#. translators: %s: Item to be removed
#: templates/wp-admin/settings/delete-and-restore-modals.php:13
msgid "this backup set"
msgstr "this backup set"

#: admin.php:1018
msgid "We requested to delete the file, but could not understand the server's response"
msgstr "We requested to delete the file, but could not understand the server's response"

#: admin.php:1000
msgid "Saving..."
msgstr "Saving..."

#: methods/updraftvault.php:519 methods/updraftvault.php:524
#: methods/updraftvault.php:525 methods/updraftvault.php:526
msgid "%s per year"
msgstr "%s per year"

#: methods/updraftvault.php:517
msgid "or (annual discount)"
msgstr "or (annual discount)"

#: methods/updraftvault.php:336
msgid "No Vault connection was found for this site (has it moved?); please disconnect and re-connect."
msgstr "No Vault connection was found for this site (has it moved?); please disconnect and re-connect."

#: class-updraftplus.php:3687 class-updraftplus.php:3799
msgid "The backup was aborted by the user"
msgstr "The backup was aborted by the user"

#: admin.php:3925
msgid "stop"
msgstr "stop"

#: admin.php:1052 admin.php:3711
msgid "The backup has finished running"
msgstr "The backup has finished running"

#: templates/wp-admin/advanced/site-info.php:120
msgid "reset"
msgstr "reset"

#: includes/class-filesystem-functions.php:112
msgid "calculate"
msgstr "calculate"

#: restorer.php:3103
msgid "Uploads URL:"
msgstr "Uploads URL:"

#: class-updraftplus.php:5259 restorer.php:3121
msgid "To import an ordinary WordPress site into a multisite installation requires %s."
msgstr "To import an ordinary WordPress site into a multisite installation requires %s."

#: class-updraftplus.php:5255
msgid "Please read this link for important information on this process."
msgstr "Please read this link for important information on this process."

#: class-updraftplus.php:5255
msgid "It will be imported as a new site."
msgstr "It will be imported as a new site."

#: templates/wp-admin/advanced/site-info.php:118
msgid "Call WordPress action:"
msgstr "Call WordPress action:"

#: admin.php:3342 templates/wp-admin/notices/autobackup-notice.php:16
#: templates/wp-admin/notices/autobackup-notice.php:18
#: templates/wp-admin/notices/horizontal-notice.php:48
#: templates/wp-admin/notices/horizontal-notice.php:50
msgid "Dismiss"
msgstr "Dismiss"

#: admin.php:1038
msgid "Please fill in the required information."
msgstr "Please fill in the required information."

#: class-updraftplus.php:2072
msgid "(when decrypted)"
msgstr "(when decrypted)"

#: class-updraftplus.php:2068 class-updraftplus.php:2073
msgid "%s checksum: %s"
msgstr "%s checksum: %s"

#: class-updraftplus.php:2063
msgid "Size: %s MB"
msgstr "Size: %s MB"

#: class-updraftplus.php:2060
msgid "External database (%s)"
msgstr "External database (%s)"

#: class-updraftplus.php:2000 class-updraftplus.php:2002
msgid "files: %s"
msgstr "files: %s"

#: restorer.php:578
msgid "Skipping: this archive was already restored."
msgstr "Skipping: this archive was already restored."

#: admin.php:4545
msgid "Uploaded to:"
msgstr "Uploaded to:"

#: templates/wp-admin/settings/form-contents.php:143
msgid "File Options"
msgstr "File Options"

#: templates/wp-admin/settings/form-contents.php:58
msgid "Database backup schedule"
msgstr "Database backup schedule"

#: templates/wp-admin/settings/form-contents.php:22
msgid "Files backup schedule"
msgstr "Files backup schedule"

#: templates/wp-admin/advanced/wipe-settings.php:6
msgid "This button will delete all UpdraftPlus settings and progress information for in-progress backups (but not any of your existing backups from your cloud storage)."
msgstr "This button will delete all UpdraftPlus settings and progress information for in-progress backups (but not any of your existing backups from your cloud storage)."

#: admin.php:5633
msgid "Not got any remote storage?"
msgstr "Not got any remote storage?"

#: admin.php:794
msgid "Extensions"
msgstr "Extensions"

#: admin.php:5633
msgid "settings"
msgstr "Settings"

#. translators: %s: "settings" which is the name of a tab on which remote
#. storage settings are configured
#: admin.php:5632
msgid "Backup won't be sent to any remote storage - none has been saved in the %s"
msgstr "Backup won't be sent to any remote storage - none have been saved in the %s"

#: templates/wp-admin/settings/backupnow-modal.php:51
msgid "Your saved settings also affect what is backed up - e.g. files excluded."
msgstr "Your saved settings also affect what is backed up - e.g. files excluded."

#: admin.php:3341
msgid "Continue restoration"
msgstr "Continue restoration"

#: admin.php:3335
msgid "You have an unfinished restoration operation, begun %s ago."
msgstr "You have an unfinished restoration operation, begun %s ago."

#: admin.php:3334
msgid "Unfinished restoration"
msgstr "Unfinished restoration"

#: admin.php:3331
msgid "%s minutes, %s seconds"
msgstr "%s minutes, %s seconds"

#: admin.php:3159
msgid "Backup Contents And Schedule"
msgstr "Backup Contents And Schedule"

#: admin.php:3262
msgid "Premium / Extensions"
msgstr "Premium / Extensions"

#: admin.php:5117 admin.php:5126
msgid "Sufficient information about the in-progress restoration operation could not be found."
msgstr "Sufficient information about the in-progress restoration operation could not be found."

#: admin.php:1023
msgctxt "(verb)"
msgid "Download"
msgstr "Download"

#: admin.php:786 admin.php:3261
msgid "Advanced Tools"
msgstr "Advanced Tools"

#: admin.php:942
msgid "You have chosen to backup files, but no file entities have been selected"
msgstr "You have chosen to backup files, but no file entities have been selected"

#: admin.php:1109 templates/wp-admin/settings/backupnow-modal.php:46
msgid "Include your files in the backup"
msgstr "Include your files in the backup"

#: class-updraftplus.php:3706
msgid "To complete your migration/clone, you should now log in to the remote site and restore the backup set."
msgstr "To complete your migration/clone, you should now log in to the remote site and restore the backup set."

#: central/translations-central.php:26
msgid "You must copy and paste this key now - it cannot be shown again."
msgstr "You must copy and paste this key now - it cannot be shown again."

#: central/translations-central.php:25 includes/class-remote-send.php:563
msgid "Key created successfully."
msgstr "Key created successfully."

#: backup.php:1655
msgid "The backup directory is not writable (or disk space is full) - the database backup is expected to shortly fail."
msgstr "The backup directory is not writable (or disk space is full) - the database backup is expected to shortly fail."

#: templates/wp-admin/advanced/site-info.php:74
msgid "required for some remote storage providers"
msgstr "required for some remote storage providers"

#: templates/wp-admin/advanced/site-info.php:74
msgid "Not installed"
msgstr "Not installed"

#: backup.php:1899
msgid "the options table was not found"
msgstr "the options table was not found"

#: backup.php:1897
msgid "no options or sitemeta table was found"
msgstr "no options or sitemeta table was found"

#. translators: %s: Item to be removed
#: templates/wp-admin/settings/delete-and-restore-modals.php:13
#: templates/wp-admin/settings/delete-and-restore-modals.php:19
msgid "Are you sure that you wish to remove %s from UpdraftPlus?"
msgstr "Are you sure that you wish to remove %s from UpdraftPlus?"

#: templates/wp-admin/settings/existing-backups-table.php:165
msgid "Deselect"
msgstr "Deselect"

#: templates/wp-admin/settings/existing-backups-table.php:164
msgid "Select all"
msgstr "Select all"

#: templates/wp-admin/settings/existing-backups-table.php:162
msgid "Actions upon selected backups"
msgstr "Actions upon selected backups"

#: templates/wp-admin/settings/downloading-and-restoring.php:54
#: templates/wp-admin/settings/tab-backups.php:60
msgid "Press here to look inside your remote storage methods for any existing backup sets (from any site, if they are stored in the same folder)."
msgstr "Press here to look inside your remote storage methods for any existing backup sets (from any site, if they are stored in the same folder)."

#: admin.php:2279
msgid "Backup sets removed:"
msgstr "Backup sets removed:"

#: admin.php:1036
msgid "Processing..."
msgstr "Processing..."

#: admin.php:1034
msgid "For backups older than"
msgstr "For backups older than"

#: admin.php:1033
msgid "week(s)"
msgstr "week(s)"

#: admin.php:1032
msgid "hour(s)"
msgstr "hour(s)"

#: admin.php:1031
msgid "day(s)"
msgstr "day(s)"

#: admin.php:1030
msgid "in the month"
msgstr "in the month"

#: admin.php:1029
msgid "day"
msgstr "day"

#. Plugin URI of the plugin
#. Author URI of the plugin
#: updraftplus.php
msgid "https://updraftplus.com"
msgstr "https://updraftplus.com"

#. Plugin Name of the plugin
#: updraftplus.php
msgid "UpdraftPlus - Backup/Restore"
msgstr "UpdraftPlus - Backup/Restore"

#: restorer.php:4234 restorer.php:4347
msgid "Table prefix has changed: changing %s table field(s) accordingly:"
msgstr "Table prefix has changed: changing %s table field(s) accordingly:"

#: restorer.php:4101
msgid "Too many database errors have occurred - aborting"
msgstr "Too many database errors have occurred - aborting"

#: restorer.php:4089
msgid "To use this backup, your database server needs to support the %s character set."
msgstr "To use this backup, your database server needs to support the %s character set."

#: restorer.php:4087
msgid "This database needs to be deployed on MySQL version %s or later."
msgstr "This database needs to be deployed on MySQL version %s or later."

#: restorer.php:4087
msgid "This problem is caused by trying to restore a database on a very old MySQL version that is incompatible with the source database."
msgstr "This problem is caused by trying to restore a database on a very old MySQL version that is incompatible with the source database."

#: includes/class-search-replace.php:519 restorer.php:4042
msgid "the database query being run was:"
msgstr "the database query being run was:"

#: restorer.php:4042
msgctxt "The user is being told the number of times an error has happened, e.g. An error (27) occurred"
msgid "An error (%s) occurred:"
msgstr "An error (%s) occurred:"

#: restorer.php:3873
msgid "An SQL line that is larger than the maximum packet size and cannot be split was found; this line will not be processed, but will be dropped: %s"
msgstr "An SQL line that is larger than the maximum packet size and cannot be split was found; this line will not be processed, but will be dropped: %s"

#: restorer.php:2654
msgid "will restore as:"
msgstr "will restore as:"

#: restorer.php:2575
msgid "Requested table engine (%s) is not present - changing to MyISAM."
msgstr "Requested table engine (%s) is not present - changing to MyISAM."

#: restorer.php:3260 restorer.php:3999 restorer.php:4072 restorer.php:4089
msgid "An error occurred on the first %s command - aborting run"
msgstr "An error occurred on the first %s command - aborting run"

#: restorer.php:3229
msgid "Split line to avoid exceeding maximum packet size"
msgstr "Split line to avoid exceeding maximum packet size"

#: restorer.php:2483 restorer.php:3110 restorer.php:3290
msgid "Old table prefix:"
msgstr "Old table prefix:"

#: restorer.php:3098
msgid "Content URL:"
msgstr "Content URL:"

#: restorer.php:3092
msgid "Site home:"
msgstr "Site home:"

#: restorer.php:3087
msgid "Backup created by:"
msgstr "Backup created by:"

#: restorer.php:2853
msgid "Failed to open database file"
msgstr "Failed to open database file"

#: restorer.php:2832
msgid "Failed to find database file"
msgstr "Failed to find database file"

#: restorer.php:2370
msgid "Please supply the requested information, and then continue."
msgstr "Please supply the requested information, and then continue."

#: class-updraftplus.php:5205 restorer.php:1910
msgid "You should enable %s to make any pretty permalinks (e.g. %s) work"
msgstr "You should enable %s to make any pretty permalinks (e.g. %s) work"

#. Description of the plugin
#: updraftplus.php
msgid "Backup and restore: take backups locally, or backup to Amazon S3, Dropbox, Google Drive, Rackspace, (S)FTP, WebDAV & email, on automatic schedules."
msgstr "Backup and restore: take backups locally, or backup to Amazon S3, Dropbox, Google Drive, Rackspace, (S)FTP, WebDAV & email, on automatic schedules."

#: admin.php:1125
msgid "Restoring table: %s"
msgstr "Restoring table: %s"

#: methods/googledrive.php:1521
msgid "Client ID"
msgstr "Client ID"

#: admin.php:1121 admin.php:3431 methods/openstack2.php:257
#: methods/updraftvault.php:543
#: templates/wp-admin/settings/updraftcentral-connect.php:50
msgid "Password"
msgstr "Password"

#: methods/googledrive.php:1523
msgid "Client Secret"
msgstr "Client Secret"

#: methods/googledrive.php:1525
msgid "Folder"
msgstr "Folder"

#: restorer.php:1581
msgid "file"
msgstr "file"

#: methods/ftp.php:442 methods/openstack2.php:122
msgid "password"
msgstr "password"

#: class-updraftplus.php:5205 restorer.php:1910
msgid "You are using the %s webserver, but do not seem to have the %s module loaded."
msgstr "You are using the %s webserver, but do not seem to have the %s module loaded."

#: restorer.php:1863
msgid "Files found:"
msgstr "Files found:"

#: restorer.php:1722 restorer.php:1771
msgid "The WordPress content folder (wp-content) was not found in this zip file."
msgstr "The WordPress content folder (wp-content) was not found in this zip file."

#: restorer.php:1605
msgid "This version of UpdraftPlus does not know how to handle this type of foreign backup"
msgstr "This version of UpdraftPlus does not know how to handle this type of foreign backup"

#: restorer.php:1573
msgid "folder"
msgstr "folder"

#: restorer.php:1573 restorer.php:1581
msgid "UpdraftPlus needed to create a %s in your content directory, but failed - please check your file permissions and enable the access (%s)"
msgstr "UpdraftPlus needed to create a %s in your content directory, but failed - please check your file permissions and enable the access (%s)"

#: restorer.php:1283
msgid "The directory does not exist"
msgstr "The directory does not exist"

#: restorer.php:1196
msgid "Failed to write out the decrypted database to the filesystem"
msgstr "Failed to write out the decrypted database to the filesystem"

#: restorer.php:1180
msgid "Failed to create a temporary directory"
msgstr "Failed to create a temporary directory"

#: restorer.php:895
msgid "Failed to unpack the archive"
msgstr "Failed to unpack the archive"

#: restorer.php:893
msgid "Failed to delete working directory after restoring."
msgstr "Failed to delete working directory after restoring."

#: restorer.php:889
msgid "You should check the file ownerships and permissions in your WordPress installation"
msgstr "You should check the file ownerships and permissions in your WordPress installation"

#: restorer.php:889
msgid "Could not move old files out of the way."
msgstr "Could not move old files out of the way."

#: restorer.php:888
msgid "Cleaning up rubbish..."
msgstr "Cleaning up rubbish..."

#: restorer.php:887
msgid "Restoring the database (on a large site this can take a long time - if it times out (which can happen if your web hosting company has configured your hosting to limit resources) then you should use a different method, such as phpMyAdmin)..."
msgstr "Restoring the database (on a large site this can take a long time - if it times out (which can happen if your web hosting company has configured your hosting to limit resources) then you should use a different method, such as phpMyAdmin)..."

#: restorer.php:886
msgid "Moving unpacked backup into place..."
msgstr "Moving unpacked backup into place..."

#: restorer.php:885
msgid "Moving old data out of the way..."
msgstr "Moving old data out of the way..."

#: restorer.php:884
msgid "Database successfully decrypted."
msgstr "Database successfully decrypted."

#: restorer.php:883
msgid "Decrypting database (can take a while)..."
msgstr "Decrypting database (can take a while)..."

#: restorer.php:882
msgid "Unpacking backup..."
msgstr "Unpacking backup..."

#: restorer.php:881
msgid "Copying this entity failed."
msgstr "Copying this entity failed."

#: restorer.php:880
msgid "Backup file not available."
msgstr "Backup file not available."

#: options.php:282
msgid "(This applies to all WordPress backup plugins unless they have been explicitly coded for multisite compatibility)."
msgstr "(This applies to all WordPress backup plugins unless they have been explicitly coded for multisite compatibility)."

#: options.php:282
msgid "This is a WordPress multi-site (a.k.a. network) installation."
msgstr "This is a WordPress multi-site (a.k.a. network) installation."

#: options.php:282
msgid "UpdraftPlus warning:"
msgstr "UpdraftPlus warning:"

#: options.php:141
msgid "UpdraftPlus Backups"
msgstr "UpdraftPlus Backups"

#: methods/addon-not-yet-present.php:34 methods/addon-not-yet-present.php:76
#: methods/addon-not-yet-present.php:83
msgid "You do not have the UpdraftPlus %s add-on installed - get it from %s"
msgstr "You do not have the UpdraftPlus %s add-on installed - get it from %s"

#: methods/updraftvault.php:957
msgid "Your email address and password were not recognised by UpdraftPlus.Com"
msgstr "Your email address and password were not recognised by UpdraftPlus.Com"

#: methods/updraftvault.php:954
msgid "You entered an email address that was not recognised by UpdraftPlus.Com"
msgstr "You entered an email address that was not recognised by UpdraftPlus.Com"

#: methods/updraftvault.php:951
msgid "If you have forgotten your password, then go here to change your password on updraftplus.com."
msgstr "If you have forgotten your password, then go here to change your password on updraftplus.com."

#: methods/updraftvault.php:951
msgid "Your email address was valid, but your password was not recognised by UpdraftPlus.Com."
msgstr "Your email address was valid, but your password was not recognised by UpdraftPlus.Com."

#: methods/updraftvault.php:945 methods/updraftvault.php:968
#: methods/updraftvault.php:971
msgid "UpdraftPlus.Com returned a response, but we could not understand it"
msgstr "UpdraftPlus.Com returned a response, but we could not understand it"

#: includes/updraftplus-login.php:57 methods/updraftvault.php:918
msgid "UpdraftPlus.Com returned a response which we could not understand (data: %s)"
msgstr "UpdraftPlus.Com returned a response which we could not understand (data: %s)"

#: includes/updraftplus-login.php:55 methods/updraftvault.php:916
msgid "To remove the block, please go here."
msgstr "To remove the block, please go here."

#: includes/updraftplus-login.php:55 methods/updraftvault.php:916
msgid "This most likely means that you share a webserver with a hacked website that has been used in previous attacks."
msgstr "This most likely means that you share a webserver with a hacked website that has been used in previous attacks."

#: includes/updraftplus-login.php:55 methods/updraftvault.php:916
msgid "It appears that your web server's IP Address (%s) is blocked."
msgstr "It appears that your web server's IP Address (%s) is blocked."

#: includes/updraftplus-login.php:55 methods/updraftvault.php:916
msgid "UpdraftPlus.com has responded with 'Access Denied'."
msgstr "UpdraftPlus.com has responded with 'Access Denied'."

#: methods/updraftvault.php:891
msgid "You need to supply both an email address and a password"
msgstr "You need to supply both an email address and a password"

#: includes/class-commands.php:909 methods/updraftvault.php:865
msgid "An unknown error occurred when trying to connect to UpdraftPlus.Com"
msgstr "An unknown error occurred when trying to connect to UpdraftPlus.Com"

#: methods/updraftvault.php:673 methods/updraftvault.php:776
msgid "Refresh current status"
msgstr "Refresh current status"

#: methods/updraftvault.php:671 methods/updraftvault.php:688
#: methods/updraftvault.php:690 methods/updraftvault.php:776
msgid "Get more quota"
msgstr "Get more quota"

#: methods/updraftvault.php:668 methods/updraftvault.php:685
#: methods/updraftvault.php:740
msgid "Current use:"
msgstr "Current use:"

#: methods/updraftvault.php:653
msgid "You can get more quota here"
msgstr "You can get more quota here"

#: methods/updraftvault.php:552 methods/updraftvault.php:622
msgid "Quota:"
msgstr "Quota:"

#: methods/updraftvault.php:551 methods/updraftvault.php:620
msgid "Vault owner"
msgstr "Vault owner"

#: methods/updraftvault.php:550 methods/updraftvault.php:620
msgid "Well done - there's nothing more needed to set up."
msgstr "Well done - there's nothing more needed to set up."

#: methods/updraftvault.php:549
msgid "Go here for help"
msgstr "Go here for help"

#: methods/updraftvault.php:546 methods/updraftvault.php:547
msgid "Don't know your email address, or forgotten your password?"
msgstr "Don't know your email address, or forgotten your password?"

#: methods/updraftvault.php:539
msgid "Enter your UpdraftPlus.Com email / password here to connect:"
msgstr "Enter your UpdraftPlus.Com email / password here to connect:"

#: central/translations-central.php:59 methods/updraftvault.php:535
#: templates/wp-admin/settings/exclude-settings-modal/exclude-panel-heading.php:4
msgid "Back..."
msgstr "Back..."

#: methods/updraftvault.php:520 methods/updraftvault.php:521
#: methods/updraftvault.php:522
msgid "%s per quarter"
msgstr "%s per quarter"

#: central/translations-central.php:67
msgid "Read more about it here."
msgstr "Read more about it here."

#: methods/updraftvault.php:500
msgid "Show the options"
msgstr "Show the options"

#: methods/updraftvault.php:155
msgid "Updraft Vault"
msgstr "Updraft Vault"

#: methods/s3generic.php:80
msgid "S3 (Compatible)"
msgstr "S3 (Compatible)"

#: methods/s3.php:1485
msgid "Delete failed:"
msgstr "Delete failed:"

#: methods/s3.php:1478
msgid "Please check your access credentials."
msgstr "Please check your access credentials."

#: methods/s3.php:1473
msgid "The communication with %s was not encrypted."
msgstr "The communication with %s was not encrypted."

#: methods/s3.php:1471
msgid "The communication with %s was encrypted."
msgstr "The communication with %s was encrypted."

#: methods/s3.php:1468
msgid "We accessed the bucket, and were able to create files within it."
msgstr "We accessed the bucket, and were able to create files within it."

#: methods/s3.php:1466 methods/s3.php:1478
msgid "We successfully accessed the bucket, but the attempt to create a file in it failed."
msgstr "We successfully accessed the bucket, but the attempt to create a file in it failed."

#: methods/s3.php:1466 methods/s3.php:1478
msgid "Failure"
msgstr "Failure"

#: methods/s3.php:1455
msgid "The error reported by %s was:"
msgstr "The error reported by %s was:"

#: methods/s3.php:1409
msgid "Failure: No bucket details were given."
msgstr "Failure: No bucket details were given."

#: methods/s3.php:1387
msgid "API secret"
msgstr "API secret"

#: methods/dreamobjects.php:220 methods/s3.php:1065 methods/s3.php:1105
#: methods/s3generic.php:199
msgid "%s location"
msgstr "%s location"

#: methods/dreamobjects.php:218 methods/s3.php:1061 methods/s3.php:1103
#: methods/s3generic.php:197
msgid "%s secret key"
msgstr "%s secret key"

#: methods/dreamobjects.php:217 methods/s3.php:1057 methods/s3.php:1102
#: methods/s3generic.php:196
msgid "%s access key"
msgstr "%s access key"

#: methods/dreamobjects.php:222 methods/s3generic.php:201
msgid "%s end-point"
msgstr "%s end-point"

#: methods/s3.php:957 methods/s3.php:1101
msgid "Other %s FAQs."
msgstr "Other %s FAQs."

#: methods/dreamobjects.php:214 methods/s3.php:955 methods/s3.php:1100
#: methods/s3generic.php:195
msgid "If you see errors about SSL certificates, then please go here for help."
msgstr "If you see errors about SSL certificates, then please go here for help."

#: methods/s3generic.php:191
msgid "... and many more!"
msgstr "... and many more!"

#: methods/s3generic.php:191
msgid "Examples of S3-compatible storage providers:"
msgstr "Examples of S3-compatible storage providers:"

#: methods/s3.php:532
msgid "%s re-assembly error (%s): (see log file for more)"
msgstr "%s re-assembly error (%s): (see log file for more)"

#: methods/s3.php:462
msgid "%s upload: getting uploadID for multipart upload failed - see log file for more details"
msgstr "%s upload: getting uploadID for multipart upload failed - see log file for more details"

#: methods/s3.php:348
msgid "The required %s PHP module is not installed - ask your web hosting company to enable it"
msgstr "The required %s PHP module is not installed - ask your web hosting company to enable it"

#: methods/s3.php:186 methods/s3.php:198
msgid "%s Error: Failed to initialise"
msgstr "%s Error: Failed to initialise"

#: methods/openstack2.php:127
msgctxt "\"tenant\" is a term used with OpenStack storage - Google for \"OpenStack tenant\" to get more help on its meaning"
msgid "tenant"
msgstr "tenant"

#: methods/openstack2.php:117
msgid "username"
msgstr "username"

#: methods/openstack2.php:259
msgid "Container"
msgstr "Container"

#: methods/openstack2.php:255
msgid "Leave this blank, and a default will be chosen."
msgstr "Leave this blank, and a default will be chosen."

#: methods/openstack2.php:254 methods/s3.php:1447
msgid "Region"
msgstr "Region"

#: methods/openstack2.php:251
msgid "Tenant"
msgstr "Tenant"

#: admin.php:1133 admin.php:6186 methods/openstack2.php:253 restorer.php:396
#: restorer.php:398
#: templates/wp-admin/settings/downloading-and-restoring.php:27
#: templates/wp-admin/settings/tab-backups.php:27
#: templates/wp-admin/settings/updraftcentral-connect.php:14
msgid "Follow this link for more information"
msgstr "Follow this link for more information"

#: methods/openstack2.php:250
msgctxt "Keystone and swauth are technical terms which cannot be translated"
msgid "This needs to be a v2 (Keystone) authentication URI; v1 (Swauth) is not supported."
msgstr "This needs to be a v2 (Keystone) authentication URI; v1 (Swauth) is not supported."

#: methods/openstack2.php:132
msgid "authentication URI"
msgstr "authentication URI"

#: methods/openstack-base.php:531 methods/openstack-base.php:536
msgid "Region: %s"
msgstr "Region: %s"

#: methods/openstack-base.php:530
msgid "%s error - we accessed the container, but failed to create a file within it"
msgstr "%s error - we accessed the container, but failed to create a file within it"

#: methods/openstack-base.php:446
msgid "The %s object was not found"
msgstr "The %s object was not found"

#: methods/openstack-base.php:56 methods/openstack-base.php:369
#: methods/openstack-base.php:438
msgid "Could not access %s container"
msgstr "Could not access %s container"

#: methods/openstack-base.php:48 methods/openstack-base.php:122
#: methods/openstack-base.php:129 methods/openstack-base.php:361
#: methods/openstack-base.php:426
msgid "%s error - failed to access the container"
msgstr "%s error - failed to access the container"

#: methods/addon-not-yet-present.php:126 methods/insufficient.php:128
msgid "You will need to ask your web hosting company to upgrade."
msgstr "You will need to ask your web hosting company to upgrade."

#: methods/addon-not-yet-present.php:27
msgid "This remote storage method (%s) requires PHP %s or later."
msgstr "This remote storage method (%s) requires PHP %s or later."

#: methods/googledrive.php:1540
msgid "<strong>(You appear to be already authenticated,</strong> though you can authenticate again to refresh your access if you've had a problem)."
msgstr "<strong>(You appear to be already authenticated,</strong> though you can authenticate again to refresh your access if you've had a problem)."

#: methods/googledrive.php:1537
msgid "Authenticate with Google"
msgstr "Authenticate with Google"

#: methods/googledrive.php:1534
msgid "To be able to set a custom folder name, use UpdraftPlus Premium."
msgstr "To be able to set a custom folder name, use UpdraftPlus Premium."

#: methods/googledrive.php:1533
msgid "It is an ID number internal to Google Drive"
msgstr "It is an ID number internal to Google Drive"

#: methods/googledrive.php:1533
msgid "<strong>This is NOT a folder name</strong>."
msgstr "<strong>This is NOT a folder name</strong>."

#: methods/googledrive.php:1522
msgid "If Google later shows you the message \"invalid_client\", then you did not enter a valid client ID here."
msgstr "If Google later shows you the message \"invalid_client\", then you did not enter a valid client ID here."

#: methods/googledrive.php:1532
msgid "N.B. If you install UpdraftPlus on several WordPress sites, then you cannot re-use your project; you must create a new one from your Google API console for each site."
msgstr "N.B. If you install UpdraftPlus on several WordPress sites, then you cannot re-use your project; you must create a new one from your Google API console for each site."

#: methods/googledrive.php:1530
msgid "You must add the following as the authorised redirect URI (under \"More Options\") when asked"
msgstr "You must add the following as the authorised redirect URI (under \"More Options\") when asked"

#: methods/googledrive.php:1529
msgid "Select 'Web Application' as the application type."
msgstr "Select 'Web Application' as the application type."

#: methods/googledrive.php:1528
msgid "Follow this link to your Google API Console, and there activate the Drive API and create a Client ID in the API Access section."
msgstr "Follow this link to your Google API Console, and there activate the Drive API and create a Client ID in the API Access section."

#: methods/googledrive.php:871 methods/googledrive.php:907
msgid "Have not yet obtained an access token from Google - you need to authorise or re-authorise your connection to Google Drive."
msgstr "Have not yet obtained an access token from Google - you need to authorise or re-authorise your connection to Google Drive."

#: methods/googledrive.php:824 methods/googledrive.php:825
#: methods/googledrive.php:835 methods/googledrive.php:836
msgid "Account is not authorized."
msgstr "Account is not authorised."

#: methods/googledrive.php:769
msgid "Upload expected to fail: the %s limit for any single file is %s, whereas this file is %s GB (%d bytes)"
msgstr "Upload expected to fail: the %s limit for any single file is %s, whereas this file is %s GB (%d bytes)"

#: methods/googledrive.php:762
msgid "Account full: your %s account has only %d bytes left, but the file to be uploaded is %d bytes"
msgstr "Account full: your %s account has only %d bytes left, but the file to be uploaded is %d bytes"

#: methods/googledrive.php:719
msgid "failed to access parent folder"
msgstr "failed to access parent folder"

#: methods/googledrive.php:719 methods/googledrive.php:781
#: methods/googledrive.php:797 methods/googledrive.php:799
msgid "Failed to upload to %s"
msgstr "Failed to upload to %s"

#: methods/googledrive.php:676
msgid "Name: %s."
msgstr "Name: %s."

#: methods/googledrive.php:676
msgid "you have authenticated your %s account."
msgstr "you have authenticated your %s account."

#: methods/googledrive.php:640 methods/googledrive.php:653
msgid "However, subsequent access attempts failed:"
msgstr "However, subsequent access attempts failed:"

#: methods/googledrive.php:339
msgid "Google Drive list files: failed to access parent folder"
msgstr "Google Drive list files: failed to access parent folder"

#: methods/googledrive.php:322 methods/googledrive.php:324
#: methods/googledrive.php:676 methods/googledrive.php:719
#: methods/googledrive.php:762 methods/googledrive.php:769
#: methods/googledrive.php:781 methods/googledrive.php:797
#: methods/googledrive.php:799 methods/googledrive.php:1520
#: methods/googledrive.php:1521 methods/googledrive.php:1523
#: methods/googledrive.php:1525 methods/googledrive.php:1547
msgid "Google Drive"
msgstr "Google Drive"

#: methods/ftp.php:464
msgid "Failure: we successfully logged in, but were not able to create a file in the given directory."
msgstr "Failure: we successfully logged in, but were not able to create a file in the given directory."

#: methods/ftp.php:461
msgid "Success: we successfully logged in, and confirmed our ability to create a file in the given directory (login type:"
msgstr "Success: we successfully logged in, and confirmed our ability to create a file in the given directory (login type:"

#: methods/ftp.php:452
msgid "Failure: we did not successfully log in with those credentials."
msgstr "Failure: we did not successfully log in with those credentials."

#: methods/ftp.php:434
msgid "Failure: No server details were given."
msgstr "Failure: No server details were given."

#: methods/ftp.php:129
msgid "Almost all FTP servers will want passive mode; but if you need active mode, then uncheck this."
msgstr "Almost all FTP servers will want passive mode; but if you need active mode, then uncheck this."

#: methods/ftp.php:128
msgid "Passive mode"
msgstr "Passive mode"

#: methods/ftp.php:127
msgid "Needs to already exist"
msgstr "Needs to already exist"

#: methods/ftp.php:126
msgid "Remote path"
msgstr "Remote path"

#: methods/ftp.php:124
msgid "FTP password"
msgstr "FTP password"

#: methods/ftp.php:123
msgid "FTP login"
msgstr "FTP login"

#: methods/ftp.php:122
msgid "FTP server"
msgstr "FTP server"

#: methods/ftp.php:120
msgid "Only non-encrypted FTP is supported by regular UpdraftPlus."
msgstr "Only non-encrypted FTP is supported by regular UpdraftPlus."

#: methods/ftp.php:113
msgid "encrypted FTP (explicit encryption)"
msgstr "encrypted FTP (explicit encryption)"

#: methods/ftp.php:112
msgid "encrypted FTP (implicit encryption)"
msgstr "encrypted FTP (implicit encryption)"

#: methods/ftp.php:111
msgid "regular non-encrypted FTP"
msgstr "regular non-encrypted FTP"

#: admin.php:1040
msgid "Testing %s Settings..."
msgstr "Testing %s Settings..."

#: methods/ftp.php:208
msgid "%s login failure"
msgstr "%s login failure"

#: methods/addon-base-v2.php:81 methods/addon-base-v2.php:129
#: methods/addon-base-v2.php:170 methods/addon-base-v2.php:229
#: methods/addon-base-v2.php:318 methods/ftp.php:42 methods/googledrive.php:322
#: methods/googledrive.php:324
msgid "No %s settings were found"
msgstr "No %s settings were found"

#. translators: %s: Admin email address
#: methods/email.php:113
msgid "configure it here"
msgstr "configure it here"

#. translators: %s: Admin email address
#: methods/email.php:113
msgid "Your site's admin email address (%s) will be used."
msgstr "Your site's admin email address (%s) will be used."

#: methods/email.php:82
msgid "The attempt to send the backup via email failed (probably the backup was too large for this method)"
msgstr "The attempt to send the backup via email failed (probably the backup was too large for this method)"

#. translators: %s: Site URL and description type
#: methods/email.php:59
msgid "Backup is of: %s."
msgstr "Backup is of: %s."

#: methods/email.php:47
msgid "WordPress Backup"
msgstr "WordPress Backup"

#: options.php:282
msgid "Without upgrading, UpdraftPlus allows <strong>every</strong> blog admin who can modify plugin settings to backup (and hence access the data, including passwords, from) and restore (including with customized modifications, e.g. changed passwords) <strong>the entire network</strong>."
msgstr "Without upgrading, UpdraftPlus allows <strong>every</strong> blog admin who can modify plugin settings to backup (and hence access the data, including passwords, from) and restore (including with customised modifications, e.g. changed passwords) <strong>the entire network</strong>."

#: methods/s3.php:528
msgid "upload (%s): re-assembly failed (see log for more details)"
msgstr "upload (%s): re-assembly failed (see log for more details)"

#: methods/s3.php:512
msgid "chunk %s: upload failed"
msgstr "chunk %s: upload failed"

#: methods/s3.php:484
msgid "error: file %s was shortened unexpectedly"
msgstr "error: file %s was shortened unexpectedly"

#: methods/googledrive.php:1491
msgid "download: failed: file not found"
msgstr "download: failed: file not found"

#: methods/googledrive.php:600
msgid "%s authorization failed"
msgstr "%s authorisation failed"

#: methods/addon-base-v2.php:358 methods/cloudfiles.php:598
#: methods/googledrive.php:676 methods/openstack-base.php:535
#: methods/s3.php:1468
msgid "Success"
msgstr "Success"

#: includes/updraftplus-notices.php:158
msgid "Facebook"
msgstr "Facebook"

#: admin.php:1120 methods/cloudfiles-new.php:186 methods/cloudfiles.php:547
#: methods/openstack2.php:256
msgid "Username"
msgstr "Username"

#: methods/addon-base-v2.php:253 methods/openstack-base.php:460
msgid "%s Error"
msgstr "%s Error"

#: class-updraftplus.php:2104
msgid "Plugins"
msgstr "Plugins"

#: class-updraftplus.php:2106
msgid "Uploads"
msgstr "Uploads"

#: class-updraftplus.php:2105
msgid "Themes"
msgstr "Themes"

#: includes/updraftplus-notices.php:160
msgid "LinkedIn"
msgstr "LinkedIn"

#: methods/cloudfiles-new.php:181 methods/cloudfiles.php:542
#: methods/s3.php:1383
msgid "API key"
msgstr "API key"

#: methods/dropbox.php:954 methods/dropbox.php:963 methods/googledrive.php:637
msgid "Your %s quota usage: %s %% used, %s available"
msgstr "Your %s quota usage: %s %% used, %s available"

#: methods/dropbox.php:933
msgid "Your %s account name: %s"
msgstr "Your %s account name: %s"

#: methods/dropbox.php:909 methods/dropbox.php:911
msgid "you have authenticated your %s account"
msgstr "you have authenticated your %s account"

#: methods/dropbox.php:909 methods/dropbox.php:911
msgid "Success:"
msgstr "Success:"

#: methods/dropbox.php:815 methods/dropbox.php:872
msgid "%s authentication"
msgstr "%s authentication"

#: methods/dropbox.php:749
msgid "Account holder's name: %s."
msgstr "Account holder's name: %s."

#: methods/dropbox.php:605
msgid "Dropbox"
msgstr "Dropbox"

#: methods/dropbox.php:605
msgid "Authenticate with %s"
msgstr "Authenticate with %s"

#: methods/dropbox.php:602
msgid "Need to use sub-folders?"
msgstr "Need to use sub-folders?"

#: methods/dropbox.php:490
msgid "Failed to access %s when deleting (see log file for more)"
msgstr "Failed to access %s when deleting (see log file for more)"

#: methods/dropbox.php:406
msgid "%s returned an unexpected HTTP response: %s"
msgstr "%s returned an unexpected HTTP response: %s"

#: methods/dropbox.php:313
msgid "error: failed to upload file to %s (see log file for more)"
msgstr "error: failed to upload file to %s (see log file for more)"

#: methods/cloudfiles.php:598 methods/openstack-base.php:535
msgid "We accessed the container, and were able to create files within it."
msgstr "We accessed the container, and were able to create files within it."

#: methods/cloudfiles.php:594
msgid "Cloud Files error - we accessed the container, but failed to create a file within it"
msgstr "Cloud Files error - we accessed the container, but failed to create a file within it"

#: methods/cloudfiles.php:577 methods/cloudfiles.php:580
#: methods/cloudfiles.php:583
msgid "Cloud Files authentication failed"
msgstr "Cloud Files authentication failed"

#: methods/cloudfiles.php:567 methods/openstack-base.php:477
msgid "Failure: No container details were given."
msgstr "Failure: No container details were given."

#: methods/cloudfiles.php:506
msgid "Cloud Files username"
msgstr "Cloud Files username"

#: methods/cloudfiles.php:486
msgid "US or UK Cloud"
msgstr "US or UK Cloud"

#: admin.php:1041
msgid "%s settings test result:"
msgstr "%s settings test result:"

#: admin.php:1039 methods/backup-module.php:404 methods/cloudfiles-new.php:315
#: methods/dreamobjects.php:223 methods/ftp.php:130 methods/openstack2.php:260
#: methods/s3.php:1107 methods/s3generic.php:214
msgid "Test %s Settings"
msgstr "Test %s Settings"

#: methods/cloudfiles.php:415 methods/openstack-base.php:460
msgid "Error downloading remote file: Failed to download"
msgstr "Error downloading remote file: Failed to download"

#: methods/cloudfiles.php:244 methods/dropbox.php:388
#: methods/openstack-base.php:117
msgid "No settings were found"
msgstr "No settings were found"

#: methods/openstack-base.php:86
msgid "%s error - failed to upload file"
msgstr "%s error - failed to upload file"

#: methods/openstack-base.php:314 methods/s3.php:420 methods/s3.php:432
#: methods/s3.php:433
msgid "%s Error: Failed to upload"
msgstr "%s Error: Failed to upload"

#: methods/cloudfiles.php:250 methods/openstack-base.php:44
#: methods/openstack-base.php:357 methods/openstack-base.php:422
#: methods/openstack-base.php:495 methods/openstack-base.php:498
#: methods/openstack-base.php:516 methods/openstack-base.php:521
msgid "%s authentication failed"
msgstr "%s authentication failed"

#: methods/cloudfiles-new.php:144 methods/cloudfiles-new.php:314
#: methods/cloudfiles.php:515
msgid "Cloud Files Container"
msgstr "Cloud Files Container"

#: methods/cloudfiles-new.php:139 methods/cloudfiles-new.php:312
#: methods/cloudfiles.php:510
msgid "Cloud Files API Key"
msgstr "Cloud Files API Key"

#: methods/cloudfiles-new.php:131 methods/cloudfiles-new.php:311
msgid "Cloud Files Username"
msgstr "Cloud Files Username"

#: methods/cloudfiles-new.php:163
msgid "London (LON)"
msgstr "London (LON)"

#: methods/cloudfiles-new.php:161
msgid "Northern Virginia (IAD)"
msgstr "Northern Virginia (IAD)"

#: methods/cloudfiles-new.php:160
msgid "Chicago (ORD)"
msgstr "Chicago (ORD)"

#: methods/cloudfiles-new.php:159
msgid "Sydney (SYD)"
msgstr "Sydney (SYD)"

#: methods/cloudfiles-new.php:158
msgid "Dallas (DFW) (default)"
msgstr "Dallas (DFW) (default)"

#: methods/cloudfiles-new.php:121 methods/cloudfiles-new.php:310
msgid "Cloud Files Storage Region"
msgstr "Cloud Files Storage Region"

#: methods/cloudfiles-new.php:116 methods/cloudfiles-new.php:308
#: methods/cloudfiles.php:490
msgid "UK"
msgstr "UK"

#: methods/cloudfiles-new.php:115 methods/cloudfiles-new.php:307
#: methods/cloudfiles.php:489
msgid "US (default)"
msgstr "US (default)"

#: methods/cloudfiles-new.php:114
msgid "Accounts created at rackspacecloud.com are US-accounts; accounts created at rackspace.co.uk are UK-based"
msgstr "Accounts created at rackspacecloud.com are US-accounts; accounts created at rackspace.co.uk are UK-based"

#: methods/cloudfiles-new.php:112 methods/cloudfiles-new.php:304
msgid "US or UK-based Rackspace Account"
msgstr "US or UK-based Rackspace Account"

#: methods/cloudfiles-new.php:112 methods/cloudfiles-new.php:305
msgid "Accounts created at rackspacecloud.com are US accounts; accounts created at rackspace.co.uk are UK accounts."
msgstr "Accounts created at rackspacecloud.com are US accounts; accounts created at rackspace.co.uk are UK accounts."

#: methods/cloudfiles-new.php:302 methods/cloudfiles.php:468
#: methods/openstack2.php:247
msgid "Also, you should read this important FAQ."
msgstr "Also, you should read this important FAQ."

#: methods/cloudfiles-new.php:37 methods/openstack-base.php:489
#: methods/openstack-base.php:491 methods/openstack-base.php:512
#: methods/openstack2.php:34
msgid "Authorisation failed (check your credentials)"
msgstr "Authorisation failed (check your credentials)"

#. translators: %s: Connection type (e.g., FTP)
#: includes/ftp.class.php:59 includes/ftp.class.php:63
msgid "The %s connection timed out; if you entered the server correctly, then this is usually caused by a firewall blocking the connection - you should check with your web hosting company."
msgstr "The %s connection timed out; if you entered the server correctly, then this is usually caused by a firewall blocking the connection - you should check with your web hosting company."

#. translators: %s: Authentication service name (e.g., Dropbox)
#: includes/Dropbox2/OAuth/Consumer/ConsumerAbstract.php:139
msgid "You need to re-authenticate with %s, as your existing credentials are not working."
msgstr "You need to re-authenticate with %s, as your existing credentials are not working."

#: includes/class-filesystem-functions.php:356
msgid "The attempt to undo the double-compression succeeded."
msgstr "The attempt to undo the double-compression succeeded."

#: includes/class-filesystem-functions.php:332
#: includes/class-filesystem-functions.php:354
msgid "The attempt to undo the double-compression failed."
msgstr "The attempt to undo the double-compression failed."

#: includes/class-filesystem-functions.php:325
msgid "The database file appears to have been compressed twice - probably the website you downloaded it from had a mis-configured webserver."
msgstr "The database file appears to have been compressed twice - probably the website you downloaded it from had a mis-configured webserver."

#: includes/class-filesystem-functions.php:305 restorer.php:2847
msgid "restoration"
msgstr "restoration"

#: includes/class-filesystem-functions.php:305 methods/ftp.php:116
msgid "Your hosting company must enable these functions before %s can work."
msgstr "Your hosting company must enable these functions before %s can work."

#: includes/class-filesystem-functions.php:305 methods/ftp.php:116
msgid "Your web server's PHP installation has these functions disabled: %s."
msgstr "Your web server's PHP installation has these functions disabled: %s."

#: class-updraftplus.php:5508
msgid "UpdraftPlus was unable to find the table prefix when scanning the database backup."
msgstr "UpdraftPlus was unable to find the table prefix when scanning the database backup."

#: class-updraftplus.php:5500
msgid "This database backup is missing core WordPress tables: %s"
msgstr "This database backup is missing core WordPress tables: %s"

#: class-updraftplus.php:5336
msgid "You must upgrade MySQL to be able to use this database."
msgstr "You must upgrade MySQL to be able to use this database."

#: class-updraftplus.php:5336
msgid "The database backup uses MySQL features not available in the old MySQL version (%s) that this site is running on."
msgstr "The database backup uses MySQL features not available in the old MySQL version (%s) that this site is running on."

#: class-updraftplus.php:5270 restorer.php:3127
msgid "Site information:"
msgstr "Site information:"

#: class-updraftplus.php:5263
msgid "If you want to restore a multisite backup, you should first set up your WordPress installation as a multisite."
msgstr "If you want to restore a multisite backup, you should first set up your WordPress installation as a multisite."

#: class-updraftplus.php:5255 restorer.php:894
msgid "You are running on WordPress multisite - but your backup is not of a multisite site."
msgstr "You are running on WordPress multisite - but your backup is not of a multisite site."

#: class-updraftplus.php:5244
msgid "Backup label:"
msgstr "Backup label:"

#: class-updraftplus.php:5234 class-updraftplus.php:5236
msgid "Any support requests to do with %s should be raised with your web hosting company."
msgstr "Any support requests to do with %s should be raised with your web hosting company."

#: class-updraftplus.php:5234
msgid "This is significantly newer than the server which you are now restoring onto (version %s)."
msgstr "This is significantly newer than the server which you are now restoring onto (version %s)."

#: class-updraftplus.php:5167
msgid "(version: %s)"
msgstr "(version: %s)"

#: class-updraftplus.php:5109
msgid "Failed to open database file."
msgstr "Failed to open database file."

#: class-updraftplus.php:5095
msgid "The database is too small to be a valid WordPress database (size: %s Kb)."
msgstr "The database is too small to be a valid WordPress database (size: %s Kb)."

#: class-updraftplus.php:4863 methods/googledrive.php:1448 methods/s3.php:382
msgid "File not found"
msgstr "File not found"

#: includes/class-updraftplus-encryption.php:355
msgid "The decryption key used:"
msgstr "The decryption key used:"

#: class-updraftplus.php:4560
msgid "Could not read the directory"
msgstr "Could not read the directory"

#: class-updraftplus.php:3721
msgid "The backup has not finished; a resumption is scheduled"
msgstr "The backup has not finished; a resumption is scheduled"

#: class-updraftplus.php:3717
msgid "The backup attempt has finished, apparently unsuccessfully"
msgstr "The backup attempt has finished, apparently unsuccessfully"

#: includes/class-backup-history.php:730
msgid "One or more backups has been added from scanning remote storage; note that these backups will not be automatically deleted through the \"retain\" settings; if/when you wish to delete them then you must do so manually."
msgstr "One or more backups has been added from scanning remote storage; note that these backups will not be automatically deleted through the \"retain\" settings; if/when you wish to delete them then you must do so manually."

#: class-updraftplus.php:2403
msgid "Your website is visited infrequently and UpdraftPlus is not getting the resources it hoped for; please read this page:"
msgstr "Your website is visited infrequently and UpdraftPlus is not getting the resources it hoped for; please read this page:"

#: class-updraftplus.php:2123
msgid "Others"
msgstr "Others"

#: class-updraftplus.php:1652 methods/cloudfiles.php:428
msgid "Error - failed to download the file"
msgstr "Error - failed to download the file"

#: class-updraftplus.php:1584 class-updraftplus.php:1628
#: methods/cloudfiles.php:398
msgid "Error opening local file: Failed to download"
msgstr "Error opening local file: Failed to download"

#: class-updraftplus.php:1541
msgid "%s error - failed to re-assemble chunks"
msgstr "%s error - failed to re-assemble chunks"

#: class-updraftplus.php:1426
msgid "%s Error: Failed to open local file"
msgstr "%s Error: Failed to open local file"

#: class-updraftplus.php:1067
msgid "Your free disk space is very low - only %s Mb remain"
msgstr "Your free disk space is very low - only %s MB remain"

#: class-updraftplus.php:1038
msgid "The amount of memory (RAM) allowed for PHP is very low (%s Mb) - you should increase it to avoid failures due to insufficient memory (consult your web hosting company for more help)"
msgstr "The amount of memory (RAM) allowed for PHP is very low (%s MB) - you should increase it to avoid failures due to insufficient memory (consult your web hosting company for more help)"

#: class-updraftplus.php:752
msgid "No log files were found."
msgstr "No log files were found."

#: class-updraftplus.php:623 methods/dropbox.php:815 methods/dropbox.php:872
#: methods/dropbox.php:886 methods/dropbox.php:906 methods/dropbox.php:1072
msgid "%s error: %s"
msgstr "%s error: %s"

#: backup.php:4390
msgid "check your log for more details."
msgstr "check your log for more details."

#: backup.php:4386
msgid "A zip error occurred"
msgstr "A zip error occurred"

#: backup.php:4384
msgid "The zip engine returned the message: %s."
msgstr "The zip engine returned the message: %s."

#: backup.php:4377 class-updraftplus.php:1054
msgid "Your free space in your hosting account is very low - only %s Mb remain"
msgstr "Your free space in your hosting account is very low - only %s MB remain"

#: backup.php:4040
msgid "A very large file was encountered: %s (size: %s Mb)"
msgstr "A very large file was encountered: %s (size: %s MB)"

#: backup.php:4005 backup.php:4330
msgid "Failed to open the zip file (%s) - %s"
msgstr "Failed to open the zip file (%s) - %s"

#: backup.php:3196 backup.php:3225
msgid "%s: unreadable file - could not be backed up"
msgstr "%s: unreadable file - could not be backed up"

#: backup.php:3136
msgid "%s: unreadable file - could not be backed up (check the file permissions and ownership)"
msgstr "%s: unreadable file - could not be backed up (check the file permissions and ownership)"

#: backup.php:3101
msgid "Infinite recursion: consult your log for more information"
msgstr "Infinite recursion: consult your log for more information"

#: backup.php:2932
msgid "Could not open the backup file for writing"
msgstr "Could not open the backup file for writing"

#: backup.php:2064
msgid "An error occurred whilst closing the final database file"
msgstr "An error occurred whilst closing the final database file"

#: backup.php:1970
msgid "Failed to open database file for reading:"
msgstr "Failed to open database file for reading:"

#: backup.php:1643
msgid "No database tables found"
msgstr "No database tables found"

#: backup.php:1641
msgid "please wait for the rescheduled attempt"
msgstr "please wait for the rescheduled attempt"

#: backup.php:1591
msgid "Connection failed: check your access details, that the database server is up, and that the network connection is not firewalled."
msgstr "Connection failed: check your access details, that the database server is up, and that the network connection is not firewalled."

#: backup.php:1591
msgid "database connection attempt failed."
msgstr "database connection attempt failed."

#: class-updraftplus.php:3901
msgid "Latest status:"
msgstr "Latest status:"

#: methods/cloudfiles-new.php:162
msgid "Hong Kong (HKG)"
msgstr "Hong Kong (HKG)"

#: methods/dropbox.php:316
msgid "did not return the expected response - check your log file for more details"
msgstr "did not return the expected response - check your log file for more details"

#: methods/cloudfiles.php:424
msgid "Error - no such file exists."
msgstr "Error - no such file exists."

#: backup.php:2111
msgid "Table %s has very many rows (%s) - we hope your web hosting company gives you enough resources to dump out that table in the backup."
msgstr "Table %s has very many rows (%s) - we hope your web hosting company gives you enough resources to dump out that table in the backup."

#: methods/backup-module.php:634 methods/dropbox.php:607
msgid "<strong>After</strong> you have saved your settings (by clicking 'Save Changes' below), then come back here and follow this link to complete authentication with %s."
msgstr "<strong>After</strong> you have saved your settings (by clicking 'Save Changes' below), then come back here and follow this link to complete authentication with %s."

#: class-updraftplus.php:5234
msgid "You should only proceed if you cannot update the current server and are confident (or willing to risk) that your plugins/themes/etc are compatible with the older %s version."
msgstr "You should only proceed if you cannot update the current server and are confident (or willing to risk) that your plugins/themes/etc are compatible with the older %s version."

#: admin.php:1186 templates/wp-admin/settings/form-contents.php:407
msgid "Save Changes"
msgstr "Save Changes"

#: updraftplus.php:158
msgid "Monthly"
msgstr "Monthly"

#: admin.php:4557
msgid "None"
msgstr "None"

#: admin.php:3852 central/translations-central.php:28
#: methods/updraftvault.php:578 methods/updraftvault.php:625
#: methods/updraftvault.php:745
msgid "Unknown"
msgstr "Unknown"

#: admin.php:3975 admin.php:3993 admin.php:4024 admin.php:4034 backup.php:2049
#: methods/addon-base-v2.php:344
msgid "Failed"
msgstr "Failed"

#: admin.php:3979 admin.php:3990 admin.php:4027 admin.php:4031
#: includes/class-remote-send.php:449
#: includes/class-storage-methods-interface.php:329 restorer.php:595
#: restorer.php:4240 restorer.php:4383
msgid "OK"
msgstr "OK"

#: updraftplus.php:146
msgid "Daily"
msgstr "Daily"

#: updraftplus.php:150
msgid "Weekly"
msgstr "Weekly"

#: admin.php:3419 methods/updraftvault.php:541
#: templates/wp-admin/settings/form-contents.php:264
#: templates/wp-admin/settings/updraftcentral-connect.php:44
msgid "Email"
msgstr "Email"

#: templates/wp-admin/settings/existing-backups-table.php:101
msgid "Site"
msgstr "Site"

#: class-updraftplus.php:3900
msgid "Backup contains:"
msgstr "Backup contains:"

#: class-updraftplus.php:3899
msgid "WordPress backup is complete"
msgstr "WordPress backup is complete"

#: class-updraftplus.php:3898 class-updraftplus.php:5167
msgid "Backup of:"
msgstr "Backup of:"

#: class-updraftplus.php:3866
msgid "read more at %s"
msgstr "read more at %s"

#: class-updraftplus.php:3866
msgid "Email reports created by UpdraftPlus (free edition) bring you the latest UpdraftPlus.com news"
msgstr "Email reports created by UpdraftPlus (free edition) bring you the latest UpdraftPlus.com news"

#: class-updraftplus.php:3857
msgid "Backed up: %s"
msgstr "Backed up: %s"

#: class-updraftplus.php:3851 class-updraftplus.php:4026
msgid "The log file has been attached to this email."
msgstr "The log file has been attached to this email."

#: class-updraftplus.php:3841
msgid "Warnings encountered:"
msgstr "Warnings encountered:"

#: class-updraftplus.php:3820
msgid "Errors encountered:"
msgstr "Errors encountered:"

#: class-updraftplus.php:3811
msgid "Unknown/unexpected error - please raise a support request"
msgstr "Unknown/unexpected error - please raise a support request"

#: class-updraftplus.php:3806
msgid "Database only (files were not part of this particular schedule)"
msgstr "Database only (files were not part of this particular schedule)"

#: class-updraftplus.php:3803
msgid "Files only (database was not part of this particular schedule)"
msgstr "Files only (database was not part of this particular schedule)"

#: admin.php:448 class-updraftplus.php:3801
msgid "Files and database"
msgstr "Files and database"

#: class-updraftplus.php:3794
msgid "Incremental"
msgstr "Incremental"

#: class-updraftplus.php:3794
msgid "Full backup"
msgstr "Full backup"

#: backup.php:288
msgid "%s - could not back this entity up; the corresponding directory does not exist (%s)"
msgstr "%s - could not back this entity up; the corresponding directory does not exist (%s)"

#: restorer.php:760
msgid "Error message"
msgstr "Error message"

#: restorer.php:600
msgid "The backup records do not contain information about the proper size of this file."
msgstr "The backup records do not contain information about the proper size of this file."

#: restorer.php:597
msgid "file is size:"
msgstr "file is size:"

#: restorer.php:592
msgid "Archive is expected to be size:"
msgstr "Archive is expected to be size:"

#: includes/class-storage-methods-interface.php:301
msgid "File is not locally present - needs retrieving from remote storage"
msgstr "File is not locally present - needs retrieving from remote storage"

#: restorer.php:575
msgid "Looking for %s archive: file name: %s"
msgstr "Looking for %s archive: file name: %s"

#: restorer.php:670
msgid "Final checks"
msgstr "Final checks"

#: admin.php:5385
msgid "If making a request for support, please include this information:"
msgstr "If making a request for support, please include this information:"

#: admin.php:5385
msgid "ABORT: Could not find the information on which entities to restore."
msgstr "ABORT: Could not find the information on which entities to restore."

#: admin.php:5251
msgid "Follow this link to download the log file for this restoration (needed for any support requests)."
msgstr "Follow this link to download the log file for this restoration (needed for any support requests)."

#: includes/class-filesystem-functions.php:83
msgid "Why am I seeing this?"
msgstr "Why am I seeing this?"

#: admin.php:5344
msgid "Backup does not exist in the backup history"
msgstr "Backup does not exist in the backup history"

#: admin.php:3711 admin.php:4857
msgid "View Log"
msgstr "View Log"

#: admin.php:4835
msgid "Delete this backup set"
msgstr "Delete this backup set"

#: admin.php:4741
msgid "After pressing this button, you will be given the option to choose which components you wish to restore"
msgstr "After pressing this button, you will be given the option to choose which components you wish to restore"

#: admin.php:4738
msgid "(backup set imported from remote location)"
msgstr "(backup set imported from remote location)"

#: admin.php:4692
msgid "If you are seeing more backups than you expect, then it is probably because the deletion of old backup sets does not happen until a fresh backup completes."
msgstr "If you are seeing more backups than you expect, then it is probably because the deletion of old backup sets does not happen until a fresh backup completes."

#: admin.php:4690 admin.php:4692
msgid "(Not finished)"
msgstr "(Not finished)"

#: admin.php:4636
msgid "Files backup (created by %s)"
msgstr "Files backup (created by %s)"

#: admin.php:4636
msgid "Files and database WordPress backup (created by %s)"
msgstr "Files and database WordPress backup (created by %s)"

#. translators: %s: The description of the accepted foreign backup type.
#: admin.php:4630 includes/class-backup-history.php:535
msgid "Backup created by: %s."
msgstr "Backup created by: %s."

#: admin.php:4595
msgid "External database"
msgstr "External database"

#: admin.php:4593
msgid "Database (created by %s)"
msgstr "Database (created by %s)"

#: admin.php:4589 admin.php:4632
msgid "unknown source"
msgstr "unknown source"

#: templates/wp-admin/settings/existing-backups-table.php:100
msgid "Backup sent to remote site - not available for download."
msgstr "Backup sent to remote site - not available for download."

#: templates/wp-admin/settings/existing-backups-table.php:18
#: templates/wp-admin/settings/existing-backups-table.php:96
msgid "Backup data (click to download)"
msgstr "Backup data (click to download)"

#: templates/wp-admin/settings/existing-backups-table.php:17
#: templates/wp-admin/settings/existing-backups-table.php:61
msgid "Backup date"
msgstr "Backup date"

#: includes/class-backup-history.php:133
msgid "You have not yet made any backups."
msgstr "You have not yet made any backups."

#: admin.php:4366
msgid "Any other directories found inside wp-content"
msgstr "Any other directories found inside wp-content"

#: admin.php:4366
msgid "Your wp-content directory server path: %s"
msgstr "Your wp-content directory server path: %s"

#: templates/wp-admin/settings/form-contents.php:379
msgid "See this FAQ also."
msgstr "See this FAQ also."

#: templates/wp-admin/settings/form-contents.php:378
msgid "Disable SSL entirely where possible"
msgstr "Disable SSL entirely where possible"

#: templates/wp-admin/settings/form-contents.php:374
msgid "Note that not all cloud backup methods are necessarily using SSL authentication."
msgstr "Note that not all cloud backup methods are necessarily using SSL authentication."

#: templates/wp-admin/settings/form-contents.php:373
msgid "Do not verify SSL certificates"
msgstr "Do not verify SSL certificates"

#: templates/wp-admin/settings/form-contents.php:368
msgid "Use the server's SSL certificates"
msgstr "Use the server's SSL certificates"

#: admin.php:4282
msgid "If that is unsuccessful check the permissions on your server or change it to another directory that is writable by your web server process."
msgstr "If that is unsuccessful check the permissions on your server or change it to another directory that is writable by your web server process."

#: admin.php:4282
msgid "or, to reset this option"
msgstr "or, to reset this option"

#: admin.php:4282
msgid "Follow this link to attempt to create the directory and set the permissions"
msgstr "Follow this link to attempt to create the directory and set the permissions"

#: admin.php:4280
msgid "Backup directory specified exists, but is <b>not</b> writable."
msgstr "Backup directory specified exists, but is <b>not</b> writable."

#: admin.php:4278
msgid "Backup directory specified does <b>not</b> exist."
msgstr "Backup directory specified does <b>not</b> exist."

#: admin.php:4274
msgid "Backup directory specified is writable, which is good."
msgstr "Backup directory specified is writable, which is good."

#: templates/wp-admin/settings/form-contents.php:352
msgid "Backup directory"
msgstr "Backup directory"

#: templates/wp-admin/settings/form-contents.php:347
msgid "Delete local backup"
msgstr "Delete local backup"

#: templates/wp-admin/settings/form-contents.php:333
msgid "Split archives every:"
msgstr "Split archives every:"

#: templates/wp-admin/settings/form-contents.php:329
msgid "This will also cause debugging output from all plugins to be shown upon this screen - please do not be surprised to see these."
msgstr "This will also cause debugging output from all plugins to be shown upon this screen - please do not be surprised to see these."

#: templates/wp-admin/settings/form-contents.php:328
msgid "Debug mode"
msgstr "Debug mode"

#: templates/wp-admin/settings/form-contents.php:318
msgid "Show expert settings"
msgstr "Show expert settings"

#: templates/wp-admin/settings/form-contents.php:317
msgid "Expert settings"
msgstr "Expert settings"

#: templates/wp-admin/settings/form-contents.php:313
msgid "Advanced / Debugging Settings"
msgstr "Advanced / Debugging Settings"

#: templates/wp-admin/settings/form-contents.php:105
msgid "Choose your remote storage"
msgstr "Choose your remote storage"

#: templates/wp-admin/settings/form-contents.php:95
msgid "Sending Your Backup To Remote Storage"
msgstr "Sending Your Backup To Remote Storage"

#: templates/wp-admin/settings/form-contents.php:282
#: templates/wp-admin/settings/form-contents.php:285
msgid "your site's admin address"
msgstr "your site's admin address"

#: templates/wp-admin/settings/form-contents.php:282
msgid "Check this box to have a basic report sent to"
msgstr "Check this box to have a basic report sent to"

#: templates/wp-admin/settings/form-contents.php:252
#: templates/wp-admin/settings/tab-addons.php:249
#: templates/wp-admin/settings/tab-addons.php:250
msgid "Reporting"
msgstr "Reporting"

#: templates/wp-admin/settings/form-contents.php:201
msgid "First, enter the decryption key"
msgstr "First, enter the decryption key"

#: templates/wp-admin/settings/form-contents.php:199
msgctxt "Uploader: Drop db.gz.crypt files here to upload them for decryption - or - Select Files"
msgid "or"
msgstr "or"

#: templates/wp-admin/settings/form-contents.php:198
msgid "Drop encrypted database files (db.gz.crypt files) here to upload them for decryption"
msgstr "Drop encrypted database files (db.gz.crypt files) here to upload them for decryption"

#: templates/wp-admin/settings/form-contents.php:186
msgid "Manually decrypt a database backup file"
msgstr "Manually decrypt a database backup file"

#: templates/wp-admin/settings/form-contents.php:183
msgid "You can manually decrypt an encrypted database here."
msgstr "You can manually decrypt an encrypted database here."

#: templates/wp-admin/settings/form-contents.php:165
msgid "Database encryption phrase"
msgstr "Database encryption phrase"

#: templates/wp-admin/settings/form-contents.php:160
msgid "Database Options"
msgstr "Database Options"

#: templates/wp-admin/settings/form-contents.php:147
msgid "Include in files backup"
msgstr "Include in files backup"

#: templates/wp-admin/settings/form-contents.php:42
#: templates/wp-admin/settings/form-contents.php:76
msgid "and retain this many scheduled backups"
msgstr "and retain this many scheduled backups"

#: updraftplus.php:154
msgid "Fortnightly"
msgstr "Fortnightly"

#: updraftplus.php:122
msgid "Every %s hours"
msgstr "Every %s hours"

#: admin.php:4267
msgctxt "i.e. Non-automatic"
msgid "Manual"
msgstr "Manual"

#: admin.php:4237
msgid "No backup has been completed"
msgstr "No backup has been completed"

#: admin.php:4207
msgid "incremental backup; base backup: %s"
msgstr "incremental backup; base backup: %s"

#: admin.php:4095
msgid "You will need to consult with your web hosting provider to find out how to set permissions for a WordPress plugin to write to the directory."
msgstr "You will need to consult with your web hosting provider to find out how to set permissions for a WordPress plugin to write to the directory."

#: admin.php:4095
msgid "The folder exists, but your webserver does not have permission to write to it."
msgstr "The folder exists, but your webserver does not have permission to write to it."

#: admin.php:4076
msgid "The request to the filesystem to create the directory failed."
msgstr "The request to the filesystem to create the directory failed."

#: admin.php:3935
msgid "Remove old directories"
msgstr "Remove old directories"

#: admin.php:3904 admin.php:4219
msgid "Warning: %s"
msgstr "Warning: %s"

#: admin.php:3924
msgid "show log"
msgstr "show log"

#: admin.php:3890
msgid "Job ID: %s"
msgstr "Job ID: %s"

#: admin.php:3870
msgid "last activity: %ss ago"
msgstr "last activity: %ss ago"

#: class-updraftplus.php:3806
msgid "Database (files backup has not completed)"
msgstr "Database (files backup has not completed)"

#: class-updraftplus.php:3803
msgid "Files (database backup has not completed)"
msgstr "Files (database backup has not completed)"

#: templates/wp-admin/settings/form-contents.php:329
msgid "Check this to receive more information and emails on the backup process - useful if something is going wrong."
msgstr "Check this to receive more information and emails on the backup process - useful if something is going wrong."

#: templates/wp-admin/settings/form-contents.php:242
msgid "Backup more databases"
msgstr "Backup more databases"

#: templates/wp-admin/settings/form-contents.php:62
msgid "Database backup interval"
msgstr "Database backup interval"

#: templates/wp-admin/settings/form-contents.php:25
msgid "Files backup interval"
msgstr "Files backup interval"

#: templates/wp-admin/settings/form-contents.php:318
msgid "open this to show some further options; don't bother with this unless you have a problem or are curious."
msgstr "open this to show some further options; don't bother with this unless you have a problem or are curious."

#: restorer.php:603 restorer.php:604
msgid "Could not read one of the files for restoration"
msgstr "Could not read one of the files for restoration"

#: templates/wp-admin/settings/header.php:17
#: templates/wp-admin/settings/tab-addons.php:28
#: templates/wp-admin/settings/tab-addons.php:145
msgid "Support"
msgstr "Support"

#: admin.php:1133 admin.php:3039 class-updraftplus.php:5263 restorer.php:3873
msgid "Warning:"
msgstr "Warning:"

#: admin.php:965 admin.php:3020 admin.php:3942 admin.php:5167 admin.php:5179
#: admin.php:5190 templates/wp-admin/settings/existing-backups-table.php:19
#: templates/wp-admin/settings/existing-backups-table.php:138
msgid "Actions"
msgstr "Actions"

#: admin.php:2771 admin.php:2792 admin.php:2800 class-updraftplus.php:1241
#: class-updraftplus.php:1247 class-updraftplus.php:5076
#: class-updraftplus.php:5078 class-updraftplus.php:5259
#: class-updraftplus.php:5336 methods/googledrive.php:594 methods/s3.php:382
msgid "Error: %s"
msgstr "Error: %s"

#: templates/wp-admin/settings/take-backup.php:95
msgid "Multisite"
msgstr "Multisite"

#: templates/wp-admin/advanced/site-info.php:55
msgid "Peak memory usage"
msgstr "Peak memory usage"

#: templates/wp-admin/settings/header.php:25
msgid "Version"
msgstr "Version"

#: templates/wp-admin/advanced/site-info.php:78
#: templates/wp-admin/advanced/site-info.php:84
#: templates/wp-admin/advanced/site-info.php:88
#: templates/wp-admin/settings/tab-addons.php:111
#: templates/wp-admin/settings/tab-addons.php:124
#: templates/wp-admin/settings/tab-addons.php:137
#: templates/wp-admin/settings/tab-addons.php:150
#: templates/wp-admin/settings/tab-addons.php:163
#: templates/wp-admin/settings/tab-addons.php:176
#: templates/wp-admin/settings/tab-addons.php:189
#: templates/wp-admin/settings/tab-addons.php:202
#: templates/wp-admin/settings/tab-addons.php:215
#: templates/wp-admin/settings/tab-addons.php:228
#: templates/wp-admin/settings/tab-addons.php:241
#: templates/wp-admin/settings/tab-addons.php:254
#: templates/wp-admin/settings/tab-addons.php:267
#: templates/wp-admin/settings/tab-addons.php:280
#: templates/wp-admin/settings/tab-addons.php:293
#: templates/wp-admin/settings/tab-addons.php:310
msgid "No"
msgstr "No"

#: templates/wp-admin/advanced/site-info.php:78
#: templates/wp-admin/advanced/site-info.php:81
#: templates/wp-admin/advanced/site-info.php:84
#: templates/wp-admin/advanced/site-info.php:88
#: templates/wp-admin/settings/tab-addons.php:85
#: templates/wp-admin/settings/tab-addons.php:98
#: templates/wp-admin/settings/tab-addons.php:101
#: templates/wp-admin/settings/tab-addons.php:114
#: templates/wp-admin/settings/tab-addons.php:127
#: templates/wp-admin/settings/tab-addons.php:140
#: templates/wp-admin/settings/tab-addons.php:153
#: templates/wp-admin/settings/tab-addons.php:166
#: templates/wp-admin/settings/tab-addons.php:179
#: templates/wp-admin/settings/tab-addons.php:192
#: templates/wp-admin/settings/tab-addons.php:205
#: templates/wp-admin/settings/tab-addons.php:218
#: templates/wp-admin/settings/tab-addons.php:231
#: templates/wp-admin/settings/tab-addons.php:244
#: templates/wp-admin/settings/tab-addons.php:257
#: templates/wp-admin/settings/tab-addons.php:270
#: templates/wp-admin/settings/tab-addons.php:283
#: templates/wp-admin/settings/tab-addons.php:296
#: templates/wp-admin/settings/tab-addons.php:313
#: templates/wp-admin/settings/tab-addons.php:319
msgid "Yes"
msgstr "Yes"

#: admin.php:448 admin.php:6002 templates/wp-admin/settings/take-backup.php:24
msgid "Files"
msgstr "Files"

#: includes/updraftplus-notices.php:28
#: templates/wp-admin/settings/header.php:11
#: templates/wp-admin/settings/tab-addons.php:79
msgid "Premium"
msgstr "Premium"

#: admin.php:3850
msgid "Encrypted database"
msgstr "Encrypted database"

#: admin.php:3842
msgid "Encrypting database"
msgstr "Encrypting database"

#: admin.php:3829
msgid "table: %s"
msgstr "table: %s"

#: admin.php:3827
msgid "Creating database backup"
msgstr "Creating database backup"

#: admin.php:3816
msgid "Created database backup"
msgstr "Created database backup"

#: admin.php:3803
msgid "Backup finished"
msgstr "Backup finished"

#: admin.php:3798
msgid "Waiting until scheduled time to retry because of errors"
msgstr "Waiting until scheduled time to retry because of errors"

#: admin.php:3794
msgid "Pruning old backup sets"
msgstr "Pruning old backup sets"

#: admin.php:3781
msgid "Uploading files to remote storage"
msgstr "Uploading files to remote storage"

#: admin.php:3771
msgid "Created file backup zips"
msgstr "Created file backup zips"

#: admin.php:3758
msgid "Creating file backup zips"
msgstr "Creating file backup zips"

#: admin.php:3753
msgid "Backup begun"
msgstr "Backup begun"

#: templates/wp-admin/advanced/wipe-settings.php:10
msgid "This will delete all your UpdraftPlus settings - are you sure you want to do this?"
msgstr "This will delete all your UpdraftPlus settings - are you sure you want to do this?"

#: templates/wp-admin/advanced/tools-menu.php:40
#: templates/wp-admin/advanced/wipe-settings.php:5
#: templates/wp-admin/advanced/wipe-settings.php:10
msgid "Wipe settings"
msgstr "Wipe settings"

#: templates/wp-admin/advanced/total-size.php:19
msgid "count"
msgstr "count"

#: templates/wp-admin/advanced/total-size.php:9
msgid "N.B. This count is based upon what was, or was not, excluded the last time you saved the options."
msgstr "N.B. This count is based upon what was, or was not, excluded the last time you saved the options."

#: templates/wp-admin/advanced/total-size.php:6
msgid "Total (uncompressed) on-disk data:"
msgstr "Total (uncompressed) on-disk data:"

#: templates/wp-admin/advanced/site-info.php:122
msgid "Show raw backup and file list"
msgstr "Show raw backup and file list"

#: templates/wp-admin/advanced/site-info.php:118
msgid "Call"
msgstr "Call"

#: templates/wp-admin/advanced/site-info.php:116
msgid "Fetch"
msgstr "Fetch"

#: templates/wp-admin/advanced/site-info.php:92
msgid "%s (%s used)"
msgstr "%s (%s used)"

#: templates/wp-admin/advanced/site-info.php:92
msgid "Free disk space in account:"
msgstr "Free disk space in account:"

#: templates/wp-admin/advanced/site-info.php:88
msgid "zip executable found:"
msgstr "zip executable found:"

#: templates/wp-admin/advanced/site-info.php:58
msgid "show PHP information (phpinfo)"
msgstr "show PHP information (phpinfo)"

#: admin.php:6217 admin.php:6221 templates/wp-admin/advanced/site-info.php:58
#: templates/wp-admin/advanced/site-info.php:64
#: templates/wp-admin/advanced/site-info.php:76
#: templates/wp-admin/advanced/site-info.php:77
msgid "%s version:"
msgstr "%s version:"

#: templates/wp-admin/advanced/site-info.php:57
msgid "Memory limit"
msgstr "Memory limit"

#: templates/wp-admin/advanced/site-info.php:56
msgid "Current memory usage"
msgstr "Current memory usage"

#: templates/wp-admin/advanced/site-info.php:35
msgid "Web server:"
msgstr "Web server:"

#: templates/wp-admin/advanced/advanced-tools.php:6
msgid "Unless you have a problem, you can completely ignore everything here."
msgstr "Unless you have a problem, you can completely ignore everything here."

#: templates/wp-admin/settings/delete-and-restore-modals.php:51
msgid "Do read this helpful article of useful things to know before restoring."
msgstr "Do read this helpful article of useful things to know before restoring."

#. translators: %s: Restoration type
#: templates/wp-admin/settings/delete-and-restore-modals.php:95
msgid "%s restoration options:"
msgstr "%s restoration options:"

#: templates/wp-admin/settings/delete-and-restore-modals.php:84
msgid "You will need to restore it manually."
msgstr "You will need to restore it manually."

#. translators: %s: Entity that cannot be restored
#: templates/wp-admin/settings/delete-and-restore-modals.php:83
msgid "The following entity cannot be restored automatically: \"%s\"."
msgstr "The following entity cannot be restored automatically: \"%s\"."

#: templates/wp-admin/settings/delete-and-restore-modals.php:66
msgid "Your web server has PHP's so-called safe_mode active."
msgstr "Your web server has PHP's so-called safe_mode active."

#: templates/wp-admin/settings/delete-and-restore-modals.php:50
msgid "Choose the components to restore"
msgstr "Choose the components to restore"

#: templates/wp-admin/settings/delete-and-restore-modals.php:50
msgid "Restoring will replace this site's themes, plugins, uploads, database and/or other content directories (according to what is contained in the backup set, and your selection)."
msgstr "Restoring will replace this site's themes, plugins, uploads, database and/or other content directories (according to what is contained in the backup set, and your selection)."

#: templates/wp-admin/settings/delete-and-restore-modals.php:42
msgid "Retrieving (if necessary) and preparing backup files..."
msgstr "Retrieving (if necessary) and preparing backup files..."

#: templates/wp-admin/settings/delete-and-restore-modals.php:38
msgid "Restore backup"
msgstr "Restore backup"

#: templates/wp-admin/settings/delete-and-restore-modals.php:29
msgid "Also delete from remote storage"
msgstr "Also delete from remote storage"

#: templates/wp-admin/settings/delete-and-restore-modals.php:8
msgid "Delete backup set"
msgstr "Delete backup set"

#: templates/wp-admin/settings/downloading-and-restoring.php:79
#: templates/wp-admin/settings/form-contents.php:200
#: templates/wp-admin/settings/tab-backups.php:89
msgid "Select Files"
msgstr "Select Files"

#: templates/wp-admin/settings/downloading-and-restoring.php:78
msgctxt "Uploader: Drop backup files here - or - Select Files"
msgid "or"
msgstr "or"

#: templates/wp-admin/settings/downloading-and-restoring.php:77
msgid "Drop backup files here"
msgstr "Drop backup files here"

#: templates/wp-admin/settings/downloading-and-restoring.php:67
#: templates/wp-admin/settings/tab-backups.php:71
msgid "Or, you can place them manually into your UpdraftPlus directory (usually wp-content/updraft), e.g. via FTP, and then use the \"rescan\" link above."
msgstr "Or, you can place them manually into your UpdraftPlus directory (usually wp-content/updraft), e.g. via FTP, and then use the \"rescan\" link above."

#: templates/wp-admin/settings/downloading-and-restoring.php:67
#: templates/wp-admin/settings/tab-backups.php:71
msgid "Upload files into UpdraftPlus."
msgstr "Upload files into UpdraftPlus."

#: templates/wp-admin/settings/downloading-and-restoring.php:66
#: templates/wp-admin/settings/tab-backups.php:70
msgid "UpdraftPlus - Upload backup files"
msgstr "UpdraftPlus - Upload backup files"

#: templates/wp-admin/settings/downloading-and-restoring.php:57
#: templates/wp-admin/settings/tab-backups.php:63
msgid "If you are using this, then turn Turbo/Road mode off."
msgstr "If you are using this, then turn Turbo/Road mode off."

#: templates/wp-admin/settings/downloading-and-restoring.php:57
#: templates/wp-admin/settings/tab-backups.php:63
msgid "Opera web browser"
msgstr "Opera web browser"

#: templates/wp-admin/advanced/site-info.php:122
#: templates/wp-admin/settings/downloading-and-restoring.php:54
#: templates/wp-admin/settings/tab-backups.php:60
msgid "Rescan remote storage"
msgstr "Rescan remote storage"

#: templates/wp-admin/settings/downloading-and-restoring.php:53
#: templates/wp-admin/settings/tab-backups.php:59
msgid "Rescan local folder for new backup sets"
msgstr "Rescan local folder for new backup sets"

#: templates/wp-admin/settings/downloading-and-restoring.php:53
#: templates/wp-admin/settings/tab-backups.php:59
msgid "The location of this directory is set in the expert settings, in the Settings tab."
msgstr "The location of this directory is set in the expert settings, in the Settings tab."

#: templates/wp-admin/settings/downloading-and-restoring.php:53
#: templates/wp-admin/settings/tab-backups.php:59
msgid "Press here to look inside your UpdraftPlus directory (in your web hosting space) for any new backup sets that you have uploaded."
msgstr "Press here to look inside your UpdraftPlus directory (in your web hosting space) for any new backup sets that you have uploaded."

#: templates/wp-admin/settings/downloading-and-restoring.php:49
#: templates/wp-admin/settings/tab-backups.php:55
msgid "Upload backup files"
msgstr "Upload backup files"

#: templates/wp-admin/settings/downloading-and-restoring.php:45
#: templates/wp-admin/settings/tab-backups.php:51
msgid "More tasks:"
msgstr "More tasks:"

#: includes/class-filesystem-functions.php:105
#: templates/wp-admin/advanced/site-info.php:51
msgid "refresh"
msgstr "refresh"

#: includes/class-filesystem-functions.php:126
#: templates/wp-admin/advanced/site-info.php:51
msgid "Web-server disk space in use by UpdraftPlus"
msgstr "Web-server disk space in use by UpdraftPlus"

#: includes/class-filesystem-functions.php:126
msgid "This is a count of the contents of your Updraft directory"
msgstr "This is a count of the contents of your Updraft directory"

#: admin.php:3573
msgid "Latest UpdraftPlus.com news:"
msgstr "Latest UpdraftPlus.com news:"

#: admin.php:3600
msgid "Download most recently modified log file"
msgstr "Download most recently modified log file"

#: central/translations-central.php:17
msgid "(Nothing yet logged)"
msgstr "(Nothing yet logged)"

#: admin.php:3556 admin.php:3562 templates/wp-admin/settings/take-backup.php:72
msgid "Last log message"
msgstr "Last log message"

#: admin.php:5640
msgid "Send this backup to remote storage"
msgstr "Send this backup to remote storage"

#: templates/wp-admin/settings/tab-addons.php:27
msgid "Ask a pre-sales question"
msgstr "Ask a pre-sales question"

#: templates/wp-admin/settings/tab-addons.php:26
msgid "Pre-sales FAQs"
msgstr "Pre-sales FAQs"

#: templates/wp-admin/settings/tab-addons.php:25
msgid "Full feature list"
msgstr "Full feature list"

#: templates/wp-admin/settings/take-backup.php:99
msgid "Do you need WordPress Multisite support?"
msgstr "Do you need WordPress Multisite support?"

#: templates/wp-admin/settings/take-backup.php:19
msgid "Next scheduled backups"
msgstr "Next scheduled backups"

#: admin.php:427
msgid "At the same time as the files backup"
msgstr "At the same time as the files backup"

#: admin.php:417 admin.php:438 admin.php:445 admin.php:488 admin.php:519
msgid "Nothing currently scheduled"
msgstr "Nothing currently scheduled"

#: admin.php:5741 templates/wp-admin/settings/take-backup.php:52
msgid "This button is disabled because your backup directory is not writable (see the settings)."
msgstr "This button is disabled because your backup directory is not writable (see the settings)."

#: templates/wp-admin/settings/take-backup.php:6
msgid "JavaScript warning"
msgstr "JavaScript warning"

#: admin.php:3098
msgid "Current limit is:"
msgstr "Current limit is:"

#: admin.php:3061
msgid "Your backup has been restored."
msgstr "Your backup has been restored."

#: admin.php:3039
msgid "If you can still read these words after the page finishes loading, then there is a JavaScript or jQuery problem in the site."
msgstr "If you can still read these words after the page finishes loading, then there is a JavaScript or jQuery problem in the site."

#: admin.php:5896
msgid "Your settings have been wiped."
msgstr "Your settings have been wiped."

#: admin.php:3018
msgid "Backup directory successfully created."
msgstr "Backup directory successfully created."

#: admin.php:3011
msgid "Backup directory could not be created"
msgstr "Backup directory could not be created"

#: templates/wp-admin/settings/header.php:25
msgid "More plugins"
msgstr "More plugins"

#: templates/wp-admin/settings/header.php:25
msgid "Lead developer's homepage"
msgstr "Lead developer's homepage"

#: templates/wp-admin/settings/header.php:21
msgid "Newsletter sign-up"
msgstr "Newsletter sign-up"

#: admin.php:2903
msgid "Bad filename format - this does not look like an encrypted database file created by UpdraftPlus"
msgstr "Bad filename format - this does not look like an encrypted database file created by UpdraftPlus"

#: admin.php:2792
msgid "Bad filename format - this does not look like a file created by UpdraftPlus"
msgstr "Bad filename format - this does not look like a file created by UpdraftPlus"

#: admin.php:2786
msgid "This backup was created by %s, and can be imported."
msgstr "This backup was created by %s, and can be imported."

#: admin.php:2771
msgid "This file could not be uploaded"
msgstr "This file could not be uploaded"

#: admin.php:2734
msgid "You will find more information about this in the Settings section."
msgstr "You will find more information about this in the Settings section."

#: admin.php:2734 backup.php:1338
msgid "Backup directory (%s) is not writable, or does not exist."
msgstr "Backup directory (%s) is not writable, or does not exist."

#: admin.php:2680
msgid "No local copy present."
msgstr "No local copy present."

#: admin.php:2677
msgid "Download in progress"
msgstr "Download in progress"

#: admin.php:2647
msgid "Download failed"
msgstr "Download failed"

#: admin.php:2596 admin.php:2600 class-updraftplus.php:747
msgid "The log file could not be read."
msgstr "The log file could not be read."

#: admin.php:2588
msgid "Error: unexpected file read fail"
msgstr "Error: unexpected file read fail"

#: admin.php:1982
msgid "Messages:"
msgstr "Messages:"

#: admin.php:2391
msgid "Could not find that job - perhaps it has already finished?"
msgstr "Could not find that job - perhaps it has already finished?"

#: templates/wp-admin/settings/tab-addons.php:171
#: templates/wp-admin/settings/tab-addons.php:172
msgid "Backup non-WordPress files and databases"
msgstr "Backup non-WordPress files and databases"

#: admin.php:1396 includes/class-commands.php:506
#: templates/wp-admin/settings/take-backup.php:13
msgid "The 'Backup Now' button is disabled as your backup directory is not writable (go to the 'Settings' tab and find the relevant option)."
msgstr "The 'BackUp Now' button is disabled as your backup directory is not writable (go to the 'Settings' tab and find the relevant option)."

#: templates/wp-admin/settings/existing-backups-table.php:169
msgid "Please allow time for the communications with the remote storage to complete."
msgstr "Please allow time for the communications with the remote storage to complete."

#: templates/wp-admin/settings/backupnow-modal.php:30
msgid "Include your database in the backup"
msgstr "Include your database in the backup"

#: templates/wp-admin/settings/take-backup.php:47
msgid "Time now"
msgstr "Time now"

#: admin.php:3020 admin.php:3942 admin.php:5167 admin.php:5179 admin.php:5190
#: admin.php:5434 admin.php:6398 admin.php:6409 includes/migrator-lite.php:235
#: includes/migrator-lite.php:254
msgid "Return to UpdraftPlus configuration"
msgstr "Return to UpdraftPlus configuration"

#: admin.php:988 admin.php:3972 admin.php:3987 admin.php:4019 admin.php:4836
#: includes/class-remote-send.php:735
#: templates/wp-admin/settings/existing-backups-table.php:163
#: templates/wp-admin/settings/file-backup-exclude.php:11
msgid "Delete"
msgstr "Delete"

#: admin.php:1432 admin.php:1530
msgid "Notice"
msgstr "Notice"

#: admin.php:778 admin.php:1327 admin.php:3260
msgid "Settings"
msgstr "Settings"

#: admin.php:995 admin.php:1187 central/translations-central.php:16
#: includes/updraftplus-tour.php:97
msgid "Close"
msgstr "Close"

#: admin.php:458 admin.php:4532 admin.php:4593 admin.php:5241
#: includes/class-remote-send.php:453 includes/class-wpadmin-commands.php:155
#: includes/class-wpadmin-commands.php:630 restorer.php:712
#: templates/wp-admin/settings/delete-and-restore-modals.php:90
#: templates/wp-admin/settings/delete-and-restore-modals.php:96
#: templates/wp-admin/settings/take-backup.php:34
msgid "Database"
msgstr "Database"

#: admin.php:961 class-updraftplus.php:1584 class-updraftplus.php:1628
#: includes/class-filesystem-functions.php:440
#: includes/class-storage-methods-interface.php:338
#: methods/addon-base-v2.php:100 methods/addon-base-v2.php:105
#: methods/addon-base-v2.php:239 methods/addon-base-v2.php:259
#: methods/googledrive.php:1448 restorer.php:4236 restorer.php:4261
#: restorer.php:4380 updraftplus.php:226
msgid "Error"
msgstr "Error"

#: admin.php:1001 admin.php:3499 methods/updraftvault.php:503
#: methods/updraftvault.php:545
#: templates/wp-admin/settings/temporary-clone.php:84
msgid "Connect"
msgstr "Connect"

#: admin.php:1003 methods/updraftvault.php:553 methods/updraftvault.php:636
msgid "Disconnect"
msgstr "Disconnect"

#: admin.php:987 methods/backup-module.php:91
#: templates/wp-admin/settings/delete-and-restore-modals.php:109
msgid "Cancel"
msgstr "Cancel"

#: admin.php:1405 admin.php:1410 admin.php:1416 admin.php:1420 admin.php:1424
#: admin.php:1428 admin.php:1446 admin.php:1459 admin.php:4464 admin.php:4471
#: admin.php:4473 admin.php:6186 admin.php:6487 includes/migrator-lite.php:702
#: methods/cloudfiles-new.php:298 methods/cloudfiles-new.php:299
#: methods/cloudfiles.php:455 methods/dreamobjects.php:210
#: methods/dreamobjects.php:211 methods/ftp.php:116
#: methods/openstack-base.php:577 methods/openstack2.php:244 methods/s3.php:938
#: methods/s3.php:942 methods/s3.php:1097 methods/s3.php:1098
#: methods/s3generic.php:192 methods/s3generic.php:193
#: methods/updraftvault.php:495
#: templates/wp-admin/settings/downloading-and-restoring.php:27
#: templates/wp-admin/settings/tab-backups.php:27
msgid "Warning"
msgstr "Warning"

#: includes/class-wpadmin-commands.php:400
msgid "Constants"
msgstr "Constants"

#: admin.php:104 admin.php:960 admin.php:1517
#: includes/class-remote-send.php:360 includes/class-remote-send.php:407
#: includes/class-remote-send.php:413 includes/class-remote-send.php:415
#: includes/class-remote-send.php:419 includes/class-remote-send.php:488
#: includes/class-remote-send.php:546 includes/class-remote-send.php:573
#: includes/class-remote-send.php:601 includes/class-remote-send.php:611
#: includes/class-remote-send.php:616 includes/class-remote-send.php:628
#: includes/class-search-replace.php:333 includes/class-search-replace.php:519
#: includes/migrator-lite.php:648 includes/migrator-lite.php:963
#: includes/migrator-lite.php:1041 methods/remotesend.php:91
#: methods/remotesend.php:269 methods/updraftvault.php:743 restorer.php:597
#: restorer.php:625 restorer.php:2375 restorer.php:4423
msgid "Error:"
msgstr "Error:"

#: admin.php:1020 includes/class-commands.php:965
#: includes/class-remote-send.php:484 includes/class-remote-send.php:688
msgid "Send"
msgstr "Send"

#: admin.php:989 central/translations-central.php:58
msgid "Create"
msgstr "Create"

#: admin.php:1014 templates/wp-admin/settings/temporary-clone.php:65
msgid "Key"
msgstr "Key"

#: admin.php:2384
msgid "Job deleted"
msgstr "Job deleted"

#: admin.php:2507 admin.php:2530 includes/class-commands.php:993
msgid "Start backup"
msgstr "Start backup"

#: admin.php:6030
msgid "Options (raw)"
msgstr "Options (raw)"

#: admin.php:5995
msgid "Known backups (raw)"
msgstr "Known backups (raw)"

#: admin.php:2049 includes/class-wpadmin-commands.php:615
msgid "Backup set not found"
msgstr "Backup set not found"

#: includes/class-wpadmin-commands.php:206
msgid "This multi-archive backup set appears to have the following archives missing: %s"
msgstr "This multi-archive backup set appears to have the following archives missing: %s"

#: includes/class-wpadmin-commands.php:191
msgid "File (%s) was found, but has a different size (%s) from what was expected (%s) - it may be corrupt."
msgstr "File (%s) was found, but has a different size (%s) from what was expected (%s) - it may be corrupt."

#: includes/class-wpadmin-commands.php:186
msgid "File was found, but is zero-sized (you need to re-upload it): %s"
msgstr "File was found, but is zero-sized (you need to re-upload it): %s"

#: includes/class-wpadmin-commands.php:184
msgid "File not found (you need to upload it): %s"
msgstr "File not found (you need to upload it): %s"

#: admin.php:4633 includes/class-wpadmin-commands.php:160 restorer.php:2816
msgid "Backup created by unknown source (%s) - cannot be restored."
msgstr "Backup created by unknown source (%s) - cannot be restored."

#: includes/class-wpadmin-commands.php:147
msgid "Only the WordPress database can be restored; you will need to deal with the external database manually."
msgstr "Only the WordPress database can be restored; you will need to deal with the external database manually."

#: includes/class-wpadmin-commands.php:135
msgid "You should make sure that this really is a backup set intended for use on this website, before you restore (rather than a backup set of an unrelated website)."
msgstr "You should make sure that this really is a backup set intended for use on this website, before you restore (rather than a backup set of an unrelated website)."

#: includes/class-wpadmin-commands.php:135
msgid "This backup set was not known by UpdraftPlus to be created by the current WordPress installation, but was either found in remote storage, or was sent from a remote site."
msgstr "This backup set was not known by UpdraftPlus to be created by the current WordPress installation, but was either found in remote storage, or was sent from a remote site."

#: includes/class-wpadmin-commands.php:111
msgid "No such backup set exists"
msgstr "No such backup set exists"

#: admin.php:1042
msgid "Nothing yet logged"
msgstr "Nothing yet logged"

#: admin.php:1486
msgid "%s has been chosen for remote storage, but you are not currently connected."
msgstr "%s has been chosen for remote storage, but you are not currently connected."

#: admin.php:1486 admin.php:1507 admin.php:1561 class-updraftplus.php:675
#: class-updraftplus.php:747 class-updraftplus.php:752
#: class-updraftplus.php:757
msgid "UpdraftPlus notice:"
msgstr "UpdraftPlus notice:"

#: admin.php:1459
msgid "Read this page for a guide to possible causes and how to fix it."
msgstr "Read this page for a guide to possible causes and how to fix it."

#: admin.php:1424
msgid "Please consult this FAQ if you have problems backing up."
msgstr "Please consult this FAQ if you have problems backing up."

#: admin.php:1424
msgid "Your website is hosted using the %s web server."
msgstr "Your website is hosted using the %s web server."

#: admin.php:1410 admin.php:1428 admin.php:3044 admin.php:5223 backup.php:4384
#: class-updraftplus.php:5361 templates/wp-admin/advanced/db-size.php:19
#: updraftplus.php:226
msgid "Go here for more information."
msgstr "Go here for more information."

#: admin.php:1405 class-updraftplus.php:1041
msgid "The amount of time allowed for WordPress plugins to run is very low (%s seconds) - you should increase it to avoid backup failures due to time-outs (consult your web hosting company for more help - it is the max_execution_time PHP setting; the recommended value is %s seconds or more)"
msgstr "The amount of time allowed for WordPress plugins to run is very low (%s seconds) - you should increase it to avoid backup failures due to time-outs (consult your web hosting company for more help - it is the max_execution_time PHP setting; the recommended value is %s seconds or more)"

#: admin.php:1401
msgid "To change any of the default settings of what is backed up, to configure scheduled backups, to send your backups to remote storage (recommended), and more, go to the settings tab."
msgstr "To change any of the default settings of what is backed up, to configure scheduled backups, to send your backups to remote storage (recommended), and more, go to the settings tab."

#: admin.php:1401
msgid "Welcome to UpdraftPlus!"
msgstr "Welcome to UpdraftPlus!"

#: admin.php:1352
msgid "Update Theme"
msgstr "Update Theme"

#: admin.php:1348
msgid "Update Plugin"
msgstr "Update Plugin"

#: admin.php:1285
msgid "Allowed Files"
msgstr "Allowed Files"

#: admin.php:1019 includes/class-remote-send.php:449
msgid "Testing connection..."
msgstr "Testing connection..."

#: admin.php:1016 central/translations-central.php:82
#: templates/wp-admin/settings/existing-backups-table.php:169
msgid "Deleting..."
msgstr "Deleting..."

#: admin.php:1015
msgid "key name"
msgstr "key name"

#: admin.php:1015 includes/class-remote-send.php:588
#: includes/migrator-lite.php:251 methods/addon-base-v2.php:336
#: methods/cloudfiles-new.php:181 methods/cloudfiles-new.php:186
#: methods/cloudfiles.php:542 methods/cloudfiles.php:547 methods/ftp.php:438
#: methods/ftp.php:442 methods/openstack2.php:117 methods/openstack2.php:122
#: methods/openstack2.php:127 methods/openstack2.php:132 methods/s3.php:1383
#: methods/s3.php:1387
msgid "Failure: No %s was given."
msgstr "Failure: No %s was given."

#: admin.php:1013
msgid "Please give this key a name (e.g. indicate the site it is for):"
msgstr "Please give this key a name (e.g. indicate the site it is for):"

#: admin.php:1012
msgid "You should check that the remote site is online, not firewalled, does not have security modules that may be blocking access, has UpdraftPlus version %s or later active and that the keys have been entered correctly."
msgstr "You should check that the remote site is online, not firewalled, does not have security modules that may be blocking access, has UpdraftPlus version %s or later active and that the keys have been entered correctly."

#: admin.php:1011 includes/class-remote-send.php:677
msgid "Send to site:"
msgstr "Send to site:"

#: admin.php:1010 central/translations-central.php:84
msgid "Creating..."
msgstr "Creating..."

#: admin.php:1008
msgid "Add site"
msgstr "Add site"

#: admin.php:1007
msgid "Adding..."
msgstr "Adding..."

#: admin.php:1006
msgid "Update quota count"
msgstr "Update quota count"

#: admin.php:1005
msgid "Counting..."
msgstr "Counting..."

#: admin.php:1004
msgid "Disconnecting..."
msgstr "Disconnecting..."

#: admin.php:1002
msgid "Connecting..."
msgstr "Connecting..."

#: admin.php:999 admin.php:1025 admin.php:1026
msgid "You have made changes to your settings, and not saved."
msgstr "You have made changes to your settings, and not saved."

#: admin.php:998
msgid "Automatic backup before update"
msgstr "Automatic backup before update"

#: admin.php:997 admin.php:4233
msgid "Download log file"
msgstr "Download log file"

#: admin.php:762 admin.php:996 admin.php:4742
msgid "Restore"
msgstr "Restore"

#: admin.php:994
msgid "Proceed with update"
msgstr "Proceed with update"

#: admin.php:984
msgid "The file was uploaded."
msgstr "The file was uploaded."

#: admin.php:983
msgid "Unknown server response status:"
msgstr "Unknown server response status:"

#: admin.php:982
msgid "Unknown server response:"
msgstr "Unknown server response:"

#: admin.php:981
msgid "This decryption key will be attempted:"
msgstr "This decryption key will be attempted:"

#: admin.php:980
msgid "Follow this link to attempt decryption and download the database file to your computer."
msgstr "Follow this link to attempt decryption and download the database file to your computer."

#: admin.php:979
msgid "Upload error"
msgstr "Upload error"

#: admin.php:977
msgid "Upload error:"
msgstr "Upload error:"

#: admin.php:976
msgid "(make sure that you were trying to upload a zip file previously created by UpdraftPlus)"
msgstr "(make sure that you were trying to upload a zip file previously created by UpdraftPlus)"

#: admin.php:975 includes/class-backup-history.php:542
msgid "If this is a backup created by a different backup plugin, then UpdraftPlus Premium may be able to help you."
msgstr "If this is a backup created by a different backup plugin, then UpdraftPlus Premium may be able to help you."

#: admin.php:974
msgid "However, UpdraftPlus archives are standard zip/SQL files - so if you are sure that your file has the right format, then you can rename it to match that pattern."
msgstr "However, UpdraftPlus archives are standard zip/SQL files - so if you are sure that your file has the right format, then you can rename it to match that pattern."

#: includes/class-backup-history.php:542
msgid "This file does not appear to be an UpdraftPlus backup archive (such files are .zip or .gz files which have a name like: backup_(time)_(site name)_(code)_(type).(zip|gz))."
msgstr "This file does not appear to be an UpdraftPlus backup archive (such files are .zip or .gz files which have a name like: backup_(time)_(site name)_(code)_(type).(zip|gz))."

#: admin.php:973
msgid "Raw backup history"
msgstr "Raw backup history"

#: admin.php:972
msgid "Delete Old Directories"
msgstr "Delete Old Directories"

#: admin.php:971
msgid "PHP information"
msgstr "PHP information"

#: admin.php:969
msgid "Download error: the server sent us a response which we did not understand."
msgstr "Download error: the server sent us a response which we did not understand."

#: admin.php:967
msgid "Download to your computer"
msgstr "Download to your computer"

#: admin.php:966
msgid "Delete from your web server"
msgstr "Delete from your web server"

#: admin.php:963 admin.php:2666
msgid "File ready."
msgstr "File ready."

#: admin.php:959 restorer.php:392
msgid "Error data:"
msgstr "Error data:"

#: admin.php:958
msgid "Error: the server sent us a response which we did not understand."
msgstr "Error: the server sent us a response which we did not understand."

#: admin.php:957 includes/migrator-lite.php:1075
msgid "Errors:"
msgstr "Errors:"

#: admin.php:956
msgid "Warnings:"
msgstr "Warnings:"

#: admin.php:955
msgid "Error: the server sent an empty response."
msgstr "Error: the server sent an empty response."

#: admin.php:954
msgid "Processing files - please wait..."
msgstr "Processing files - please wait..."

#: admin.php:953
msgid "Some files are still downloading or being processed - please wait."
msgstr "Some files are still downloading or being processed - please wait."

#: admin.php:952
msgid "Begun looking for this entity"
msgstr "Begun looking for this entity"

#: admin.php:951
msgid "calculating..."
msgstr "calculating..."

#: admin.php:949
msgid "Trying..."
msgstr "Trying..."

#: admin.php:948
msgid "The new user's RackSpace console password is (this will not be shown again):"
msgstr "The new user's RackSpace console password is (this will not be shown again):"

#: admin.php:947
msgid "The web server returned an error code (try again, or check your web server logs)"
msgstr "The web server returned an error code (try again, or check your web server logs)"

#: admin.php:946 central/translations-central.php:85 methods/remotesend.php:86
#: methods/remotesend.php:94 methods/remotesend.php:256
#: methods/remotesend.php:272
msgid "Unexpected response:"
msgstr "Unexpected response:"

#: admin.php:941
msgid "If you exclude both the database and the files, then you have excluded everything!"
msgstr "If you exclude both the database and the files, then you have excluded everything!"

#: admin.php:940
msgid "To send to more than one address, separate each address with a comma."
msgstr "To send to more than one address, separate each address with a comma."

#: admin.php:939
msgid "Rescanning remote and local storage for backup sets..."
msgstr "Rescanning remote and local storage for backup sets..."

#: admin.php:937
msgid "Rescanning (looking for backups that you have uploaded manually into the internal backup store)..."
msgstr "Rescanning (looking for backups that you have uploaded manually into the internal backup store)..."

#: admin.php:936
msgid "Be aware that mail servers tend to have size limits; typically around %s Mb; backups larger than any limits will likely not arrive."
msgstr "Be aware that mail servers tend to have size limits; typically around %s MB; backups larger than any limits will likely not arrive."

#: admin.php:934
msgid "Send a report only when there are warnings/errors"
msgstr "Send a report only when there are warnings/errors"

#: templates/wp-admin/notices/thanks-for-using-main-dash.php:40
msgid "Premium WooCommerce plugins"
msgstr "Premium WooCommerce plugins"

#: templates/wp-admin/notices/thanks-for-using-main-dash.php:40
msgid "Free two-factor security plugin"
msgstr "Free two-factor security plugin"

#: templates/wp-admin/notices/thanks-for-using-main-dash.php:18
#: templates/wp-admin/settings/tab-addons.php:78
msgid "UpdraftPlus Premium"
msgstr "UpdraftPlus Premium"

#: templates/wp-admin/notices/thanks-for-using-main-dash.php:2
#: templates/wp-admin/notices/thanks-for-using-main-dash.php:43
msgid "Dismiss (for %s months)"
msgstr "Dismiss (for %s months)"

#: templates/wp-admin/notices/thanks-for-using-main-dash.php:39
msgid "More quality plugins"
msgstr "More quality plugins"

#: admin.php:970
msgid "Requesting start of backup..."
msgstr "Requesting start of backup..."

#: admin.php:986 templates/wp-admin/settings/take-backup.php:52
msgid "Backup Now"
msgstr "Backup Now"

#: admin.php:1401
msgid "To make a backup, just press the Backup Now button."
msgstr "To make a backup, just press the BackUp Now button."