<?php get_header(); ?>

<!-- Page Header -->
<section class="page-header">
    <div class="container">
        <h1>All Competitions</h1>
        <p>Browse all available competitions and enter to win amazing film collectables</p>
    </div>
</section>

<!-- Competitions Grid -->
<section class="competitions-section">
    <div class="container">
        <!-- Filters -->
        <div class="filters-section">
            <div class="filters">
                <button class="filter-btn active" data-filter="all">All Competitions</button>
                <button class="filter-btn" data-filter="active">Active</button>
                <button class="filter-btn" data-filter="ending-soon">Ending Soon</button>
                <button class="filter-btn" data-filter="new">New</button>
            </div>

            <div class="sort-options">
                <select id="sort-competitions">
                    <option value="newest">Newest First</option>
                    <option value="ending-soon">Ending Soon</option>
                    <option value="price-low">Price: Low to High</option>
                    <option value="price-high">Price: High to Low</option>
                    <option value="popular">Most Popular</option>
                </select>
            </div>
        </div>
        <div class="competition-grid" id="competitions-grid">
            <?php
            // Get filter and sort parameters
            $filter = isset($_GET['filter']) ? sanitize_text_field($_GET['filter']) : 'all';
            $sort = isset($_GET['sort']) ? sanitize_text_field($_GET['sort']) : 'newest';
            
            // Build query arguments for competition products
            $args = array(
                'post_type' => 'product',
                'posts_per_page' => 12,
                'post_status' => 'publish',
                'meta_query' => array(
                    array(
                        'key' => '_is_competition',
                        'value' => 'yes',
                        'compare' => '='
                    )
                )
            );
            
            // Add meta query based on filter
            if ($filter === 'active') {
                $args['meta_query'][] = array(
                    'key' => '_competition_status',
                    'value' => 'active',
                    'compare' => '='
                );
            } elseif ($filter === 'ending-soon') {
                $args['meta_query'][] = array(
                    'key' => '_competition_status',
                    'value' => 'active',
                    'compare' => '='
                );
                $args['meta_query'][] = array(
                    'key' => '_competition_end_date',
                    'value' => date('Y-m-d H:i:s', strtotime('+7 days')),
                    'compare' => '<='
                );
            } elseif ($filter === 'new') {
                $args['date_query'] = array(
                    array(
                        'after' => '7 days ago'
                    )
                );
            }
            
            // Add sorting
            switch ($sort) {
                case 'ending-soon':
                    $args['meta_key'] = '_competition_end_date';
                    $args['orderby'] = 'meta_value';
                    $args['order'] = 'ASC';
                    break;
                case 'price-low':
                    $args['meta_key'] = 'price';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'ASC';
                    break;
                case 'price-high':
                    $args['meta_key'] = 'price';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    break;
                case 'popular':
                    $args['meta_key'] = 'sold_tickets';
                    $args['orderby'] = 'meta_value_num';
                    $args['order'] = 'DESC';
                    break;
                default: // newest
                    $args['orderby'] = 'date';
                    $args['order'] = 'DESC';
            }
            
            $competitions = new WP_Query($args);
            
            if ($competitions->have_posts()) :
                while ($competitions->have_posts()) : $competitions->the_post();
                    // Get WooCommerce product data
                    $product = wc_get_product(get_the_ID());
                    $price = $product ? $product->get_price() : 0;

                    // Get competition-specific meta
                    $max_tickets = get_post_meta(get_the_ID(), '_competition_max_tickets', true);
                    $sold_tickets = get_post_meta(get_the_ID(), '_competition_sold_tickets', true) ?: 0;

                    // Set default max_tickets if not set
                    if (empty($max_tickets)) {
                        $max_tickets = 100; // Default value
                        update_post_meta(get_the_ID(), '_competition_max_tickets', $max_tickets);
                    }
                    $end_date = get_post_meta(get_the_ID(), '_competition_end_date', true);
                    $status = get_competition_status_simple(get_the_ID());
                    
                    $progress = $max_tickets > 0 ? min(100, ($sold_tickets / $max_tickets) * 100) : 0;
                    $remaining = max(0, $max_tickets - $sold_tickets);

                    // Debug output (remove this later)
                    if (current_user_can('administrator')) {
                        echo "<!-- Debug: Product ID: " . get_the_ID() . ", Max: $max_tickets, Sold: $sold_tickets, Progress: $progress% -->";
                    }
                    
                    // Get instant wins data
                    $instant_wins_enabled = get_post_meta(get_the_ID(), '_instant_wins_enabled', true);
                    $instant_win_prizes = get_post_meta(get_the_ID(), '_instant_win_prizes', true) ?: array();

                    // Check if ending soon
                    $is_ending_soon = false;
                    if ($end_date) {
                        $time_left = strtotime($end_date) - current_time('timestamp');
                        $is_ending_soon = $time_left > 0 && $time_left <= (7 * 24 * 60 * 60); // 7 days
                    }
            ?>
                    <div class="competition-card <?php echo esc_attr($status); ?> <?php echo $is_ending_soon ? 'ending-soon' : ''; ?>" data-competition-id="<?php echo esc_attr(get_the_ID()); ?>">
                                            <?php 
                    $badge_count = 0;
                    $badge_offset = 10;
                    ?>
                    
                    <?php if ($is_ending_soon) : ?>
                        <div class="badge ending-soon-badge" style="top: <?php echo $badge_offset + ($badge_count * 35); ?>px;">Ending Soon!</div>
                        <?php $badge_count++; ?>
                    <?php endif; ?>

                    <?php if ($status === 'awaiting_draw') : ?>
                        <div class="badge status-badge awaiting-draw" style="top: <?php echo $badge_offset + ($badge_count * 35); ?>px;">⏳ Awaiting Draw</div>
                        <?php $badge_count++; ?>
                    <?php elseif ($status === 'completed') : ?>
                        <div class="badge status-badge completed" style="top: <?php echo $badge_offset + ($badge_count * 35); ?>px;">✅ Completed</div>
                        <?php $badge_count++; ?>
                    <?php elseif ($status !== 'active') : ?>
                        <div class="badge status-badge" style="top: <?php echo $badge_offset + ($badge_count * 35); ?>px;"><?php echo ucfirst(str_replace('_', ' ', $status)); ?></div>
                        <?php $badge_count++; ?>
                    <?php endif; ?>

                    <?php if ($instant_wins_enabled && !empty($instant_win_prizes)) : ?>
                        <div class="badge instant-win-badge" style="top: <?php echo $badge_offset + ($badge_count * 35); ?>px;">🎁 INSTANT WINS</div>
                    <?php endif; ?>

                        <?php if (has_post_thumbnail()) : ?>
                            <a href="<?php the_permalink(); ?>">
                                <?php the_post_thumbnail('medium', array('class' => 'competition-image')); ?>
                            </a>
                        <?php else : ?>
                            <div class="competition-image" style="background: #e5e7eb; display: flex; align-items: center; justify-content: center; color: #9ca3af;">
                                No Image
                            </div>
                        <?php endif; ?>
                        
                        <div class="card-content">
                            <h3 class="card-title">
                                <a href="<?php the_permalink(); ?>"><?php the_title(); ?></a>
                            </h3>

                            <div class="card-price">£<?php echo esc_html(number_format($price, 2)); ?></div>

                            <p class="card-description">
                                <?php echo wp_trim_words(get_the_excerpt(), 15); ?>
                            </p>

                            <!-- Winner Information (if completed) -->
                            <?php if ($status === 'completed') : ?>
                                <?php
                                $winner_name = get_post_meta(get_the_ID(), 'winner_username', true);
                                $winner_ticket = get_post_meta(get_the_ID(), 'winner_ticket_number', true);
                                ?>
                                <?php if ($winner_name && $winner_ticket) : ?>
                                    <div class="winner-info" style="background: rgba(16, 185, 129, 0.1); border: 1px solid rgba(16, 185, 129, 0.3); border-radius: 0.5rem; padding: 0.75rem; margin: 0.75rem 0; text-align: center;">
                                        <div style="font-size: 0.9rem; font-weight: bold; color: #10b981; margin-bottom: 0.25rem;">
                                            🏆 <?php echo esc_html($winner_name); ?>
                                        </div>
                                        <div style="font-size: 0.8rem; color: #059669;">
                                            Ticket #<?php echo esc_html($winner_ticket); ?>
                                        </div>
                                    </div>
                                <?php endif; ?>
                            <?php else : ?>
                                <div class="progress-container">
                                    <div class="progress-info">
                                        <span class="sold-tickets"><?php echo esc_html($sold_tickets); ?> sold</span>
                                        <span class="remaining-tickets"><?php echo esc_html($remaining); ?> remaining</span>
                                    </div>
                                    <div class="progress-bar">
                                        <div class="progress-fill" style="width: <?php echo esc_attr($progress); %>%; min-width: <?php echo $sold_tickets > 0 ? '2px' : '0'; ?>;"></div>
                                    </div>
                                </div>
                            <?php endif; ?>

                            <!-- Countdown Timer -->
                            <?php if ($end_date && $status === 'active') : ?>
                                <div class="competition-countdown" data-end-date="<?php echo esc_attr($end_date); ?>">
                                    <!-- Countdown will be populated by JavaScript -->
                                </div>
                            <?php elseif ($status === 'awaiting_draw') : ?>
                                <div class="countdown-expired" style="background: linear-gradient(135deg, #f59e0b, #d97706); color: white;">⏳ Awaiting Draw</div>
                            <?php elseif ($status === 'completed') : ?>
                                <div class="countdown-expired" style="background: linear-gradient(135deg, #8b5cf6, #7c3aed); color: white;">✅ Completed</div>
                            <?php elseif ($status !== 'active') : ?>
                                <div class="countdown-expired">Competition <?php echo ucfirst($status); ?></div>
                            <?php endif; ?>

                            <div class="card-actions">
                                <?php if ($status === 'active') : ?>
                                    <a href="<?php the_permalink(); ?>" class="btn">Enter Now</a>
                                <?php else : ?>
                                    <a href="<?php the_permalink(); ?>" class="btn btn-secondary">View Details</a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
            <?php
                endwhile;
                wp_reset_postdata();
            else :
            ?>
                <div style="grid-column: 1 / -1; text-align: center; padding: 3rem;">
                    <h3>No Competitions Found</h3>
                    <p>Check back soon for new competitions!</p>
                </div>
            <?php endif; ?>
        </div>
        
        <!-- Pagination -->
        <div class="pagination">
            <?php
            echo paginate_links(array(
                'total' => $competitions->max_num_pages,
                'prev_text' => '← Previous',
                'next_text' => 'Next →',
            ));
            ?>
        </div>
    </div>
</section>

<script>
// Filter and sort functionality
document.addEventListener('DOMContentLoaded', function() {
    const filterBtns = document.querySelectorAll('.filter-btn');
    const sortSelect = document.getElementById('sort-competitions');
    
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            
            const filter = this.dataset.filter;
            const sort = sortSelect.value;
            updateURL(filter, sort);
        });
    });
    
    sortSelect.addEventListener('change', function() {
        const activeFilter = document.querySelector('.filter-btn.active').dataset.filter;
        const sort = this.value;
        updateURL(activeFilter, sort);
    });
    
    function updateURL(filter, sort) {
        const url = new URL(window.location);
        url.searchParams.set('filter', filter);
        url.searchParams.set('sort', sort);
        window.location.href = url.toString();
    }
});
</script>

<?php get_footer(); ?>
