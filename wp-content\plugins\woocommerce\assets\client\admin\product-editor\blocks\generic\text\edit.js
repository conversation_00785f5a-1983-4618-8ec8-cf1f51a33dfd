"use strict";var __importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.Edit=Edit;const block_templates_1=require("@woocommerce/block-templates"),compose_1=require("@wordpress/compose"),components_1=require("@woocommerce/components"),element_1=require("@wordpress/element"),i18n_1=require("@wordpress/i18n"),icons_1=require("@wordpress/icons"),text_control_1=require("../../../components/text-control"),validation_context_1=require("../../../contexts/validation-context"),use_product_edits_1=require("../../../hooks/use-product-edits"),use_product_entity_prop_1=__importDefault(require("../../../hooks/use-product-entity-prop"));function Edit({attributes:e,context:{postType:t}}){const o=(0,block_templates_1.useWooBlockProps)(e),{property:i,label:r,placeholder:n,required:l,pattern:s,minLength:a,maxLength:u,min:c,max:_,help:m,tooltip:d,disabled:p,type:f,suffix:v}=e,[h,y]=(0,use_product_entity_prop_1.default)(i,{postType:t,fallbackValue:""}),{hasEdit:x}=(0,use_product_edits_1.useProductEdits)(),g=(0,element_1.useRef)(null),{error:w,validate:q,ref:b}=(0,validation_context_1.useValidation)(i,(async function(){if(!g.current)return;const e=g.current;let t="";return e.validity.typeMismatch&&(t=f?.message??(0,i18n_1.__)("Invalid value for the field.","woocommerce")),e.validity.valueMissing&&(t="string"==typeof l?l:(0,i18n_1.__)("This field is required.","woocommerce")),e.validity.patternMismatch&&(t=s?.message??(0,i18n_1.__)("Invalid value for the field.","woocommerce")),e.validity.tooShort&&(t=(0,i18n_1.sprintf)(a?.message??(0,i18n_1.__)("The minimum length of the field is %d","woocommerce"),a?.value)),e.validity.tooLong&&(t=(0,i18n_1.sprintf)(u?.message??(0,i18n_1.__)("The maximum length of the field is %d","woocommerce"),u?.value)),e.validity.rangeUnderflow&&(t=(0,i18n_1.sprintf)(c?.message??(0,i18n_1.__)("The minimum value of the field is %d","woocommerce"),c?.value)),e.validity.rangeOverflow&&(t=(0,i18n_1.sprintf)(_?.message??(0,i18n_1.__)("The maximum value of the field is %d","woocommerce"),_?.value)),e.setCustomValidity(t),e.validity.valid?void 0:{message:t}}),[f,l,s,a,u,c,_,h]);return(0,element_1.createElement)("div",{...o},(0,element_1.createElement)(text_control_1.TextControl,{ref:(0,compose_1.useMergeRefs)([g,b]),type:f?.value??"text",value:h,disabled:p,label:r,onChange:y,onBlur:()=>{x(i)&&q()},error:w,help:m,placeholder:n,tooltip:d,suffix:function(){if(!v||!h||!g.current)return;const e="url"===g.current.type&&!g.current.validity.typeMismatch;return!0===v&&e?(0,element_1.createElement)(components_1.Link,{type:"external",href:h,target:"_blank",rel:"noreferrer",className:"wp-block-woocommerce-product-text-field__suffix-link"},(0,element_1.createElement)(icons_1.Icon,{icon:icons_1.external,size:20})):"string"==typeof v?v:void 0}(),required:Boolean(l),pattern:s?.value,minLength:a?.value,maxLength:u?.value,min:c?.value,max:_?.value}))}