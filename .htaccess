
# Cache Busting Rules - Remove version numbers from filenames
<IfModule mod_rewrite.c>
RewriteEngine On

# Remove version numbers from CSS files (style.123456.css -> style.css)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.+)\.(\d+)\.(css)$ $1.$3 [L]

# Remove version numbers from JS files (script.123456.js -> script.js)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.+)\.(\d+)\.(js)$ $1.$3 [L]

# Remove version numbers from image files (image.123456.jpg -> image.jpg)
RewriteCond %{REQUEST_FILENAME} !-f
RewriteRule ^(.+)\.(\d+)\.(jpg|jpeg|png|gif|webp|svg)$ $1.$3 [L]
</IfModule>

# Cache Control Headers
<IfModule mod_expires.c>
ExpiresActive On
# Cache static assets for 1 year
ExpiresByType text/css "access plus 1 year"
ExpiresByType application/javascript "access plus 1 year"
ExpiresByType image/png "access plus 1 year"
ExpiresByType image/jpg "access plus 1 year"
ExpiresByType image/jpeg "access plus 1 year"
ExpiresByType image/gif "access plus 1 year"
ExpiresByType image/webp "access plus 1 year"
ExpiresByType image/svg+xml "access plus 1 year"
</IfModule>

# ETag and Last-Modified Headers
<IfModule mod_headers.c>
# Enable ETags for better cache validation
FileETag MTime Size

# Add cache-control headers
<FilesMatch "\.(css|js|png|jpg|jpeg|gif|webp|svg)$">
    Header set Cache-Control "public, max-age=31536000, immutable"
    Header unset Pragma
    Header unset Expires
</FilesMatch>

# For HTML files, use shorter cache with validation
<FilesMatch "\.(html|htm|php)$">
    Header set Cache-Control "public, max-age=3600, must-revalidate"
</FilesMatch>

# Exclude admin-ajax.php from caching to prevent AJAX response caching
<Files "admin-ajax.php">
    Header set Cache-Control "no-cache, no-store, must-revalidate"
    Header set Pragma "no-cache"
    Header set Expires "0"
</Files>
</IfModule>

# BEGIN WordPress
# The directives (lines) between "BEGIN WordPress" and "END WordPress" are
# dynamically generated, and should only be modified via WordPress filters.
# Any changes to the directives between these markers will be overwritten.
<IfModule mod_rewrite.c>
RewriteEngine On
RewriteRule .* - [E=HTTP_AUTHORIZATION:%{HTTP:Authorization}]
RewriteBase /film-collectables/
RewriteRule ^index\.php$ - [L]
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule . /film-collectables/index.php [L]
</IfModule>

# END WordPress