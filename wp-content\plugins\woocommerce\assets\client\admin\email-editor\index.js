(()=>{"use strict";var e,t={384:(e,t,o)=>{var n={};o.r(n),o.d(n,{requestSendingNewsletterPreview:()=>ao,setIsFetchingPersonalizationTags:()=>co,setPersonalizationTagsList:()=>mo,setTemplateToPost:()=>lo,togglePreviewModal:()=>so,updateSendPreviewEmail:()=>io});var r={};o.r(r),o.d(r,{canUserEditGlobalEmailStyles:()=>Po,canUserEditTemplates:()=>ko,getBlockPatternsForEmailTemplate:()=>jo,getCurrentTemplate:()=>Eo,getCurrentTemplateContent:()=>To,getEditedEmailContent:()=>bo,getEditedPostTemplate:()=>Co,getEmailPostId:()=>Mo,getEmailTemplates:()=>Bo,getGlobalEmailStylesPost:()=>No,getGlobalStylesPostId:()=>Ho,getInitialEditorSettings:()=>Io,getPaletteColors:()=>zo,getPersonalizationTagsList:()=>Lo,getPersonalizationTagsState:()=>Ao,getPreviewState:()=>Fo,getSentEmailEditorPosts:()=>vo,getStyles:()=>Ro,getTheme:()=>Oo,getUrls:()=>Vo,hasEdits:()=>xo,hasEmptyContent:()=>wo,isEmailSent:()=>fo,isFeatureActive:()=>yo});var s={};o.r(s),o.d(s,{getPersonalizationTagsList:()=>Do});const i=window.wp.data,l=window.wp.element,a=window.wp.hooks,c=(window.wp.formatLibrary,window.wp.blockLibrary),d=window.wp.blockEditor,m=window.wp.compose,p=window.ReactJSXRuntime,u=(0,m.createHigherOrderComponent)((e=>function(t){return"core/columns"!==t.name?(0,p.jsx)(e,{...t}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(e,{...t}),(0,p.jsx)(d.InspectorControls,{children:(0,p.jsx)("style",{children:"\n      .components-panel__body .components-toggle-control .components-form-toggle { opacity: 0.3; }\n      .components-panel__body .components-toggle-control .components-form-toggle__input { pointer-events: none; }\n      .components-panel__body .components-toggle-control label { pointer-events: none; }\n    "})})]})}),"columnsEditCallback"),_=window.wp.i18n;function g({layoutClassNames:e}){const t=(0,d.useBlockProps)({className:e});return(0,p.jsxs)("div",{...t,children:[(0,p.jsx)("p",{children:(0,_.__)("This is the Content block.","woocommerce")}),(0,p.jsx)("p",{children:(0,_.__)("It will display all the blocks in the email content, which might be only simple text paragraphs. You can enrich your message with images, incorporate data through tables, explore different layout designs with columns, or use any other block type.","woocommerce")})]})}const h=(0,m.createHigherOrderComponent)((e=>function(t){return"core/image"!==t.name?(0,p.jsx)(e,{...t}):(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(e,{...t}),(0,p.jsx)(d.InspectorControls,{children:(0,p.jsx)("style",{children:"\n        .components-tools-panel .components-toggle-control { display: none; }\n      "})})]})}),"imageEditCallback"),y=window.wp.richText,x=window.wp.components,w=(e,t)=>{const o=e.current.ownerDocument.defaultView.getSelection();if(!o.rangeCount)return{start:0,end:0};const n=o.getRangeAt(0);if(null===o.anchorNode.previousSibling)return{start:o.anchorOffset,end:o.anchorOffset+n.toString().length};const r=(0,y.create)({html:t});let s=o.anchorNode.previousSibling;s=function(e){let t=e;for(;t&&t?.children?.length>0;)t=t.children[0];return t}(s);const i=function(e,t){let o=null;for(const[n,r]of t.entries())if(r)for(const t of r)t?.attributes&&e.tagName.toLowerCase()===t.tagName?.toLowerCase()&&e.getAttribute("data-link-href")===t?.attributes["data-link-href"]&&(o=n);return o}(s,r.formats);if(null!==i)return{start:i+o.anchorOffset+1,end:i+o.anchorOffset+n.toString().length};const l=function(e,t){for(const[o,n]of t.entries()){if(!n)continue;const{attributes:t}=n;if(e.getAttribute("data-rich-text-comment")===t["data-rich-text-comment"])return o}return null}(s,r.replacements);return null!==l?{start:l+o.anchorOffset+1,end:l+o.anchorOffset+n.toString().length}:{start:r.text.length,end:r.text.length+n.toString().length}},f=(e,t)=>(t.forEach((t=>{if(!e.includes(t.token.slice(0,t.token.length-1)))return;const o=t.token.substring(1,t.token.length-1).replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),n=new RegExp(`(?<!\x3c!--)(?<!["'])\\[(${o}(\\s[^\\]]*)?)\\](?!--\x3e)`,"g");e=e.replace(n,(e=>`\x3c!--${e}--\x3e`))})),e),b=({groupedTags:e,activeCategory:t,onCategorySelect:o})=>{const n=e=>e===t?"woocommerce-personalization-tags-modal-menu-item-active":"";return(0,p.jsxs)(x.MenuGroup,{className:"woocommerce-personalization-tags-modal-menu",children:[(0,p.jsx)(x.MenuItem,{onClick:()=>o(null),className:n(null),children:(0,_.__)("All","woocommerce")}),(0,p.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"}),Object.keys(e).map(((e,t,r)=>(0,p.jsxs)(l.Fragment,{children:[(0,p.jsx)(x.MenuItem,{onClick:()=>o(e),className:n(e),children:e}),t<r.length-1&&(0,p.jsx)("div",{className:"woocommerce-personalization-tags-modal-menu-separator","aria-hidden":"true",role:"presentation","data-testid":"woocommerce-personalization-tags-modal-menu-separator"})]},e)))]})},v=({groupedTags:e,activeCategory:t,onInsert:o,canInsertLink:n,closeCallback:r,openLinkModal:s})=>{const{updateBlockAttributes:l}=(0,i.useDispatch)(d.store),a=(0,i.useSelect)((e=>e(d.store).getSelectedBlockClientId())),c=(0,i.useSelect)((e=>e(d.store).getBlock(a))),m=["core/button"].includes(c?.name),u=null===t?Object.entries(e):[[t,e[t]||[]]];return(0,p.jsx)(p.Fragment,{children:u.map((([e,t])=>(0,p.jsxs)("div",{children:[(0,p.jsx)("div",{className:"woocommerce-personalization-tags-modal-category",children:e}),(0,p.jsx)("div",{className:"woocommerce-personalization-tags-modal-category-group",children:t.map((t=>{const i=/\burl\b/.test(t.token);return(0,p.jsxs)("div",{className:"woocommerce-personalization-tags-modal-category-group-item",children:[(0,p.jsxs)("div",{className:"woocommerce-personalization-tags-modal-item-text",children:[(0,p.jsx)("strong",{children:t.name}),t.valueToInsert]}),(0,p.jsxs)("div",{style:{display:"flex",flexDirection:"column",alignItems:"flex-end"},children:[(0,p.jsx)(x.Button,{variant:"link",onClick:()=>{o&&o(t.valueToInsert,!1)},children:(0,_.__)("Insert","woocommerce")}),m&&i&&(0,p.jsx)(x.Button,{variant:"link",onClick:()=>{l(a,{url:t.valueToInsert}),r()},children:(0,_.__)("Set as URL","woocommerce")}),e===(0,_.__)("Link","woocommerce")&&n&&(0,p.jsx)(p.Fragment,{children:(0,p.jsx)(x.Button,{variant:"link",onClick:()=>{r(),s(t)},children:(0,_.__)("Insert as link","woocommerce")})})]})]},t.token)}))})]},e)))})},j=({onInsert:e,isOpened:t,closeCallback:o,tag:n})=>{const[r,s]=(0,l.useState)((0,_.__)("Link","woocommerce"));return t?(0,p.jsxs)(x.Modal,{size:"small",title:(0,_.__)("Insert Link","woocommerce"),onRequestClose:o,className:"woocommerce-personalization-tags-modal",children:[(0,p.jsx)(x.TextControl,{label:(0,_.__)("Link Text","woocommerce"),value:r,onChange:s}),(0,p.jsx)(x.Button,{isPrimary:!0,onClick:()=>{e&&e(n.token,r)},children:(0,_.__)("Insert","woocommerce")})]}):null},k=window.lodash,S=(0,a.applyFilters)("woocommerce_email_editor_events_tracking_enabled",!1),C="email_editor_events",E=new EventTarget,T=(e,t={})=>{if(!S)return;const o={name:`${C}_${e}`,..."object"!=typeof t?{data:t}:t};E.dispatchEvent(new CustomEvent(C,{detail:o}))},P=function(){const e={};return(t,o={})=>{if(!S)return;const n=`${t}_${JSON.stringify(o).length}`;e[n]||(T(t,o),e[n]=!0)}}(),N=(0,k.debounce)(T,700),B="email-editor/editor",M=window.WooCommerceEmailEditor.current_post_type,I=window.WooCommerceEmailEditor.current_post_id,z=({onInsert:e,isOpened:t,closeCallback:o,canInsertLink:n=!1,openedBy:r=""})=>{const[s,a]=(0,l.useState)(null),[c,d]=(0,l.useState)(""),[m,u]=(0,l.useState)(null),[g,h]=(0,l.useState)(!1),y=(0,i.useSelect)((e=>e(B).getPersonalizationTagsList()),[]);if(g)return(0,p.jsx)(j,{onInsert:(t,o)=>{e(t,o),h(!1)},isOpened:g,closeCallback:()=>h(!1),tag:m});if(!t)return null;P("personalization_tags_modal_opened",{openedBy:r});const w=y.reduce(((e,t)=>{const{category:o,name:n,token:r}=t;return(!c||n.toLowerCase().includes(c.toLowerCase())||r.toLowerCase().includes(c.toLowerCase()))&&(e[o]||(e[o]=[]),e[o].push(t)),e}),{});return(0,p.jsxs)(x.Modal,{size:"medium",title:(0,_.__)("Personalization Tags","woocommerce"),onRequestClose:()=>{o(),T("personalization_tags_modal_closed",{openedBy:r})},className:"woocommerce-personalization-tags-modal",children:[(0,p.jsxs)("p",{children:[(0,_.__)("Insert personalization tags to dynamically fill in information and personalize your emails.","woocommerce")," ",(0,p.jsx)(x.ExternalLink,{href:"https://kb.mailpoet.com/article/435-a-guide-to-personalisation-tags-for-tailored-newsletters#list",onClick:()=>T("personalization_tags_modal_learn_more_link_clicked",{openedBy:r}),children:(0,_.__)("Learn more","woocommerce")})]}),(0,p.jsx)(x.SearchControl,{onChange:e=>{d(e),P("personalization_tags_modal_search_control_input_updated",{openedBy:r})},value:c}),(0,p.jsx)(b,{groupedTags:w,activeCategory:s,onCategorySelect:e=>{a(e),T("personalization_tags_modal_category_menu_clicked",{category:e,openedBy:r})}}),(0,p.jsx)(v,{groupedTags:w,activeCategory:s,onInsert:t=>{e(t),T("personalization_tags_modal_tag_insert_button_clicked",{insertedTag:t,activeCategory:s,openedBy:r})},closeCallback:o,canInsertLink:n,openLinkModal:e=>{u(e),h(!0)}})]})},F=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(null),[i,a]=(0,l.useState)(""),[c,d]=(0,l.useState)("");return(0,l.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("span[data-rich-text-comment]");if(t){const e=t.innerText.replace(/^\[|\]$/g,"");d(e),a(e),s(t),n(!0)}};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,p.jsx)(p.Fragment,{children:o&&r&&(0,p.jsx)(x.Popover,{position:"bottom right",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,p.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,p.jsx)(x.TextControl,{label:(0,_.__)("Personalization Tag","woocommerce"),value:i,onChange:e=>a(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0}),(0,p.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,p.jsx)(x.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,_.__)("Cancel","woocommerce")}),(0,p.jsx)(x.Button,{isPrimary:!0,onClick:()=>{t(c,i),n(!1)},children:(0,_.__)("Update","woocommerce")})]})]})})})},A=({contentRef:e,onUpdate:t})=>{const[o,n]=(0,l.useState)(!1),[r,s]=(0,l.useState)(null),[a,c]=(0,l.useState)(""),[d,m]=(0,l.useState)(""),u=(0,i.useSelect)((e=>e(B).getPersonalizationTagsList()),[]);return(0,l.useEffect)((()=>{if(!e||!e.current)return;const t=e.current,o=e=>{const t=e.target.closest("a[data-link-href]");t&&(s(t),m(t.getAttribute("data-link-href")||""),c(t.textContent||""),n(!0))};return t.addEventListener("click",o),()=>{t.removeEventListener("click",o)}}),[e]),(0,p.jsx)(p.Fragment,{children:o&&r&&(0,p.jsx)(x.Popover,{position:"bottom left",onClose:()=>n(!1),anchor:r,className:"woocommerce-personalization-tag-popover",children:(0,p.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content",children:[(0,p.jsx)(x.TextControl,{label:(0,_.__)("Link Text","woocommerce"),value:a,onChange:e=>c(e),__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,autoComplete:"off"}),(0,p.jsx)(x.SelectControl,{__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,label:(0,_.__)("Link tag","woocommerce"),value:d,onChange:e=>{m(e)},options:u.filter((e=>e.category===(0,_.__)("Link","woocommerce"))).map((e=>({label:e.name,value:e.token})))}),(0,p.jsxs)("div",{className:"woocommerce-personalization-tag-popover-content-buttons",children:[(0,p.jsx)(x.Button,{isTertiary:!0,onClick:()=>{n(!1)},children:(0,_.__)("Cancel","woocommerce")}),(0,p.jsx)(x.Button,{isPrimary:!0,onClick:()=>{n(!1),t(r,d,a)},children:(0,_.__)("Update link","woocommerce")})]})]})})})};function L({contentRef:e}){const[t,o]=(0,l.useState)(!1),n=(0,i.useSelect)((e=>e("core/block-editor").getSelectedBlockClientId())),{updateBlockAttributes:r}=(0,i.useDispatch)("core/block-editor"),s=(0,i.useSelect)((e=>e("core/block-editor").getBlockAttributes(n))),a="text"in s?"text":"content",c=s?.[a]?.originalHTML||s?.[a]||"",m=(0,l.useCallback)(((t,o)=>{let{start:s,end:i}=w(e,c),l="";if(o){let e=(0,y.create)({html:c});e=(0,y.insert)(e,o,s,i),i=s+o.length,e=(0,y.applyFormat)(e,{type:"woocommerce-email-editor/link-shortcode",attributes:{"data-link-href":t,contenteditable:"false",style:"text-decoration: underline;"}},s,i),l=(0,y.toHTMLString)({value:e})}else{let e=(0,y.create)({html:c});e=(0,y.insert)(e,(0,y.create)({html:`\x3c!--${t}--\x3e&nbsp;`}),s,i),l=(0,y.toHTMLString)({value:e})}r(n,{[a]:l})}),[c,a,e,n,r]);return(0,p.jsx)(d.BlockControls,{children:(0,p.jsxs)(x.ToolbarGroup,{children:[(0,p.jsx)(x.ToolbarButton,{icon:"shortcode",title:(0,_.__)("Personalization Tags","woocommerce"),onClick:()=>{o(!0),T("block_controls_personalization_tags_button_clicked")}}),(0,p.jsx)(F,{contentRef:e,onUpdate:(e,t)=>{const o=c.replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);r(n,{[a]:o})}}),(0,p.jsx)(A,{contentRef:e,onUpdate:(e,t,o)=>{const s=e.getAttribute("data-link-href").replace(/[.*+?^${}()|[\]\\]/g,"\\$&"),i=new RegExp(`<a([^>]*?)data-link-href="${s}"([^>]*?)>${e.textContent}</a>`,"gi"),l=c.replace(i,((e,n,r)=>`<a${n}data-link-href="${t}"${r}>${o}</a>`));r(n,{content:l})}}),(0,p.jsx)(z,{isOpened:t,onInsert:(e,t)=>{m(e,t),o(!1)},closeCallback:()=>o(!1),canInsertLink:!0,openedBy:"block-controls"})]})})}const R=(0,m.createHigherOrderComponent)((e=>t=>{const{attributes:o,setAttributes:n,name:r}=t,{content:s}=o,a=(0,i.useSelect)((e=>e(B).getPersonalizationTagsList()),[]),c=(0,l.useCallback)((()=>s?f(s,a):""),[s,a]),d=(0,l.useCallback)((e=>{if(void 0!==e.content){const t=f(e.content,a);n({...e,content:t})}else n(e)}),[a,n]);return"core/paragraph"===r||"core/heading"===r||"core/list-item"===r?(0,p.jsx)(e,{...t,attributes:{...o,content:c()},setAttributes:d}):(0,p.jsx)(e,{...t})}),"personalizationTagsLiveContentUpdate"),O=e=>t=>{const{setAttributes:o}=t,n=(0,l.useCallback)((e=>{e?.url&&e.url?.startsWith("http://[")&&(e.url=e.url.replace("http://[","[")),o(e)}),[o]);return(0,p.jsx)(e,{...t,setAttributes:n})},H=window.wp.blocks,V=["behance","bluesky","chain","discord","facebook","feed","github","gravatar","instagram","linkedin","mail","mastodon","medium","patreon","pinterest","reddit","spotify","telegram","threads","tiktok","tumblr","twitch","twitter","vimeo","wordpress","whatsapp","x","youtube"],D=e=>t=>{if("core/social-links"!==t.name)return(0,p.jsx)(e,{...t});const o=`\n\t\t.block-editor-tools-panel-color-gradient-settings__item:has([title="${(0,_.__)("Icon color")}"]) {\n\t\t\tdisplay: none !important;\n\t\t}\n\t\t.block-editor-tools-panel-color-gradient-settings__item:nth-child(2 of .block-editor-tools-panel-color-gradient-settings__item){\n\t\t\tborder-top:1px solid #ddd;\n\t\t\tborder-top-left-radius:2px;\n\t\t\tborder-top-right-radius:2px;\n\t\t}\n\t\t`;return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(e,{...t}),(0,p.jsx)(d.InspectorControls,{group:"color",children:(0,p.jsx)("style",{children:o})})]})},G=window.wp.privateApis,$=window.wp.editor,{unlock:W}=(0,G.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/edit-site"),{ColorPanel:U}=W(d.privateApis),{useGlobalStylesOutputWithConfig:q}=W(d.privateApis),{Editor:Z,FullscreenMode:J,ViewMoreMenuGroup:Y,BackButton:K}=W($.privateApis),{registerEntityAction:X,unregisterEntityAction:Q}=W((0,i.dispatch)($.store)),ee=window.wp.coreData,te=window.wp.notices,oe=window.wp.primitives,ne=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 5.5A2.25 2.25 0 0 0 9.878 7h4.244A2.251 2.251 0 0 0 12 5.5ZM12 4a3.751 3.751 0 0 0-3.675 3H5v1.5h1.27l.818 8.997a2.75 2.75 0 0 0 2.739 2.501h4.347a2.75 2.75 0 0 0 2.738-2.5L17.73 8.5H19V7h-3.325A3.751 3.751 0 0 0 12 4Zm4.224 4.5H7.776l.806 8.861a1.25 1.25 0 0 0 1.245 1.137h4.347a1.25 1.25 0 0 0 1.245-1.137l.805-8.861Z"})}),re=window.wp.htmlEntities,se=(0,a.applyFilters)("woocommerce_email_editor_trash_modal_should_permanently_delete",!1);function ie(e){return"string"==typeof e.title?(0,re.decodeEntities)(e.title):e.title&&"rendered"in e.title?(0,re.decodeEntities)(e.title.rendered):e.title&&"raw"in e.title?(0,re.decodeEntities)(e.title.raw):""}function le(e){return se?e.length>1?(0,_.sprintf)((0,_._n)("Are you sure you want to permanently delete %d item?","Are you sure you want to permanently delete %d items?",e.length,"woocommerce"),e.length):(0,_.sprintf)((0,_.__)('Are you sure you want to permanently delete "%s"?',"woocommerce"),(0,re.decodeEntities)(ie(e[0]))):e.length>1?(0,_.sprintf)((0,_._n)("Are you sure you want to move %d item to the trash ?","Are you sure you want to move %d items to the trash ?",e.length,"woocommerce"),e.length):(0,_.sprintf)((0,_.__)('Are you sure you want to move "%s" to the trash?',"woocommerce"),ie(e[0]))}const ae={id:"trash-email-post",label:se?(0,_.__)("Permanently delete","woocommerce"):(0,_.__)("Move to trash","woocommerce"),supportsBulk:!0,icon:ne,isEligible(e){if("wp_template"===e.type||"wp_template_part"===e.type||"wp_block"===e.type)return!1;const{permissions:t}=e;return t?.delete},hideModalHeader:!0,modalFocusOnMount:"firstContentElement",RenderModal:({items:e,closeModal:t,onActionPerformed:o})=>{const[n,r]=(0,l.useState)(!1),{createSuccessNotice:s,createErrorNotice:a}=(0,i.useDispatch)(te.store),{deleteEntityRecord:c}=(0,i.useDispatch)(ee.store),{urls:d}=(0,i.useSelect)((e=>({urls:e(B).getUrls()})),[]);return(0,p.jsxs)(x.__experimentalVStack,{spacing:"5",children:[(0,p.jsx)(x.__experimentalText,{children:le(e)}),(0,p.jsxs)(x.__experimentalHStack,{justify:"right",children:[(0,p.jsx)(x.Button,{variant:"tertiary",onClick:()=>{t?.(),T("trash_modal_cancel_button_clicked")},disabled:n,__next40pxDefaultSize:!0,children:(0,_.__)("Cancel","woocommerce")}),(0,p.jsx)(x.Button,{variant:"primary",onClick:async()=>{T("trash_modal_move_to_trash_button_clicked"),r(!0);const n=await Promise.allSettled(e.map((e=>c("postType",e.type,e.id,{force:se},{throwOnError:!0}))));if(n.every((({status:e})=>"fulfilled"===e))){let t;t=1===n.length?se?(0,_.sprintf)((0,_.__)('"%s" permanently deleted.',"woocommerce"),ie(e[0])):(0,_.sprintf)((0,_.__)('"%s" moved to the trash.',"woocommerce"),ie(e[0])):se?(0,_.__)("The items were permanently deleted.","woocommerce"):(0,_.sprintf)((0,_._n)("%s item moved to the trash.","%s items moved to the trash.",e.length,"woocommerce"),e.length),s(t,{type:"snackbar",id:"trash-email-post-action"}),o?.(e),d?.listings&&(window.location.href=d.listings)}else{let e;if(1===n.length){const t=n[0];e=t.reason?.message?t.reason.message:(0,_.__)("An error occurred while performing the action.","woocommerce")}else{const t=new Set,o=n.filter((({status:e})=>"rejected"===e));for(const e of o){const o=e;o.reason?.message&&t.add(o.reason.message)}e=0===t.size?(0,_.__)("An error occurred while performing the action.","woocommerce"):1===t.size?(0,_.sprintf)((0,_.__)("An error occurred while performing the action: %s","woocommerce"),[...t][0]):(0,_.sprintf)((0,_.__)("Some errors occurred while performing the action: %s","woocommerce"),[...t].join(","))}T("trash_modal_move_to_trash_error",{errorMessage:e}),a(e,{type:"snackbar"})}r(!1),t?.()},isBusy:n,disabled:n,__next40pxDefaultSize:!0,children:se?(0,_.__)("Delete permanently","woocommerce"):(0,_.__)("Move to trash","woocommerce")})]})]})}},ce=e=>{Q("postType",e,"move-to-trash"),X("postType",e,ae)};function de(e){var t,o,n="";if("string"==typeof e||"number"==typeof e)n+=e;else if("object"==typeof e)if(Array.isArray(e)){var r=e.length;for(t=0;t<r;t++)e[t]&&(o=de(e[t]))&&(n&&(n+=" "),n+=o)}else for(o in e)e[o]&&(n&&(n+=" "),n+=o);return n}const me=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M9 9v6h11V9H9zM4 20h1.5V4H4v16z"})}),pe=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M12.5 15v5H11v-5H4V9h7V4h1.5v5h7v6h-7Z"})}),ue=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M4 15h11V9H4v6zM18.5 4v16H20V4h-1.5z"})}),_e="__experimentalEmailFlexLayout";function ge(e){return(0,H.hasBlockSupport)(e,_e)}function he({justificationValue:e,onChange:t,isToolbar:o=!1}){const n=[{value:"left",icon:me,label:(0,_.__)("Justify items left","woocommerce")},{value:"center",icon:pe,label:(0,_.__)("Justify items center","woocommerce")},{value:"right",icon:ue,label:(0,_.__)("Justify items right","woocommerce")}];if(o){const o=n.map((e=>e.value));return(0,p.jsx)(d.JustifyContentControl,{value:e,onChange:t,allowedControls:o,popoverProps:{placement:"bottom-start"}})}return(0,p.jsx)(x.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,label:(0,_.__)("Justification","woocommerce"),value:e,onChange:t,className:"block-editor-hooks__flex-layout-justification-controls",children:n.map((({value:e,icon:t,label:o})=>(0,p.jsx)(x.__experimentalToggleGroupControlOptionIcon,{value:e,icon:t,label:o},e)))})}function ye({setAttributes:e,attributes:t,name:o}){if(!(0,H.getBlockSupport)(o,_e,{}))return null;const{justifyContent:n="left"}=t.layout||{},r=o=>{e({layout:{...t.layout,justifyContent:o}})};return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(d.InspectorControls,{children:(0,p.jsx)(x.PanelBody,{title:(0,_.__)("Layout","woocommerce"),children:(0,p.jsx)(x.Flex,{children:(0,p.jsx)(x.FlexItem,{children:(0,p.jsx)(he,{justificationValue:n,onChange:r})})})})}),(0,p.jsx)(d.BlockControls,{group:"block",__experimentalShareWithChildBlocks:!0,children:(0,p.jsx)(he,{justificationValue:n,onChange:r,isToolbar:!0})})]})}function xe(e){return ge(e.name)?{...e,attributes:{...e.attributes,layout:{type:"object"}}}:e}const we=(0,m.createHigherOrderComponent)((e=>t=>[ge(t.name)&&(0,p.jsx)(ye,{...t},"layout"),(0,p.jsx)(e,{...t},"edit")]),"withLayoutControls");function fe({block:e,props:t}){const{attributes:o}=t,{layout:n}=o,r=function(){for(var e,t,o=0,n="",r=arguments.length;o<r;o++)(e=arguments[o])&&(t=de(e))&&(n&&(n+=" "),n+=t);return n}(`is-content-justification-${n?.justifyContent||"left"}`,"is-layout-email-flex is-layout-flex");return(0,p.jsx)(e,{...t,className:r})}const be=(0,m.createHigherOrderComponent)((e=>function(t){return ge(t.name)?(0,p.jsx)(fe,{block:e,props:t}):(0,p.jsx)(e,{...t})}),"withLayoutStyles"),ve=window.wp.commands;var je=o(192),ke=o.n(je);function Se(){const{globalStylePost:e}=(0,i.useSelect)((e=>({globalStylePost:e(B).getGlobalEmailStylesPost()||null})),[]),t=(0,l.useCallback)((t=>{e&&(0,i.dispatch)(ee.store).editEntityRecord("postType","wp_global_styles",e.id,{styles:t.styles,settings:t.settings})}),[e]);return{userTheme:{settings:e?.settings,styles:e?.styles},updateUserTheme:t}}function Ce(e){if("string"!=typeof e)return null;const t=e.match(/^var:preset\|([a-zA-Z0-9-]+)\|([a-zA-Z0-9-]+)$/);return t?`--wp--preset--${t[1]}--${t[2]}`:null}function Ee(e){const t=Ce(e);return t?`var(${t})`:e}function Te(e){const t=Ce(e);if(!t)return e;const o=document.querySelector(":root");return o&&getComputedStyle(o).getPropertyValue(t).trim()||e}const Pe=[];function Ne(){const{userTheme:e}=Se(),{editorTheme:t,layout:o,deviceType:n,editorSettingsStyles:r}=(0,i.useSelect)((e=>{const{getEditorSettings:t,getDeviceType:o}=e($.store),n=t();return{editorTheme:e(B).getTheme(),layout:n.__experimentalFeatures?.layout,deviceType:o(),editorSettingsStyles:n.styles}}),[]),s=(0,l.useMemo)((()=>ke().all([{},t||{},e||{}])),[t,e]),[a]=q(s);let c="";o&&"Mobile"!==n&&(c=`display:flow-root; width:${o?.contentSize}; margin: 0 auto;box-sizing: border-box;`);const d=s.styles?.spacing?.padding;return d&&(c+=`padding-left:${Ee(d.left)};`,c+=`padding-right:${Ee(d.right)};`),[(0,l.useMemo)((()=>{var e;return[...null!==(e=a)&&void 0!==e?e:[],{css:`.is-root-container{ ${c} }`},...null!=r?r:[]]}),[a,r,c])||Pe]}const Be=[];function Me(e,t){return e.map((e=>"core/post-content"===e.name?{...e,name:"core/group",innerBlocks:t}:e.innerBlocks?.length?{...e,innerBlocks:Me(e.innerBlocks,t)}:e))}const Ie={};function ze(e=""){const{templates:t,patterns:o,emailPosts:n,hasEmailPosts:r}=(0,i.useSelect)((t=>{const o="swap"!==e?t(B).getSentEmailEditorPosts():void 0;return{templates:t(B).getEmailTemplates(),patterns:t(B).getBlockPatternsForEmailTemplate(),emailPosts:o,hasEmailPosts:!(!o||!o?.length)}}),[e]),s=(0,l.useMemo)((()=>{let n=[];const r=e&&(0,H.parse)(e);if(n=r?[{blocks:r}]:o,!n||!t)return Be;const s=[];return t?.filter((e=>"email-general"!==e.slug))?.forEach((e=>{n?.forEach((t=>{let o=(0,H.parse)(e.content?.raw);o=Me(o,t.blocks),s.push({id:e.id,slug:e.slug,previewContentParsed:o,emailParsed:t.blocks,template:e,category:"basic",type:e.type,displayName:t.title?`${e.title.rendered} - ${t.title}`:e.title.rendered})}))})),s}),[t,o,e]),c=(0,l.useMemo)((()=>n?.map((e=>{const t=(0,a.applyFilters)("woocommerce_email_editor_preferred_template_title","",e),{postTemplateContent:o}=function(e,t=[]){const o=e.template,n={postTemplateContent:null};if(!o)return n;if(Ie[o])return Ie[o];const r=t.find((e=>e.slug===o));if(!r)return n;const s={postTemplateContent:r?.template};return Ie[o]=s,s}(e,s),n=(0,H.parse)(e.content?.raw);let r=n;o?.content?.raw&&(r=Me((0,H.parse)(o?.content?.raw),n));const i={...e,title:{raw:e.title.raw,rendered:t||e.title.rendered}};return{id:e.id,slug:e.slug,previewContentParsed:r,emailParsed:n,category:"recent",type:e.type,displayName:i.title.rendered,template:i}}))),[n,s]);return[s||Be,c||Be,r]}const Fe=(0,l.forwardRef)((function({icon:e,size:t=24,...o},n){return(0,l.cloneElement)(e,{width:t,height:t,...o,ref:n})})),Ae=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M19 8h-1V6h-5v2h-2V6H6v2H5c-1.1 0-2 .9-2 2v8c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2v-8c0-1.1-.9-2-2-2zm.5 10c0 .3-.2.5-.5.5H5c-.3 0-.5-.2-.5-.5v-8c0-.3.2-.5.5-.5h14c.3 0 .5.2.5.5v8z"})}),Le=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M12 3.2c-4.8 0-8.8 3.9-8.8 8.8 0 4.8 3.9 8.8 8.8 8.8 4.8 0 8.8-3.9 8.8-8.8 0-4.8-4-8.8-8.8-8.8zm0 16c-4 0-7.2-3.3-7.2-7.2C4.8 8 8 4.8 12 4.8s7.2 3.3 7.2 7.2c0 4-3.2 7.2-7.2 7.2zM11 17h2v-6h-2v6zm0-8h2V7h-2v2z"})}),Re=(0,window.wp.priorityQueue.createQueue)();function Oe({children:e,placeholder:t}){const[o,n]=(0,l.useState)(!1);return(0,l.useEffect)((()=>{const e={};return Re.add(e,(()=>{(0,l.flushSync)((()=>{n(!0)}))})),()=>{Re.cancel(e)}}),[]),o?e:t}function He(){return(0,p.jsxs)("div",{className:"block-editor-inserter__no-results",children:[(0,p.jsx)(Fe,{className:"block-editor-inserter__no-results-icon",icon:Ae}),(0,p.jsx)("p",{children:(0,_.__)("No recent templates.","woocommerce")}),(0,p.jsx)("p",{children:(0,_.__)("Your recent creations will appear here as soon as you begin.","woocommerce")})]})}const Ve=(0,l.memo)((function({templates:e,onTemplateSelection:t,selectedCategory:o}){const{layout:n}=(0,i.useSelect)((e=>{const{getEditorSettings:t}=e($.store);return{layout:t().__experimentalFeatures.layout}})),[r]=Ne(),s=r.reduce(((e,t)=>{var o;return e+(null!==(o=t.css)&&void 0!==o?o:"")}),"")+`.is-root-container { width: ${n.contentSize}; margin: 0 auto; }`;return"recent"===o&&0===e.length?(0,p.jsx)(He,{}):(0,p.jsx)("div",{className:"block-editor-block-patterns-list",role:"listbox",children:e.map((e=>(0,p.jsx)("div",{className:"block-editor-block-patterns-list__list-item email-editor-pattern__list-item",children:(0,p.jsx)("div",{className:"block-editor-block-patterns-list__item",role:"button",tabIndex:0,onClick:()=>{t(e)},onKeyPress:o=>{"Enter"!==o.key&&" "!==o.key||t(e)},children:(0,p.jsxs)(Oe,{placeholder:(0,p.jsx)("p",{children:(0,_.__)("rendering template","woocommerce")}),children:[(0,p.jsx)(d.BlockPreview,{blocks:e.previewContentParsed,viewportWidth:900,minHeight:300,additionalStyles:[{css:s}]}),(0,p.jsx)(x.__experimentalHStack,{className:"block-editor-patterns__pattern-details",children:(0,p.jsx)("h4",{className:"block-editor-block-patterns-list__item-title",children:e.displayName})})]})})},`${e.slug}_${e.displayName}_${e.id}`)))})}),((e,t)=>e.templates.length===t.templates.length&&e.selectedCategory===t.selectedCategory));function De({templates:e,onTemplateSelection:t,selectedCategory:o}){const n=(0,l.useMemo)((()=>e.filter((e=>e.category===o))),[o,e]);return(0,p.jsxs)("div",{className:"block-editor-block-patterns-explorer__list",children:["recent"===o&&(0,p.jsx)("div",{className:"email-editor-recent-templates-info",children:(0,p.jsxs)(x.__experimentalHStack,{spacing:1,expanded:!1,justify:"start",children:[(0,p.jsx)(Fe,{icon:Le}),(0,p.jsx)("p",{children:(0,_.__)("Templates created on the legacy editor will not appear here.","woocommerce")})]})}),(0,p.jsx)(Ve,{templates:n,onTemplateSelection:t,selectedCategory:o})]})}function Ge({selectedCategory:e,templateCategories:t,onClickCategory:o}){const n="block-editor-block-patterns-explorer__sidebar";return(0,p.jsx)("div",{className:n,children:(0,p.jsx)("div",{className:`${n}__categories-list`,children:t.map((({name:t,label:r})=>(0,p.jsx)(x.Button,{label:r,className:`${n}__categories-list__item`,isPressed:e===t,onClick:()=>{o(t)},children:r},t)))})})}const $e=[{name:"recent",label:"Recent"},{name:"basic",label:"Basic"}],We=(0,l.memo)((function({hasEmailPosts:e,templates:t,handleTemplateSelection:o,templateSelectMode:n}){const[r,s]=(0,l.useState)($e[1].name),i="swap"===n,a=$e.filter((({name:e})=>"recent"!==e||!i));return(0,l.useEffect)((()=>{setTimeout((()=>{e&&!i&&s($e[0].name)}),1e3)}),[e,i]),(0,p.jsxs)("div",{className:"block-editor-block-patterns-explorer",children:[(0,p.jsx)(Ge,{templateCategories:a,selectedCategory:r,onClickCategory:e=>{T("template_select_modal_category_change",{category:e}),s(e)}}),(0,p.jsx)(De,{templates:t,onTemplateSelection:o,selectedCategory:r})]})}));function Ue({onSelectCallback:e,closeCallback:t=null,previewContent:o=""}){const n=o?"swap":"new";P("template_select_modal_opened",{templateSelectMode:n});const[r,s,l]=ze(o),a=r?.length>0,c=t=>{const r=t.type===M,s=t.template;T("template_select_modal_template_selected",{templateSlug:t.slug,templateSelectMode:n,templateType:t.type}),o||(0,i.dispatch)($.store).resetEditorBlocks(t.emailParsed),(0,i.dispatch)(B).setTemplateToPost(r?s.template:t.slug),e()},d=()=>{var e;const t=null!==(e=r[0])&&void 0!==e?e:null;t&&(T("template_select_modal_handle_close_without_template_selected"),c(t))};return(0,p.jsxs)(x.Modal,{title:"new"===n?(0,_.__)("Start with an email preset","woocommerce"):(0,_.__)("Select a template","woocommerce"),onRequestClose:()=>(T("template_select_modal_closed",{templateSelectMode:n}),t?t():d()),isFullScreen:!0,children:[(0,p.jsx)(We,{hasEmailPosts:l,templates:[...r,...s],handleTemplateSelection:c,templateSelectMode:n}),(0,p.jsx)(x.Flex,{className:"email-editor-modal-footer",justify:"flex-end",children:(0,p.jsx)(x.FlexItem,{children:(0,p.jsx)(x.Button,{variant:"tertiary",className:"email-editor-start_from_scratch_button",onClick:()=>(T("template_select_modal_start_from_scratch_clicked"),d()),isBusy:!a,children:(0,_.__)("Start from scratch","woocommerce")})})})]})}function qe(){const[e,t]=(0,l.useState)(!1),{emailContentIsEmpty:o,emailHasEdits:n}=(0,i.useSelect)((e=>({emailContentIsEmpty:e(B).hasEmptyContent(),emailHasEdits:e(B).hasEdits()})),[]);return!o||n||e?null:(0,p.jsx)(Ue,{onSelectCallback:()=>t(!0)})}const Ze=(0,p.jsx)(oe.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,p.jsx)(oe.Path,{d:"M12 4c-4.4 0-8 3.6-8 8v.1c0 4.1 3.2 7.5 7.2 7.9h.8c4.4 0 8-3.6 8-8s-3.6-8-8-8zm0 15V5c3.9 0 7 3.1 7 7s-3.1 7-7 7z"})}),Je=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M6.9 7L3 17.8h1.7l1-2.8h4.1l1 2.8h1.7L8.6 7H6.9zm-.7 6.6l1.5-4.3 1.5 4.3h-3zM21.6 17c-.1.1-.2.2-.3.2-.1.1-.2.1-.4.1s-.3-.1-.4-.2c-.1-.1-.1-.3-.1-.6V12c0-.5 0-1-.1-1.4-.1-.4-.3-.7-.5-1-.2-.2-.5-.4-.9-.5-.4 0-.8-.1-1.3-.1s-1 .1-1.4.2c-.4.1-.7.3-1 .4-.2.2-.4.3-.6.5-.1.2-.2.4-.2.7 0 .3.1.5.2.8.2.2.4.3.8.3.3 0 .6-.1.8-.3.2-.2.3-.4.3-.7 0-.3-.1-.5-.2-.7-.2-.2-.4-.3-.6-.4.2-.2.4-.3.7-.4.3-.1.6-.1.8-.1.3 0 .6 0 .8.1.2.1.4.3.5.5.1.2.2.5.2.9v1.1c0 .3-.1.5-.3.6-.2.2-.5.3-.9.4-.3.1-.7.3-1.1.4-.4.1-.8.3-1.1.5-.3.2-.6.4-.8.7-.2.3-.3.7-.3 1.2 0 .6.2 1.1.5 1.4.3.4.9.5 1.6.5.5 0 1-.1 1.4-.3.4-.2.8-.6 1.1-1.1 0 .4.1.7.3 1 .2.3.6.4 1.2.4.4 0 .7-.1.9-.2.2-.1.5-.3.7-.4h-.3zm-3-.9c-.2.4-.5.7-.8.8-.3.2-.6.2-.8.2-.4 0-.6-.1-.9-.3-.2-.2-.3-.6-.3-1.1 0-.5.1-.9.3-1.2s.5-.5.8-.7c.3-.2.7-.3 1-.5.3-.1.6-.3.7-.6v3.4z"})}),Ye=(0,p.jsx)(oe.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,p.jsx)(oe.Path,{d:"M17.2 10.9c-.5-1-1.2-2.1-2.1-3.2-.6-.9-1.3-1.7-2.1-2.6L12 4l-1 1.1c-.6.9-1.3 1.7-2 2.6-.8 1.2-1.5 2.3-2 3.2-.6 1.2-1 2.2-1 3 0 3.4 2.7 6.1 6.1 6.1s6.1-2.7 6.1-6.1c0-.8-.3-1.8-1-3zm-5.1 7.6c-2.5 0-4.6-2.1-4.6-4.6 0-.3.1-1 .8-2.3.5-.9 1.1-1.9 2-3.1.7-.9 1.3-1.7 1.8-2.3.7.8 1.3 1.6 1.8 2.3.8 1.1 1.5 2.2 2 3.1.7 1.3.8 2 .8 2.3 0 2.5-2.1 4.6-4.6 4.6z"})}),Ke=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M18 5.5H6a.5.5 0 00-.5.5v3h13V6a.5.5 0 00-.5-.5zm.5 5H10v8h8a.5.5 0 00.5-.5v-7.5zm-10 0h-3V18a.5.5 0 00.5.5h2.5v-8zM6 4h12a2 2 0 012 2v12a2 2 0 01-2 2H6a2 2 0 01-2-2V6a2 2 0 012-2z"})}),Xe={};function Qe(e){const t=e=>{if("object"==typeof e&&null!==e||void 0===e){if(Array.isArray(e)&&0===e.length)return;for(const o in e)if(e.hasOwnProperty(o)){const n=t(e[o]);void 0===n?delete e[o]:e[o]=n}}return e};return t(e)}const et=()=>{const{userTheme:e,updateUserTheme:t}=Se(),o=(0,l.useMemo)((()=>e?Qe(function(e){const t=e=>{if("object"==typeof e&&null!==e)for(const o in e)e.hasOwnProperty(o)&&(e[o]=t(e[o]));else if("string"==typeof e)return e.replace(/var\(--([a-z]+)--([a-z]+(?:--[a-z0-9]+(?:-[a-z0-9]+)*)*)--([a-z0-9-]+)\)/g,((e,t,o,n)=>`var:${o.split("--").concat(n).join("|")}`));return e};return t(e)}(e?.styles)):Xe),[e]),{styles:n}=(0,i.useSelect)((e=>({styles:e(B).getStyles()}))),r=(0,l.useCallback)((o=>{const n={...e,styles:Qe(o)};t(n)}),[t,e]),s=(0,l.useCallback)(((o,n)=>{const r=function(e,t,o){const n=Array.isArray(t)?[...t]:[t],r=Array.isArray(e)?[...e]:{...e},s=n.pop();let i=r;return n.forEach((e=>{const t=i[e];i[e]=Array.isArray(t)?[...t]:{...t},i=i[e]})),i[s]=o,r}(e,["styles",...o],n);t(r)}),[t,e]);return{styles:(0,l.useMemo)((()=>n?o?ke().all([n,o]):n:Xe),[n,o]),userStyles:e?.styles,defaultStyles:n,updateStyleProp:s,updateStyles:r}},tt={start:{scale:1,opacity:1},hover:{scale:0,opacity:0}},ot={hover:{opacity:1},start:{opacity:.5}},nt={hover:{scale:1,opacity:1},start:{scale:0,opacity:0}};function rt({label:e,isFocused:t,withHoverView:o}){const{colors:n}=(0,i.useSelect)((e=>({colors:e(B).getPaletteColors()})),[]),r=(0,l.useMemo)((()=>n.theme.concat(n.default)),[n]),{styles:s}=et(),{backgroundColor:a,headingColor:c,textColorPaletteObject:d,buttonBackgroundColorPaletteObject:m}=(0,l.useMemo)((()=>{const e=Te(s?.color?.background)||"white",t=Te(s?.color?.text)||"black",o=Te(s?.elements?.h1?.color?.text)||t,n=Te(s?.elements?.link?.color?.text)||o,i=Te(s?.elements?.button?.color?.background)||n,l=r.find((({color:e})=>e.toLowerCase()===t.toLowerCase())),a=r.find((({color:e})=>e.toLowerCase()===i.toLowerCase()));return{backgroundColor:e,headingColor:o,buttonBackgroundColor:i,textColorPaletteObject:l,buttonBackgroundColorPaletteObject:a}}),[s,r]),u=s?.elements?.heading?.typography?.fontWeight||"inherit",_=s?.elements?.heading?.typography?.fontFamily||"inherit",g=[...d?[d]:[],...m?[m]:[],...r].filter((({color:e})=>e.toLowerCase()!==a.toLowerCase())).slice(0,2),[h,y]=(0,l.useState)(!1);return(0,p.jsx)("div",{onMouseEnter:()=>y(!0),onMouseLeave:()=>y(!1),children:(0,p.jsxs)(x.__unstableMotion.div,{style:{height:152,width:"100%",background:a,cursor:o?"pointer":void 0},initial:"start",animate:(h||t)&&e?"hover":"start",children:[(0,p.jsx)(x.__unstableMotion.div,{variants:tt,style:{height:"100%",overflow:"hidden"},children:(0,p.jsxs)(x.__experimentalHStack,{spacing:10,justify:"center",style:{height:"100%",overflow:"hidden"},children:[(0,p.jsx)(x.__unstableMotion.div,{style:{fontFamily:_,fontSize:65,color:c,fontWeight:u},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:.3,type:"tween"},children:"Aa"}),(0,p.jsx)(x.__experimentalVStack,{spacing:4,children:g.map((({slug:e,color:t},o)=>(0,p.jsx)(x.__unstableMotion.div,{style:{height:32,width:32,background:t,borderRadius:16},animate:{scale:1,opacity:1},initial:{scale:.1,opacity:0},transition:{delay:1===o?.2:.1}},e)))})]})}),(0,p.jsx)(x.__unstableMotion.div,{variants:o&&ot,style:{height:"100%",width:"100%",position:"absolute",top:0,overflow:"hidden",filter:"blur(60px)",opacity:.1},children:(0,p.jsx)(x.__experimentalHStack,{spacing:0,justify:"flex-start",style:{height:"100%",overflow:"hidden"},children:r.slice(0,4).map((({color:e})=>(0,p.jsx)("div",{style:{height:"100%",background:e,flexGrow:1}},e)))})}),(0,p.jsx)(x.__unstableMotion.div,{variants:nt,style:{height:"100%",width:"100%",overflow:"hidden",position:"absolute",top:0},children:(0,p.jsx)(x.__experimentalVStack,{spacing:3,justify:"center",style:{height:"100%",overflow:"hidden",padding:10,boxSizing:"border-box"},children:e&&(0,p.jsx)("div",{style:{fontSize:40,fontFamily:_,color:c,fontWeight:u,lineHeight:"1em",textAlign:"center"},children:e})})})]})})}function st(){return(0,p.jsx)(x.Card,{size:"small",className:"edit-site-global-styles-screen-root",variant:"primary",children:(0,p.jsx)(x.CardBody,{children:(0,p.jsxs)(x.__experimentalVStack,{spacing:4,children:[(0,p.jsx)(x.Card,{children:(0,p.jsx)(x.CardMedia,{children:(0,p.jsx)(rt,{})})}),(0,p.jsxs)(x.__experimentalItemGroup,{children:[(0,p.jsx)(x.__experimentalNavigatorButton,{path:"/typography",onClick:()=>T("styles_sidebar_navigation_click",{path:"typography"}),children:(0,p.jsx)(x.__experimentalItem,{children:(0,p.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,p.jsx)(x.Icon,{icon:Je,size:24}),(0,p.jsx)(x.FlexItem,{children:(0,_.__)("Typography","woocommerce")})]})})}),(0,p.jsx)(x.__experimentalNavigatorButton,{path:"/colors",onClick:()=>T("styles_sidebar_navigation_click",{path:"colors"}),children:(0,p.jsx)(x.__experimentalItem,{children:(0,p.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,p.jsx)(x.Icon,{icon:Ye,size:24}),(0,p.jsx)(x.FlexItem,{children:(0,_.__)("Colors","woocommerce")})]})})}),(0,p.jsx)(x.__experimentalNavigatorButton,{path:"/layout",onClick:()=>T("styles_sidebar_navigation_click",{path:"layout"}),children:(0,p.jsx)(x.__experimentalItem,{children:(0,p.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,p.jsx)(x.Icon,{icon:Ke,size:24}),(0,p.jsx)(x.FlexItem,{children:(0,_.__)("Layout","woocommerce")})]})})})]})]})})})}const it={typography:{},color:{}},lt=(e,t,o="heading",n=!1)=>{switch(t){case"text":return{typography:e.typography,color:e.color};case"heading":return((e,t="heading",o=!1)=>o?ke().all([it,e.elements.heading||{},e.elements[t]||{}]):{...it,...e.elements.heading||{},...e.elements[t]||{}})(e,null!=o?o:"heading",n);default:return e.elements[t]||it}};function at({element:e,label:t}){const{styles:o}=et(),n=lt(o,e,null,!0),{fontFamily:r,fontStyle:s,fontWeight:i,letterSpacing:l,textDecoration:a,textTransform:c}=n.typography,d=n.color?.text||"inherit",m=n.color?.background||"#f0f0f0",u=(0,_.sprintf)((0,_.__)("Typography %s styles","woocommerce"),t);return(0,p.jsx)(x.__experimentalItem,{children:(0,p.jsx)(x.__experimentalNavigatorButton,{path:`/typography/${e}`,"aria-label":u,onClick:()=>T("styles_sidebar_screen_typography_button_click",{element:e,label:t,path:`typography/${e}`}),children:(0,p.jsxs)(x.__experimentalHStack,{justify:"flex-start",children:[(0,p.jsx)(x.FlexItem,{className:"edit-site-global-styles-screen-typography__indicator",style:{fontFamily:null!=r?r:"serif",background:m,color:d,fontStyle:null!=s?s:"normal",fontWeight:null!=i?i:"normal",letterSpacing:null!=l?l:"normal",textDecoration:null!=a?a:"link"===e?"underline":"none",textTransform:null!=c?c:"none"},children:"Aa"}),(0,p.jsx)(x.FlexItem,{children:t})]})})})}const ct=function(){return(0,p.jsx)(x.Card,{size:"small",variant:"primary",isBorderless:!0,children:(0,p.jsx)(x.CardBody,{children:(0,p.jsxs)(x.__experimentalVStack,{spacing:3,children:[(0,p.jsx)(x.__experimentalHeading,{level:3,className:"edit-site-global-styles-subtitle",children:(0,_.__)("Elements","woocommerce")}),(0,p.jsxs)(x.__experimentalItemGroup,{isBordered:!0,isSeparated:!0,size:"small",children:[(0,p.jsx)(at,{element:"text",label:(0,_.__)("Text","woocommerce")}),(0,p.jsx)(at,{element:"link",label:(0,_.__)("Links","woocommerce")}),(0,p.jsx)(at,{element:"heading",label:(0,_.__)("Headings","woocommerce")}),(0,p.jsx)(at,{element:"button",label:(0,_.__)("Buttons","woocommerce")})]})]})})})},dt=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})}),mt=x.Navigator||x.__experimentalNavigatorProvider;function pt({title:e,description:t,onBack:o}){return(0,p.jsxs)(x.__experimentalVStack,{spacing:0,children:[(0,p.jsx)(x.__experimentalView,{children:(0,p.jsx)(x.__experimentalSpacer,{marginBottom:0,paddingX:4,paddingY:3,children:(0,p.jsxs)(x.__experimentalHStack,{spacing:2,children:[(0,p.jsx)(mt.BackButton,{style:{minWidth:24,padding:0},icon:dt,size:"small","aria-label":(0,_.__)("Navigate to the previous view","woocommerce"),onClick:o}),(0,p.jsx)(x.__experimentalSpacer,{children:(0,p.jsx)(x.__experimentalHeading,{className:"woocommerce-email-editor-styles-header",level:2,size:13,children:e})})]})})}),t&&(0,p.jsx)("p",{className:"woocommerce-email-editor-styles-header-description",children:t})]})}x.Navigator||(mt.Screen=x.__experimentalNavigatorScreen,mt.BackButton=x.__experimentalNavigatorBackButton);const ut=pt;function _t(){return P("styles_sidebar_screen_typography_opened"),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(ut,{title:(0,_.__)("Typography","woocommerce"),description:(0,_.__)("Manage the typography settings for different elements.","woocommerce")}),(0,p.jsx)(ct,{})]})}const gt={fontFamily:!0,fontSize:!0,fontAppearance:!0,lineHeight:!0,letterSpacing:!1,textTransform:!1,textDecoration:!1,writingMode:!0,textColumns:!0},ht=function({element:e,headingLevel:t,defaultControls:o=gt}){const[n,r]=(0,d.useSettings)("typography.fontSizes","typography.fontFamilies"),s=r?.default||[],{styles:i,defaultStyles:a,updateStyleProp:c}=et(),m=lt(i,e,t),u=lt(a,e,t),{fontFamily:g,fontSize:h,fontStyle:y,fontWeight:w,lineHeight:f,letterSpacing:b,textDecoration:v,textTransform:j}=m.typography,{fontFamily:k,fontSize:S,fontStyle:C,fontWeight:E,lineHeight:P,letterSpacing:B,textDecoration:M,textTransform:I}=u.typography,z="heading"!==e||"heading"!==t,F=(0,l.useCallback)(((o,n)=>{c("heading"===e?["elements",t,...o]:"text"===e?[...o]:["elements",e,...o],n)}),[e,c,t]),A=t=>{F(["typography","letterSpacing"],t),N("styles_sidebar_screen_typography_element_panel_set_letter_spacing",{element:e,newValue:t,selectedDefaultLetterSpacing:t===B})},L=t=>{F(["typography","lineHeight"],t),N("styles_sidebar_screen_typography_element_panel_set_line_height",{element:e,newValue:t,selectedDefaultLineHeight:t===P})},R=o=>{F(["typography","fontSize"],o),N("styles_sidebar_screen_typography_element_panel_set_font_size",{element:e,headingLevel:t,newValue:o,selectedDefaultFontSize:o===S})},O=t=>{F(["typography","fontFamily"],t),N("styles_sidebar_screen_typography_element_panel_set_font_family",{element:e,newValue:t,selectedDefaultFontFamily:t===k})},H=t=>{F(["typography","textDecoration"],t),N("styles_sidebar_screen_typography_element_panel_set_text_decoration",{element:e,newValue:t,selectedDefaultTextDecoration:t===M})},V=t=>{F(["typography","textTransform"],t),N("styles_sidebar_screen_typography_element_panel_set_text_transform",{element:e,newValue:t,selectedDefaultTextTransform:t===I})},D=({fontStyle:t,fontWeight:o})=>{F(["typography","fontStyle"],t),F(["typography","fontWeight"],o),N("styles_sidebar_screen_typography_element_panel_set_font_appearance",{element:e,newFontStyle:t,newFontWeight:o,selectedDefaultFontStyle:t===C,selectedDefaultFontWeight:o===E})};return(0,p.jsxs)(x.__experimentalToolsPanel,{label:(0,_.__)("Typography","woocommerce"),resetAll:()=>{F(["typography"],u.typography),T("styles_sidebar_screen_typography_element_panel_reset_all_styles_selected",{element:e,headingLevel:t})},children:[(0,p.jsx)(x.__experimentalToolsPanelItem,{label:(0,_.__)("Font family","woocommerce"),hasValue:()=>g!==k,onDeselect:()=>O(k),isShownByDefault:o.fontFamily,children:(0,p.jsx)(d.__experimentalFontFamilyControl,{value:g,onChange:O,size:"__unstable-large",fontFamilies:s,__nextHasNoMarginBottom:!0})}),z&&(0,p.jsx)(x.__experimentalToolsPanelItem,{label:(0,_.__)("Font size","woocommerce"),hasValue:()=>h!==S,onDeselect:()=>R(S),isShownByDefault:o.fontSize,children:(0,p.jsx)(x.FontSizePicker,{value:h,onChange:R,fontSizes:n,disableCustomFontSizes:!1,withReset:!1,withSlider:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})}),(0,p.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,_.__)("Appearance","woocommerce"),hasValue:()=>w!==E||y!==C,onDeselect:()=>{D({fontStyle:C,fontWeight:E})},isShownByDefault:o.fontAppearance,children:(0,p.jsx)(d.__experimentalFontAppearanceControl,{value:{fontStyle:y,fontWeight:w},onChange:D,hasFontStyles:!0,hasFontWeights:!0,size:"__unstable-large"})}),(0,p.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,_.__)("Line height","woocommerce"),hasValue:()=>f!==P,onDeselect:()=>L(P),isShownByDefault:o.lineHeight,children:(0,p.jsx)(d.LineHeightControl,{__nextHasNoMarginBottom:!0,__unstableInputWidth:"auto",value:f,onChange:L,size:"__unstable-large"})}),(0,p.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,_.__)("Letter spacing","woocommerce"),hasValue:()=>b!==B,onDeselect:()=>A(B),isShownByDefault:o.letterSpacing,children:(0,p.jsx)(d.__experimentalLetterSpacingControl,{value:b,onChange:A,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,p.jsx)(x.__experimentalToolsPanelItem,{className:"single-column",label:(0,_.__)("Text decoration","woocommerce"),hasValue:()=>v!==M,onDeselect:()=>H(M),isShownByDefault:o.textDecoration,children:(0,p.jsx)(d.__experimentalTextDecorationControl,{value:v,onChange:H,size:"__unstable-large",__unstableInputWidth:"auto"})}),(0,p.jsx)(x.__experimentalToolsPanelItem,{label:(0,_.__)("Letter case","woocommerce"),hasValue:()=>j!==I,onDeselect:()=>V(I),isShownByDefault:o.textTransform,children:(0,p.jsx)(d.__experimentalTextTransformControl,{value:j,onChange:V,showNone:!0,isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0})})]})};function yt({element:e,headingLevel:t}){const{styles:o}=et(),n=lt(o,e,t,!0),{fontFamily:r,fontSize:s,fontStyle:i,fontWeight:l,lineHeight:a,letterSpacing:c,textDecoration:d,textTransform:m}=n.typography,u=n.color?.text||"inherit",_=n.color?.background||"#f0f0f0",g="link"===e?{textDecoration:null!=d?d:"underline"}:{};return(0,p.jsx)("div",{className:"edit-site-typography-preview",style:{fontFamily:null!=r?r:"serif",background:_,color:u,lineHeight:a,fontSize:s,fontStyle:i,fontWeight:l,letterSpacing:c,textDecoration:d,textTransform:m,...g},children:"Aa"})}const xt={text:{title:(0,_.__)("Text","woocommerce"),description:(0,_.__)("Manage the fonts and typography used on text.","woocommerce"),defaultControls:gt},link:{title:(0,_.__)("Links","woocommerce"),description:(0,_.__)("Manage the fonts and typography used on links.","woocommerce"),defaultControls:{...gt,textDecoration:!0}},heading:{title:(0,_.__)("Headings","woocommerce"),description:(0,_.__)("Manage the fonts and typography used on headings.","woocommerce"),defaultControls:{...gt,textTransform:!0}},button:{title:(0,_.__)("Buttons","woocommerce"),description:(0,_.__)("Manage the fonts and typography used on buttons.","woocommerce"),defaultControls:gt}};function wt({element:e}){P("styles_sidebar_screen_typography_element_opened",{element:e});const[t,o]=(0,l.useState)("heading");return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(ut,{title:xt[e].title,description:xt[e].description}),(0,p.jsx)(x.__experimentalSpacer,{marginX:4,children:(0,p.jsx)(yt,{element:e,headingLevel:t})}),"heading"===e&&(0,p.jsx)(x.__experimentalSpacer,{marginX:4,marginBottom:"1em",children:(0,p.jsxs)(x.__experimentalToggleGroupControl,{label:(0,_.__)("Select heading level","woocommerce"),hideLabelFromVision:!0,value:t,onChange:e=>{o(e.toString()),T("styles_sidebar_screen_typography_element_heading_level_selected",{value:e})},isBlock:!0,size:"__unstable-large",__nextHasNoMarginBottom:!0,children:[(0,p.jsx)(x.__experimentalToggleGroupControlOption,{value:"heading",label:(0,_._x)("All","heading levels","woocommerce")}),(0,p.jsx)(x.__experimentalToggleGroupControlOption,{value:"h1",label:(0,_._x)("H1","Heading Level","woocommerce")}),(0,p.jsx)(x.__experimentalToggleGroupControlOption,{value:"h2",label:(0,_._x)("H2","Heading Level","woocommerce")}),(0,p.jsx)(x.__experimentalToggleGroupControlOption,{value:"h3",label:(0,_._x)("H3","Heading Level","woocommerce")}),(0,p.jsx)(x.__experimentalToggleGroupControlOption,{value:"h4",label:(0,_._x)("H4","Heading Level","woocommerce")}),(0,p.jsx)(x.__experimentalToggleGroupControlOption,{value:"h5",label:(0,_._x)("H5","Heading Level","woocommerce")}),(0,p.jsx)(x.__experimentalToggleGroupControlOption,{value:"h6",label:(0,_._x)("H6","Heading Level","woocommerce")})]})}),(0,p.jsx)(ht,{element:e,headingLevel:t,defaultControls:xt[e].defaultControls})]})}function ft(){P("styles_sidebar_screen_colors_opened");const{userStyles:e,styles:t,updateStyles:o}=et(),n=(0,i.useSelect)((e=>e(B).getTheme()),[]);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(ut,{title:(0,_.__)("Colors","woocommerce"),description:(0,_.__)("Manage palettes and the default color of different global elements.","woocommerce")}),(0,p.jsx)(U,{value:e,inheritedValue:t,onChange:e=>{o(e),T("styles_sidebar_screen_colors_styles_updated")},settings:n?.settings,panelId:"colors"})]})}function bt(){const[e]=(0,d.useSettings)("spacing.units"),t=(0,x.__experimentalUseCustomUnits)({availableUnits:e}),{styles:o,defaultStyles:n,updateStyleProp:r}=et();return(0,p.jsxs)(x.__experimentalToolsPanel,{label:(0,_.__)("Dimensions","woocommerce"),resetAll:()=>{r(["spacing"],n.spacing),T("styles_sidebar_screen_layout_dimensions_reset_all_selected")},children:[(0,p.jsx)(x.__experimentalToolsPanelItem,{isShownByDefault:!0,hasValue:()=>!(0,k.isEqual)(o.spacing.padding,n.spacing.padding),label:(0,_.__)("Padding","woocommerce"),onDeselect:()=>{r(["spacing","padding"],n.spacing.padding),T("styles_sidebar_screen_layout_dimensions_padding_reset_clicked")},className:"tools-panel-item-spacing",children:(0,p.jsx)(d.__experimentalSpacingSizesControl,{allowReset:!0,values:o.spacing.padding,onChange:e=>{r(["spacing","padding"],e),N("styles_sidebar_screen_layout_dimensions_padding_updated",{value:e})},label:(0,_.__)("Padding","woocommerce"),sides:["horizontal","vertical","top","left","right","bottom"],units:t})}),(0,p.jsx)(x.__experimentalToolsPanelItem,{isShownByDefault:!0,label:(0,_.__)("Block spacing","woocommerce"),hasValue:()=>o.spacing.blockGap!==n.spacing.blockGap,onDeselect:()=>{r(["spacing","blockGap"],n.spacing.blockGap),T("styles_sidebar_screen_layout_dimensions_block_spacing_reset_clicked")},className:"tools-panel-item-spacing",children:(0,p.jsx)(d.__experimentalSpacingSizesControl,{label:(0,_.__)("Block spacing","woocommerce"),min:0,onChange:e=>{r(["spacing","blockGap"],e.top),N("styles_sidebar_screen_layout_dimensions_block_spacing_updated",{value:e})},showSideInLabel:!1,sides:["top"],values:{top:o.spacing.blockGap},allowReset:!0})})]})}function vt(){return P("styles_sidebar_screen_layout_opened"),(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)(pt,{title:(0,_.__)("Layout","woocommerce")}),(0,p.jsx)(bt,{})]})}const jt=(0,l.memo)((function(){const{userCanEditGlobalStyles:e}=(0,i.useSelect)((e=>{const{canEdit:t}=e(B).canUserEditGlobalEmailStyles();return{userCanEditGlobalStyles:t}}),[]);return e&&(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)($.PluginSidebarMoreMenuItem,{target:"email-styles-sidebar",icon:Ze,children:(0,_.__)("Email styles","woocommerce")}),(0,p.jsx)($.PluginSidebar,{name:"email-styles-sidebar",icon:Ze,title:(0,_.__)("Styles","woocommerce"),className:"woocommerce-email-editor-styles-panel",header:(0,_.__)("Styles","woocommerce"),children:(0,p.jsxs)(mt,{initialPath:"/",children:[(0,p.jsx)(mt.Screen,{path:"/",children:(0,p.jsx)(st,{})}),(0,p.jsx)(mt.Screen,{path:"/typography",children:(0,p.jsx)(_t,{})}),(0,p.jsx)(mt.Screen,{path:"/typography/text",children:(0,p.jsx)(wt,{element:"text"})}),(0,p.jsx)(mt.Screen,{path:"/typography/link",children:(0,p.jsx)(wt,{element:"link"})}),(0,p.jsx)(mt.Screen,{path:"/typography/heading",children:(0,p.jsx)(wt,{element:"heading"})}),(0,p.jsx)(mt.Screen,{path:"/typography/button",children:(0,p.jsx)(wt,{element:"button"})}),(0,p.jsx)(mt.Screen,{path:"/colors",children:(0,p.jsx)(ft,{})}),(0,p.jsx)(mt.Screen,{path:"/layout",children:(0,p.jsx)(vt,{})})]})})]})})),kt=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M19.5 4.5h-7V6h4.44l-5.97 5.97 1.06 1.06L18 7.06v4.44h1.5v-7Zm-13 1a2 2 0 0 0-2 2v10a2 2 0 0 0 2 2h10a2 2 0 0 0 2-2v-3H17v3a.5.5 0 0 1-.5.5h-10a.5.5 0 0 1-.5-.5v-10a.5.5 0 0 1 .5-.5h3V5.5h-3Z"})}),St=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),Ct=window.wp.keycodes,Et=window.wp.url;let Tt=function(e){return e.SUCCESS="success",e.ERROR="error",e}({});const Pt=(0,a.applyFilters)("woocommerce_email_editor_check_sending_method_configuration_link",`https://www.mailpoet.com/blog/mailpoet-smtp-plugin/?utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${M}`),Nt=(0,l.memo)((function(){const e=(0,l.useRef)(null),{requestSendingNewsletterPreview:t,togglePreviewModal:o,updateSendPreviewEmail:n}=(0,i.useDispatch)(B),{toEmail:r,isSendingPreviewEmail:s,sendingPreviewStatus:a,isModalOpened:c,errorMessage:d}=(0,i.useSelect)((e=>e(B).getPreviewState()),[]),m=()=>{t(r)},u=()=>{T("send_preview_email_modal_closed"),o(!1)};return(0,l.useEffect)((()=>{c&&(e.current?.focus(),T("send_preview_email_modal_opened"))}),[c]),c?(0,p.jsxs)(x.Modal,{className:"woocommerce-send-preview-email",title:(0,_.__)("Send a test email","woocommerce"),onRequestClose:u,focusOnMount:!1,children:[a===Tt.ERROR?(0,p.jsxs)("div",{className:"woocommerce-send-preview-modal-notice-error",children:[(0,p.jsx)("p",{children:(0,_.__)("Sorry, we were unable to send this email.","woocommerce")}),(0,p.jsx)("strong",{children:d&&(0,_.sprintf)((0,_.__)("Error: %s","woocommerce"),d)}),(0,p.jsxs)("ul",{children:[(0,p.jsx)("li",{children:Pt&&(0,l.createInterpolateElement)((0,_.__)("Please check your <link>sending method configuration</link> with your hosting provider.","woocommerce"),{link:(0,p.jsx)("a",{href:Pt,target:"_blank",rel:"noopener noreferrer",onClick:()=>T("send_preview_email_modal_check_sending_method_configuration_link_clicked")})})}),(0,p.jsx)("li",{children:(0,l.createInterpolateElement)((0,_.__)("Or, sign up for MailPoet Sending Service to easily send emails. <link>Sign up for free</link>","woocommerce"),{link:(0,p.jsx)("a",{href:`https://account.mailpoet.com/?s=1&g=1&utm_source=woocommerce_email_editor&utm_medium=plugin&utm_source_platform=${M}`,target:"_blank",rel:"noopener noreferrer",onClick:()=>T("send_preview_email_modal_sign_up_for_mailpoet_sending_service_link_clicked")},"sign-up-for-free")})})]})]}):null,(0,p.jsx)("p",{children:(0,_.__)("Send yourself a test email to test how your email would look like in different email apps.","woocommerce")}),(0,p.jsx)(x.TextControl,{label:(0,_.__)("Send to","woocommerce"),onChange:e=>{n(e),P("send_preview_email_modal_send_to_field_updated")},onKeyDown:e=>{const{keyCode:t}=e;t===Ct.ENTER&&(e.preventDefault(),m(),T("send_preview_email_modal_send_to_field_key_code_enter"))},className:"woocommerce-send-preview-email__send-to-field",value:r,type:"email",ref:e,required:!0,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0}),a===Tt.SUCCESS?(0,p.jsxs)("p",{className:"woocommerce-send-preview-modal-notice-success",children:[(0,p.jsx)(Fe,{icon:St,style:{fill:"#4AB866"}}),(0,_.__)("Test email sent successfully!","woocommerce")]}):null,(0,p.jsxs)("div",{className:"woocommerce-send-preview-modal-footer",children:[(0,p.jsx)(x.Button,{variant:"tertiary",onClick:()=>{T("send_preview_email_modal_close_button_clicked"),u()},children:(0,_.__)("Cancel","woocommerce")}),(0,p.jsx)(x.Button,{variant:"primary",onClick:()=>{m(),T("send_preview_email_modal_send_test_email_button_clicked")},disabled:s||!(0,Et.isEmail)(r),children:s?(0,_.__)("Sending…","woocommerce"):(0,_.__)("Send test email","woocommerce")})]})]}):null}));function Bt(){const{togglePreviewModal:e}=(0,i.useDispatch)(B);return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)($.PluginPreviewMenuItem,{icon:kt,onClick:()=>{T("header_preview_dropdown_send_test_email_selected"),e(!0)},children:(0,_.__)("Send a test email","woocommerce")}),(0,p.jsx)(Nt,{})]})}const Mt=window.wp.preferences,It=()=>{const e=(0,m.useViewportMatch)("large");return(0,p.jsx)(p.Fragment,{children:e&&(0,p.jsx)(Y,{children:(0,p.jsx)(Mt.PreferenceToggleMenuItem,{scope:B,name:"fullscreenMode",label:(0,_.__)("Fullscreen mode","woocommerce"),info:(0,_.__)("Show and hide the admin user interface","woocommerce"),messageActivated:(0,_.__)("Fullscreen mode activated.","woocommerce"),messageDeactivated:(0,_.__)("Fullscreen mode deactivated.","woocommerce"),shortcut:Ct.displayShortcut.secondary("f")})})})};function zt({close:e}){P("edit_template_modal_opened");const{onNavigateToEntityRecord:t,template:o}=(0,i.useSelect)((e=>{const{getEditorSettings:t}=e($.store);return{onNavigateToEntityRecord:t().onNavigateToEntityRecord,template:e(B).getCurrentTemplate()}}),[]);return(0,p.jsxs)(x.Modal,{size:"medium",onRequestClose:e,__experimentalHideHeader:!0,children:[(0,p.jsx)("p",{children:(0,_.__)("This template is used by multiple emails. Any changes made would affect other emails on the site. Are you sure you want to edit the template?","woocommerce")}),(0,p.jsxs)(x.Flex,{justify:"end",children:[(0,p.jsx)(x.FlexItem,{children:(0,p.jsx)(x.Button,{variant:"tertiary",onClick:()=>{T("edit_template_modal_cancel_button_clicked"),e()},children:(0,_.__)("Cancel","woocommerce")})}),(0,p.jsx)(x.FlexItem,{children:(0,p.jsx)(x.Button,{variant:"primary",onClick:()=>{T("edit_template_modal_continue_button_clicked",{templateId:o.id}),t({postId:o.id,postType:"wp_template"})},disabled:!o.id,children:(0,_.__)("Edit template","woocommerce")})})]})]})}function Ft(){const{template:e,currentEmailContent:t,canUpdateTemplates:o}=(0,i.useSelect)((e=>({template:e(B).getCurrentTemplate(),currentEmailContent:e(B).getEditedEmailContent(),canUpdateTemplates:e(B).canUserEditTemplates()})),[]),[n]=ze("swap"),[r,s]=(0,l.useState)(!1),[a,c]=(0,l.useState)(!1);return(0,p.jsxs)(p.Fragment,{children:[e&&(0,p.jsx)(x.PanelRow,{children:(0,p.jsxs)(x.Flex,{justify:"start",children:[(0,p.jsx)(x.FlexItem,{className:"editor-post-panel__row-label",children:(0,_.__)("Template","woocommerce")}),(0,p.jsxs)(x.FlexItem,{children:[!(n?.length>1||o)&&(0,p.jsx)("b",{children:e?.title}),(n?.length>1||o)&&(0,p.jsx)(x.DropdownMenu,{icon:null,text:e?.title,toggleProps:{variant:"tertiary"},label:(0,_.__)("Template actions","woocommerce"),onToggle:t=>T("sidebar_template_actions_clicked",{currentTemplate:e?.title,isOpen:t}),children:({onClose:e})=>(0,p.jsxs)(p.Fragment,{children:[o&&(0,p.jsx)(x.MenuItem,{onClick:()=>{T("sidebar_template_actions_edit_template_clicked"),s(!0),e()},children:(0,_.__)("Edit template","woocommerce")}),n?.length>1&&(0,p.jsx)(x.MenuItem,{onClick:()=>{T("sidebar_template_actions_swap_template_clicked"),c(!0),e()},children:(0,_.__)("Swap template","woocommerce")})]})})]})]})}),r&&(0,p.jsx)(zt,{close:()=>(T("edit_template_modal_closed"),s(!1))}),a&&(0,p.jsx)(Ue,{onSelectCallback:()=>c(!1),closeCallback:()=>c(!1),previewContent:t})]})}const At={recordEvent:T,recordEventOnce:P,debouncedRecordEvent:N},Lt=(0,a.applyFilters)("woocommerce_email_editor_setting_sidebar_extension_component",(function({label:e,labelSuffix:t,help:o,placeholder:n,attributeName:r,attributeValue:s,updateProperty:a=()=>{}}){const[c,m]=(0,l.useState)(null),[u,g]=(0,l.useState)(!1),h=(0,i.useSelect)((e=>e(B).getPersonalizationTagsList()),[]),b=(0,l.useRef)(null),v=(0,l.useCallback)(((e,t,o)=>{var n,s;const i=null!==(n=o?.start)&&void 0!==n?n:t.length,l=null!==(s=o?.end)&&void 0!==s?s:t.length;let c=(0,y.create)({html:t});c=(0,y.insert)(c,(0,y.create)({html:`\x3c!--${e}--\x3e`}),i,l);const d=(0,y.toHTMLString)({value:c});a(r,d),m(null)}),[r,a]),j=(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("span",{children:e}),(0,p.jsx)(x.Button,{className:"woocommerce-settings-panel-personalization-tags-button",icon:"shortcode",title:(0,_.__)("Personalization Tags","woocommerce"),onClick:()=>{g(!0),T("rich_text_with_button_personalization_tags_shortcode_icon_clicked",{attributeName:r,label:e})}}),t]});return r?(0,p.jsxs)(x.BaseControl,{id:"",label:j,className:`woocommerce-settings-panel-${r}-text`,help:o,__nextHasNoMarginBottom:!0,children:[(0,p.jsx)(z,{isOpened:u,onInsert:e=>{v(e,null!=s?s:"",c),g(!1),T("rich_text_with_button_personalization_tags_inserted",{attributeName:r,value:e})},closeCallback:()=>g(!1),openedBy:"RichTextWithButton-BaseControl"}),(0,p.jsx)(F,{contentRef:b,onUpdate:(e,t)=>{const o=(null!=s?s:"").replace(`\x3c!--[${e}]--\x3e`,`\x3c!--[${t}]--\x3e`);a(r,o)}}),(0,p.jsx)(d.RichText,{ref:b,className:"woocommerce-settings-panel-richtext",placeholder:n,onFocus:()=>{m(w(b,null!=s?s:""))},onKeyUp:()=>{m(w(b,null!=s?s:""))},onClick:()=>{m(w(b,null!=s?s:""))},onChange:e=>{var t;e=f(null!==(t=e)&&void 0!==t?t:"",h),a(r,e),P("rich_text_with_button_input_field_updated",{attributeName:r})},value:null!=s?s:"","data-automation-id":`email_${r}`})]}):null}),At),Rt=(0,a.applyFilters)("woocommerce_email_editor_setting_sidebar_email_status_component",(()=>null),At);function Ot(){return(0,p.jsxs)($.PluginDocumentSettingPanel,{name:"email-settings-panel",title:(0,_.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:[(0,p.jsx)(Rt,{}),(0,p.jsx)(Ft,{}),(0,p.jsx)($.ErrorBoundary,{canCopyContent:!0,children:(0,p.jsx)(Lt,{})})]})}const Ht={recordEvent:T,recordEventOnce:P,debouncedRecordEvent:N};function Vt(){const e=(0,a.applyFilters)("woocommerce_email_editor_template_sections",[],Ht);return 0===e.length?null:(0,p.jsx)($.PluginDocumentSettingPanel,{name:"template-settings-panel",title:(0,_.__)("Settings","woocommerce"),className:"woocommerce-email-editor__settings-panel",children:e.map((e=>(0,p.jsx)($.ErrorBoundary,{children:(0,p.jsx)("div",{children:e.render()},e.id)},`error-boundary-${e.id}`)))})}function Dt(){const{isDirty:e}=(0,$.useEntitiesSavedStatesIsDirty)(),{hasEmptyContent:t,isEmailSent:o,urls:n}=(0,i.useSelect)((e=>({hasEmptyContent:e(B).hasEmptyContent(),isEmailSent:e(B).isEmailSent(),urls:e(B).getUrls()})),[]);function r(){n.send&&(window.location.href=n.send)}const s=t||o||e,l=(0,a.applyFilters)("woocommerce_email_editor_send_button_label",(0,_.__)("Send","woocommerce"));return(0,p.jsx)(x.Button,{variant:"primary",size:"compact",onClick:()=>{T("header_send_button_clicked"),(0,a.applyFilters)("woocommerce_email_editor_send_action_callback",r)()},disabled:s,"data-automation-id":"email_editor_send_button",children:l})}function Gt({children:e}){const t=(0,l.useRef)(document.createElement("div"));return(0,l.useEffect)((()=>{const e=document.getElementsByClassName("editor-post-publish-button__button")[0];e&&e.parentNode?.insertBefore(t.current,e.nextSibling)}),[t]),(0,l.createPortal)((0,p.jsx)(p.Fragment,{children:e}),t.current)}function $t(){const e=(0,l.useRef)(null),{hasNonPostEntityChanges:t,isEditedPostDirty:o}=(0,i.useSelect)((e=>({hasNonPostEntityChanges:e($.store).hasNonPostEntityChanges(),isEditedPostDirty:e($.store).isEditedPostDirty()})),[]),n=t||o,r=(0,l.useCallback)(((e,t)=>{t&&e.classList.contains("force-hidden")&&e.classList.remove("force-hidden"),t||e.classList.contains("force-hidden")||e.classList.add("force-hidden")}),[]);return(0,l.useEffect)((()=>{const t=document.getElementsByClassName("editor-post-publish-button__button")[0];return r(t,n),t?(e.current&&e.current.disconnect(),e.current=new MutationObserver((()=>{r(t,n)})),e.current.observe(t,{attributes:!0,childList:!0,subtree:!1}),()=>e.current?.disconnect()):()=>e.current?.disconnect()}),[n,r]),(0,p.jsx)(Gt,{children:!n&&(0,p.jsx)(Dt,{})})}const Wt=()=>{const e="email-validation",t=(0,i.useSelect)((t=>t(te.store).getNotices(e)));return{notices:t,hasValidationNotice:(0,l.useCallback)((e=>e?void 0!==t.find((t=>t.id===e)):t?.length>0),[t]),addValidationNotice:(0,l.useCallback)(((t,o,n=[])=>{(0,i.dispatch)(te.store).createNotice("error",o,{id:t,isDismissible:!1,actions:n,context:e})}),[e]),removeValidationNotice:(0,l.useCallback)((t=>{(0,i.dispatch)(te.store).removeNotice(t,e)}),[e])}};function Ut(){const{notices:e}=Wt();return 0===e.length?null:(0,p.jsx)(x.Notice,{status:"error",className:"woocommerce-email-editor-validation-errors components-editor-notices__pinned",isDismissible:!1,children:(0,p.jsxs)(p.Fragment,{children:[(0,p.jsx)("strong",{children:(0,_.__)("Fix errors to continue:","woocommerce")}),(0,p.jsx)("ul",{children:e.map((({id:e,content:t,actions:o})=>(0,p.jsxs)("li",{children:[t,o.length>0?o.map((({label:e,onClick:t})=>(0,p.jsx)(x.Button,{onClick:t,variant:"link",children:e},e))):null]},e)))})]})})}function qt({context:e="email-editor"}){const{notices:t}=(0,i.useSelect)((t=>({notices:t(te.store).getNotices(e)})),[e]),o=(0,l.useMemo)((()=>({"site-editor-save-success":{content:(0,_.__)("Email design updated.","woocommerce"),removeActions:!0},"editor-save":{content:(0,_.__)("Email saved.","woocommerce"),removeActions:!1,contentCheck:e=>e.content.includes((0,_.__)("Post updated."))}})),[]),{removeNotice:n}=(0,i.useDispatch)(te.store),r=t.filter((({type:e})=>"snackbar"===e)).map((e=>o[e.id]?o[e.id].contentCheck&&!o[e.id].contentCheck(e)?e:{...e,content:o[e.id].content,spokenMessage:o[e.id].content,actions:o[e.id].removeActions?[]:e.actions}:e));return(0,p.jsx)(x.SnackbarList,{notices:r,className:"components-editor-notices__snackbar",onRemove:t=>n(t,e)})}function Zt({children:e}){const[t]=(0,l.useState)(document.createElement("div"));return(0,l.useEffect)((()=>{const e=document.getElementsByClassName("editor-visual-editor ")[0];e&&e.parentNode?.insertBefore(t,e)}),[t]),(0,l.createPortal)((0,p.jsx)(p.Fragment,{children:e}),t)}function Jt(){const{notices:e}=(0,i.useSelect)((e=>({notices:e(te.store).getNotices("email-editor")})),[]),{removeNotice:t}=(0,i.useDispatch)(te.store),o=e.filter((({isDismissible:e,type:t})=>e&&"default"===t)),n=e.filter((({isDismissible:e,type:t})=>!e&&"default"===t));return(0,p.jsxs)(p.Fragment,{children:[(0,p.jsxs)(Zt,{children:[(0,p.jsx)(x.NoticeList,{notices:n,className:"components-editor-notices__pinned"}),(0,p.jsx)(x.NoticeList,{notices:o,className:"components-editor-notices__dismissible",onRemove:e=>t(e,"email-editor")}),(0,p.jsx)(Ut,{})]}),(0,p.jsx)(qt,{context:"global"}),(0,p.jsx)(qt,{context:"email-editor"})]})}const Yt=e=>{const t=(0,H.getBlockSupport)(e,"background");return t&&!1!==t?.backgroundImage};function Kt(){const e=(0,i.useSelect)((e=>e("core/block-editor").getSelectedBlock()),[]),t=(0,H.hasBlockSupport)(e?.name,"border",!1)||(0,H.hasBlockSupport)(e?.name,"__experimentalBorder",!1);return(0,p.jsxs)(p.Fragment,{children:[t&&(0,p.jsx)(x.Fill,{name:"InspectorControlsBorder",children:(0,p.jsxs)(x.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:[(0,_.__)("Border display may vary or be unsupported in some email clients.","woocommerce"),(0,p.jsx)("br",{}),(0,_.__)("Units other than pixels (px) lack support in old email clients.","woocommerce")]})}),Yt(e?.name)&&(0,p.jsx)(x.Fill,{name:"InspectorControlsBackground",children:(0,p.jsx)(x.Notice,{className:"woocommerce-grid-full-width",status:"warning",isDismissible:!1,children:(0,_.__)("Select a background color for email clients that do not support background images.","woocommerce")})})]})}const Xt=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,p.jsx)(oe.Path,{d:"M20 10c0-5.51-4.49-10-10-10C4.48 0 0 4.49 0 10c0 5.52 4.48 10 10 10 5.51 0 10-4.48 10-10zM7.78 15.37L4.37 6.22c.55-.02 1.17-.08 1.17-.08.5-.06.44-1.13-.06-1.11 0 0-1.45.11-2.37.11-.18 0-.37 0-.58-.01C4.12 2.69 6.87 1.11 10 1.11c2.33 0 4.45.87 6.05 2.34-.68-.11-1.65.39-1.65 1.58 0 .74.45 1.36.9 2.1.35.61.55 1.36.55 2.46 0 1.49-1.4 5-1.4 5l-3.03-8.37c.54-.02.82-.17.82-.17.5-.05.44-1.25-.06-1.22 0 0-1.44.12-2.38.12-.87 0-2.33-.12-2.33-.12-.5-.03-.56 1.2-.06 1.22l.92.08 1.26 3.41zM17.41 10c.24-.64.74-1.87.43-4.25.7 1.29 1.05 2.71 1.05 4.25 0 3.29-1.73 6.24-4.4 7.78.97-2.59 1.94-5.2 2.92-7.78zM6.1 18.09C3.12 16.65 1.11 13.53 1.11 10c0-1.3.23-2.48.72-3.59C3.25 10.3 4.67 14.2 6.1 18.09zm4.03-6.63l2.58 6.98c-.86.29-1.76.45-2.71.45-.79 0-1.57-.11-2.29-.33.81-2.38 1.62-4.74 2.42-7.1z"})}),Qt=(0,p.jsx)(oe.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,p.jsx)(oe.Path,{d:"M20 11.2H6.8l3.7-3.7-1-1L3.9 12l5.6 5.5 1-1-3.7-3.7H20z"})}),eo={edit:{opacity:0,scale:.2},hover:{opacity:1,scale:1,clipPath:"inset( 22% round 2px )"}},to={edit:{clipPath:"inset(0% round 0px)"},hover:{clipPath:"inset( 22% round 2px )"},tap:{clipPath:"inset(0% round 0px)"}},oo=()=>{const{urls:e}=(0,i.useSelect)((e=>({urls:e(B).getUrls()})),[]);function t(){e.listings&&(window.location.href=e.back)}return(0,p.jsx)(K,{children:({length:e})=>e<=1&&(0,p.jsxs)(x.__unstableMotion.div,{className:"woocommerce-email-editor__view-mode-toggle",transition:{duration:.2},animate:"edit",initial:"edit",whileHover:"hover",whileTap:"tap",children:[(0,p.jsx)(x.Button,{label:(0,_.__)("Close editor","woocommerce"),showTooltip:!0,tooltipPosition:"middle right",onClick:()=>{T("header_close_button_clicked"),(0,a.applyFilters)("woocommerce_email_editor_close_action_callback",t)()},children:(0,p.jsx)(x.__unstableMotion.div,{variants:to,children:(0,p.jsx)("div",{className:"woocommerce-email-editor__view-mode-toggle-icon",children:(0,p.jsx)(Fe,{className:"woocommerce-email-editor-icon__icon",icon:Xt,size:48})})})}),(0,p.jsx)(x.__unstableMotion.div,{className:"woocommerce-email-editor-icon",variants:eo,children:(0,p.jsx)(Fe,{icon:Qt})})]})})};function no({postId:e,postType:t,settings:o}){const{currentPost:n,onNavigateToEntityRecord:r,onNavigateToPreviousEntityRecord:s}=function(e,t,o){const[n,r]=(0,l.useReducer)(((e,{type:t,post:o,previousRenderingMode:n})=>"push"===t?[...e,{post:o,previousRenderingMode:n}]:"pop"===t&&e.length>1?e.slice(0,-1):e),[{post:{postId:e,postType:t}}]),{post:s,previousRenderingMode:a}=n[n.length-1],{getRenderingMode:c}=(0,i.useSelect)($.store),{setRenderingMode:d}=(0,i.useDispatch)($.store),m=(0,l.useCallback)((e=>{r({type:"push",post:{postId:e.postId,postType:e.postType},previousRenderingMode:c()}),d(o)}),[c,d,o]),p=(0,l.useCallback)((()=>{r({type:"pop"}),a&&d(a)}),[d,a]);return{currentPost:s,onNavigateToEntityRecord:m,onNavigateToPreviousEntityRecord:n.length>1?p:void 0}}(e,t,"post-only"),{post:a,template:c,isFullscreenEnabled:d}=(0,i.useSelect)((e=>{const{getEntityRecord:t}=e(ee.store),{getEditedPostTemplate:o}=e(B),r=t("postType",n.postType,n.postId);return{template:"wp_template"!==n.postType?o():null,post:r,isFullscreenEnabled:e(B).isFeatureActive("fullscreenMode")}}),[n.postType,n.postId]),{isFullScreenForced:m,displaySendEmailButton:u}=o,{removeEditorPanel:_}=(0,i.useDispatch)($.store);(0,l.useEffect)((()=>{_("post-status")}),[_]);const[g]=Ne(),h=(0,l.useMemo)((()=>({...o,onNavigateToEntityRecord:r,onNavigateToPreviousEntityRecord:s,defaultRenderingMode:"wp_template"===n.postType?"post-only":"template-locked",supportsTemplateMode:!0})),[o,r,s,n.postType]);return!a||"wp_template"!==n.postType&&!c?(0,p.jsx)("div",{className:"spinner-container",children:(0,p.jsx)(x.Spinner,{style:{width:"80px",height:"80px"}})}):(P("editor_layout_loaded"),(0,p.jsx)(x.SlotFillProvider,{children:(0,p.jsxs)($.ErrorBoundary,{canCopyContent:!0,children:[(0,p.jsx)(ve.CommandMenu,{}),(0,p.jsxs)(Z,{postId:n.postId,postType:n.postType,settings:h,templateId:c&&c.id,styles:g,children:[(0,p.jsx)($.AutosaveMonitor,{}),(0,p.jsx)($.LocalAutosaveMonitor,{}),(0,p.jsx)($.UnsavedChangesWarning,{}),(0,p.jsx)($.EditorKeyboardShortcutsRegister,{}),(0,p.jsx)($.PostLockedModal,{}),(0,p.jsx)(qe,{}),(0,p.jsx)(jt,{}),(0,p.jsx)(Bt,{}),(0,p.jsx)(J,{isActive:m||d}),(m||d)&&(0,p.jsx)(oo,{}),!m&&(0,p.jsx)(It,{}),"wp_template"===n.postType?(0,p.jsx)(Vt,{}):(0,p.jsx)(Ot,{}),u&&(0,p.jsx)($t,{}),(0,p.jsx)(Jt,{}),(0,p.jsx)(Kt,{})]})]})}))}const ro=window.wp.dataControls;function so(e){return{type:"CHANGE_PREVIEW_STATE",state:{isModalOpened:e}}}function io(e){return{type:"CHANGE_PREVIEW_STATE",state:{toEmail:e}}}const lo=e=>async({registry:t})=>{const o=t.select(B).getEmailPostId();t.dispatch(ee.store).editEntityRecord("postType",M,o,{template:e})};function*ao(e){if(!(0,i.select)(B).getPreviewState().isSendingPreviewEmail){yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:null,isSendingPreviewEmail:!0}};try{const t=(0,i.select)(B).getEmailPostId();yield(0,ro.apiFetch)({path:"/woocommerce-email-editor/v1/send_preview_email",method:"POST",data:{email:e,postId:t}}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:Tt.SUCCESS,isSendingPreviewEmail:!1}},T("sent_preview_email",{postId:t,email:e})}catch(t){T("sent_preview_email_error",{email:e}),yield{type:"CHANGE_PREVIEW_STATE",state:{sendingPreviewStatus:Tt.ERROR,isSendingPreviewEmail:!1,errorMessage:JSON.stringify(t?.error)}}}}}function co(e){return{type:"SET_IS_FETCHING_PERSONALIZATION_TAGS",state:{isFetching:e}}}function mo(e){return{type:"SET_PERSONALIZATION_TAGS_LIST",state:{list:e}}}function po(e,t){switch(t.type){case"CHANGE_PREVIEW_STATE":return{...e,preview:{...e.preview,...t.state}};case"CHANGE_PERSONALIZATION_TAGS_STATE":case"SET_IS_FETCHING_PERSONALIZATION_TAGS":case"SET_PERSONALIZATION_TAGS_LIST":return{...e,personalizationTags:{...e.personalizationTags,...t.state}};case"SET_PERSONALIZATION_TAGS":return{...e,personalizationTags:{...e.personalizationTags,list:t.personalizationTags}};default:return e}}function uo(e){return e?.content&&"function"==typeof e.content?e.content(e):e?.blocks?(0,H.serialize)(e.blocks):e?.content?e.content:""}const _o=new WeakMap;function go(e){let t=_o.get(e);return t||(t={...e,get blocks(){return(0,H.parse)(e.content)}},_o.set(e,t)),t}function ho(e){return e?{...e,title:e?.title?.raw||e?.title||"",content:e?.content?.raw||e?.content||""}:null}const yo=(0,i.createRegistrySelector)((e=>(t,o)=>!!e(Mt.store).get(B,o))),xo=(0,i.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId();return!!e(ee.store).hasEditsForEntityRecord("postType",M,t)})),wo=(0,i.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId(),o=e(ee.store).getEntityRecord("postType",M,t);if(!o)return!0;const{content:n}=o;return!n.raw})),fo=(0,i.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId(),o=e(ee.store).getEntityRecord("postType",M,t);return!!o&&"sent"===o.status})),bo=(0,i.createRegistrySelector)((e=>()=>{const t=e(B).getEmailPostId(),o=e(ee.store).getEditedEntityRecord("postType",M,t);return o?uo(o):""})),vo=(0,i.createRegistrySelector)((e=>()=>e(ee.store).getEntityRecords("postType",M,{per_page:30,status:"publish,sent"})?.filter((e=>""!==e?.content?.raw))||[])),jo=(0,i.createRegistrySelector)((e=>(0,i.createSelector)((()=>e(ee.store).getBlockPatterns().filter((({templateTypes:e})=>Array.isArray(e)&&e.includes("email-template"))).map(go)),(()=>[e(ee.store).getBlockPatterns()])))),ko=(0,i.createRegistrySelector)((e=>()=>e(ee.store).canUser("create",{kind:"postType",name:"wp_template"})));function So(e,t){return ko()?e(ee.store).getEditedEntityRecord("postType","wp_template",t):ho(e(ee.store).getEntityRecord("postType","wp_template",t,{context:"view"}))}const Co=(0,i.createRegistrySelector)((e=>()=>{const t=e($.store).getEditedPostAttribute("template");if(t){const o=e(ee.store).getEntityRecords("postType","wp_template",{per_page:-1,context:"view"})?.find((e=>e.slug===t));return o?So(e,o.id):ho(o)}const o=e(ee.store).getDefaultTemplateId({slug:"email-general"});return So(e,o)})),Eo=(0,i.createRegistrySelector)((e=>()=>{if("wp_template"===e($.store).getCurrentPostType()){const t=e($.store).getCurrentPostId();return e(ee.store).getEditedEntityRecord("postType","wp_template",t)}return Co()})),To=()=>{const e=Eo();return e?uo(e):""},Po=(0,i.createRegistrySelector)((e=>()=>{const t=e(B).getGlobalStylesPostId();return{postId:t,canEdit:e(ee.store).canUser("update",{kind:"root",name:"globalStyles",id:t})}})),No=(0,i.createRegistrySelector)((e=>()=>{const{postId:t,canEdit:o}=Po();return t&&void 0!==o&&t?o?e(ee.store).getEditedEntityRecord("postType","wp_global_styles",t):ho(e(ee.store).getEntityRecord("postType","wp_global_styles",t,{context:"view"})):null})),Bo=(0,i.createRegistrySelector)((e=>()=>e(ee.store).getEntityRecords("postType","wp_template",{per_page:-1,post_type:M,context:"view"})?.filter((e=>e.post_types.includes(M)))));function Mo(e){return e.postId}function Io(e){return e.editorSettings}function zo(e){return e.editorSettings.__experimentalFeatures.color.palette}function Fo(e){return e.preview}function Ao(e){return e.personalizationTags}function Lo(e){return e.personalizationTags.list}function Ro(e){return e.theme.styles}function Oo(e){return e.theme}function Ho(e){return e.styles.globalStylesPostId}function Vo(e){return e.urls}function*Do(){const e=yield(0,i.select)(B),t=e.personalizationTags?.isFetching;if(!t){yield co(!0);try{const e=yield(0,ro.apiFetch)({path:"/woocommerce-email-editor/v1/get_personalization_tags",method:"GET"});yield mo(e.result)}finally{yield co(!1)}}}const Go=window.wp.mediaUtils,$o=e=>{(0,a.doAction)("woocommerce_email_editor_events",e.detail)};window.addEventListener("unload",(function(){S&&E.removeEventListener(C,$o)}));const Wo=(...e)=>{const t=(0,i.select)($.store).isInserterOpened(),o=!!document.getElementsByClassName("block-editor-inserter__quick-inserter").length;let n="other_inserter";t?n="inserter_sidebar":o&&(n="quick_inserter");const r=e[0],s=e[5];!1===Array.isArray(r)&&"object"==typeof r&&T(`${n}_library_block_selected`,{blockName:r.name}),Array.isArray(r)&&s&&s.patternName&&T(`${n}_library_pattern_selected`,{patternName:s.patternName})},Uo={"core/editor":{autosave:"editor_content_auto_saved",setDeviceType:e=>{T(`header_preview_dropdown_${e.toLowerCase()}_selected`)},setRenderingMode:e=>{(0,i.select)($.store).getRenderingMode()!==e&&document.querySelector(`[aria-label="${(0,_.__)("View options")}"]`)&&T("preview_dropdown_rendering_mode_changed",{renderingMode:e})}},core:{deleteEntityRecord:(e,t,o)=>{t===M&&o===I&&T("trash_modal_move_to_trash_button_clicked")}},"core/block-editor":{insertBlock:Wo,insertBlocks:Wo},"core/preferences":{set:(e,t,o)=>{if((0,i.select)(Mt.store).get(e,t)===o)return;const n={focusMode:"focus_mode_toggle",fullscreenMode:"full_screen_mode_toggle",distractionFree:"distraction_free_toggle",fixedToolbar:"fixed_toolbar_toggle"};n[t]&&T(n[t],{isEnabled:o})}},"core/commands":{open:"command_menu_opened",close:"command_menu_closed"}},qo={},Zo={};let Jo=[];function Yo(e){Jo.forEach((t=>{const o=e.target?.matches?.(t.selector)?e.target:e.target?.closest?.(t.selector);o&&("function"==typeof t.track?t.track(o,e):T(t.track))}))}const Ko=window.wp.isShallowEqual;var Xo=o.n(Ko);function Qo(e){const t=(0,l.useRef)(e);return Xo()(e,t.current)||(t.current=e),t.current}const en=[],tn=(0,a.applyFilters)("woocommerce_email_editor_wrap_editor_component",(function(){const{postId:e,settings:t}=(0,i.useSelect)((e=>({postId:e(B).getEmailPostId(),settings:e(B).getInitialEditorSettings()})),[]);return(()=>{const{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o}=Wt(),{editedContent:n,editedTemplateContent:r}=(0,i.useSelect)((e=>({editedContent:e(B).getEditedEmailContent(),editedTemplateContent:e(B).getCurrentTemplateContent()}))),s=Qo(n),c=Qo(r),d=(0,l.useCallback)((()=>((e,t,{addValidationNotice:o,hasValidationNotice:n,removeValidationNotice:r})=>{const s=(0,a.applyFilters)("woocommerce_email_editor_content_validation_rules",en);let i=!0;return s.forEach((({id:s,testContent:l,message:a,actions:c})=>{l(e+t)?(o(s,a,c),i=!1):n(s)&&r(s)})),i})(s,c,{addValidationNotice:e,hasValidationNotice:t,removeValidationNotice:o})),[s,c,e,o,t]);(0,l.useEffect)((()=>((0,a.addFilter)("editor.preSavePost","woocommerce/email-editor/validate-content",(async e=>{if(!d())throw new Error;return e})),()=>{(0,a.removeFilter)("editor.preSavePost","woocommerce/email-editor/validate-content")})),[d]),(0,l.useEffect)((()=>{const e=(0,i.subscribe)((()=>{t()&&d()}),ee.store);return()=>e()}),[t,d]),t()})(),(0,p.jsx)(l.StrictMode,{children:(0,p.jsx)(no,{postId:e,postType:M,settings:t})})}));window.addEventListener("DOMContentLoaded",(()=>{!function(e){const t=document.getElementById(e);t&&(S&&E.addEventListener(C,$o),(0,a.applyFilters)("woocommerce_email_editor_events_tracking_enabled",!1)&&(0,i.use)((e=>({dispatch:t=>{const o="object"==typeof t?t.name:t,n=e.dispatch(o),r=Uo[o];if(!r)return n;qo[o]||(qo[o]={}),Zo[o]||(Zo[o]={});for(const[e,t]of Object.entries(r))Zo[o][e]||(Zo[o][e]=n[e],qo[o][e]=(...n)=>{try{"function"==typeof t?t(...n):"string"==typeof t&&T(t)}catch(e){console.error("Error tracking event",e)}return Zo[o][e](...n)}),n[e]=qo[o][e];return n}}))),(0,a.applyFilters)("woocommerce_email_editor_events_tracking_enabled",!1)&&(Jo=[{track:"header_preview_dropdown_preview_in_new_tab_selected",selector:".editor-preview-dropdown__button-external"},{track:()=>{const e=document.getElementsByClassName("is-collapsed editor-collapsible-block-toolbar").length;T("header_blocks_tool_button_clicked",{isBlockToolsCollapsed:e})},selector:".editor-collapsible-block-toolbar__toggle"},{track:e=>{const t=e.classList.contains("is-opened");T("header_more_menu_dropdown_toggle",{isOpened:t})},selector:`.components-dropdown-menu__toggle[aria-label="${(0,_.__)("Options")}"]`},{track:e=>{(e.textContent===(0,_.__)("Save")&&"false"===e.getAttribute("aria-disabled")||e.textContent===(0,_.__)("Saving…"))&&T("header_save_button_clicked")},selector:".editor-post-publish-button"},{track:"header_save_email_button_clicked",selector:".editor-post-saved-state.is-saving"},{track:"inserter_sidebar_library_close_icon_clicked",selector:".block-editor-inserter__menu .block-editor-tabbed-sidebar__close-button"},{track:e=>{const t=e.classList.contains("is-opened");T("header_preview_dropdown_clicked",{isOpened:t})},selector:".editor-preview-dropdown__toggle"},{track:()=>{T("sidebar_tab_selected",{tab:"document"})},selector:'[data-tab-id="edit-post/document"]'},{track:()=>{T("sidebar_tab_selected",{tab:"block"})},selector:'[data-tab-id="edit-post/block"]'},{track:e=>{const t=e.classList.contains("is-pressed");T("header_inserter_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__inserter-toggle"},{track:e=>{const t=e.classList.contains("is-pressed");T("header_listview_sidebar_clicked",{isOpened:t})},selector:".editor-document-tools__document-overview-toggle"},{track:e=>{T("command_bar_command_clicked",{command:e.dataset?.value})},selector:'.commands-command-menu__container [role="option"]'}],document.addEventListener("click",Yo)),(()=>{const e=(0,i.createReduxStore)(B,{actions:n,controls:ro.controls,selectors:r,resolvers:s,reducer:po,initialState:{postId:I,editorSettings:window.WooCommerceEmailEditor.editor_settings,theme:window.WooCommerceEmailEditor.editor_theme,styles:{globalStylesPostId:window.WooCommerceEmailEditor.user_theme_post_id},urls:window.WooCommerceEmailEditor.urls,preview:{toEmail:window.WooCommerceEmailEditor.current_wp_user_email,isModalOpened:!1,isSendingPreviewEmail:!1,sendingPreviewStatus:null},personalizationTags:{list:[],isFetching:!1}}});(0,i.register)(e)})(),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/layout/addAttribute",xe),(0,a.addFilter)("editor.BlockListBlock","woocommerce-email-editor/with-layout-styles",be),(0,a.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-inspector-controls",we),(0,a.addFilter)("editor.BlockEdit","woocommerce-email-editor/filter-set-url-attribute",O),(0,a.addFilter)("editor.BlockEdit","woocommerce-email-editor/deactivate-stack-on-mobile",u),(0,a.addFilter)("editor.BlockEdit","woocommerce-email-editor/hide-expand-on-click",h),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/deactivate-image-filter",((e,t)=>"core/image"===t?{...e,supports:{...e.supports,filter:{duetone:!1}}}:e)),(0,y.unregisterFormatType)("core/image"),(0,y.unregisterFormatType)("core/code"),(0,y.unregisterFormatType)("core/language"),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-columns-layout",((e,t)=>"core/columns"===t||"core/column"===t?{...e,supports:{...e.supports,layout:!1}}:e)),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-group-variations",((e,t)=>"core/group"===t?{...e,variations:e.variations.filter((e=>"group"===e.name)),supports:{...e.supports,layout:!1}}:e)),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-button",((e,t)=>"core/button"===t?{...e,styles:[]}:e)),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-buttons",((e,t)=>"core/buttons"===t?{...e,supports:{...e.supports,layout:!1,__experimentalEmailFlexLayout:!0}}:e)),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-column",((e,t)=>"core/column"===t?{...e,supports:{...e.supports,background:{backgroundImage:!0}}}:e)),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-columns",((e,t)=>"core/columns"===t?{...e,supports:{...e.supports,background:{backgroundImage:!0}}}:e)),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-post-content",((e,t)=>{return"core/post-content"===t?{...e,edit:(o=e.edit,function({context:e,__unstableLayoutClassNames:t}){const{postId:n,postType:r}=e;return n&&r?(0,p.jsx)(o,{context:e,__unstableLayoutClassNames:t}):(0,p.jsx)(g,{layoutClassNames:t})})}:e;var o})),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/change-quote",((e,t)=>"core/quote"===t?{...e,styles:[],supports:{...e.supports,align:[]}}:e)),(0,y.registerFormatType)("woocommerce-email-editor/shortcode",{name:"woocommerce-email-editor/shortcode",title:(0,_.__)("Personalization Tags","woocommerce"),className:"woocommerce-email-editor-personalization-tags",tagName:"span",attributes:{},edit:L}),(0,y.registerFormatType)("woocommerce-email-editor/link-shortcode",{name:"woocommerce-email-editor/link-shortcode",title:(0,_.__)("Personalization Tags Link","woocommerce"),className:"woocommerce-email-editor-personalization-tags-link",tagName:"a",attributes:{"data-link-href":"data-link-href",contenteditable:"contenteditable",style:"style"},edit:null}),(0,a.addFilter)("editor.BlockEdit","woocommerce-email-editor/with-live-content-update",R),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/block-support",(e=>e.supports?.shadow?{...e,supports:{...e.supports,shadow:!1}}:e)),(0,a.addFilter)("blocks.registerBlockType","woocommerce-email-editor/disable-social-link-variations",((e,t)=>"core/social-link"===t?{...e,variations:e.variations.filter((e=>V.includes(e.name))),supports:{...e.supports,layout:!1}}:e)),(0,H.registerBlockVariation)("core/social-links",{name:"social-links-default",title:"Social Icons",attributes:{openInNewTab:!0,showLabels:!1,align:"center",className:"is-style-logos-only"},isDefault:!0,innerBlocks:[{name:"core/social-link",attributes:{service:"wordpress",url:"https://wordpress.org"}},{name:"core/social-link",attributes:{service:"facebook",url:"https://www.facebook.com/WordPress/"}},{name:"core/social-link",attributes:{service:"x",url:"https://x.com/WordPress"}}]}),(0,a.addFilter)("editor.BlockEdit","woocommerce-email-editor/disable-social-links-icon-color",D),(0,a.addAction)("core.registerPostTypeSchema","woocommerce-email-editor/modify-move-to-trash-action",(e=>{ce(e)})),(0,a.addAction)("core.registerPostTypeActions","woocommerce-email-editor/modify-move-to-trash-action",(e=>{ce(e)})),(0,c.registerCoreBlocks)(),(0,a.addFilter)("editor.MediaUpload","woocommerce/email-editor/replace-media-upload",(()=>Go.MediaUpload)),(()=>{const e={"You’ve tried to select a block that is part of a template that may be used elsewhere on your site. Would you like to edit the template?":{domain:"default",replacementText:(0,_.__)("You’ve tried to select a block that is part of a template that may be used in other emails. Would you like to edit the template?","woocommerce")}};(0,a.addFilter)("i18n.gettext","woocommerce/email-editor/override-text",((t,o,n)=>e[o]&&e[o].domain===(n||"default")?e[o].replacementText:t))})(),(0,l.createRoot)(t).render((0,p.jsx)(tn,{})))}("woocommerce-email-editor")}))},192:e=>{var t=function(e){return function(e){return!!e&&"object"==typeof e}(e)&&!function(e){var t=Object.prototype.toString.call(e);return"[object RegExp]"===t||"[object Date]"===t||function(e){return e.$$typeof===o}(e)}(e)},o="function"==typeof Symbol&&Symbol.for?Symbol.for("react.element"):60103;function n(e,t){return!1!==t.clone&&t.isMergeableObject(e)?l((o=e,Array.isArray(o)?[]:{}),e,t):e;var o}function r(e,t,o){return e.concat(t).map((function(e){return n(e,o)}))}function s(e){return Object.keys(e).concat(function(e){return Object.getOwnPropertySymbols?Object.getOwnPropertySymbols(e).filter((function(t){return Object.propertyIsEnumerable.call(e,t)})):[]}(e))}function i(e,t){try{return t in e}catch(e){return!1}}function l(e,o,a){(a=a||{}).arrayMerge=a.arrayMerge||r,a.isMergeableObject=a.isMergeableObject||t,a.cloneUnlessOtherwiseSpecified=n;var c=Array.isArray(o);return c===Array.isArray(e)?c?a.arrayMerge(e,o,a):function(e,t,o){var r={};return o.isMergeableObject(e)&&s(e).forEach((function(t){r[t]=n(e[t],o)})),s(t).forEach((function(s){(function(e,t){return i(e,t)&&!(Object.hasOwnProperty.call(e,t)&&Object.propertyIsEnumerable.call(e,t))})(e,s)||(i(e,s)&&o.isMergeableObject(t[s])?r[s]=function(e,t){if(!t.customMerge)return l;var o=t.customMerge(e);return"function"==typeof o?o:l}(s,o)(e[s],t[s],o):r[s]=n(t[s],o))})),r}(e,o,a):n(o,a)}l.all=function(e,t){if(!Array.isArray(e))throw new Error("first argument should be an array");return e.reduce((function(e,o){return l(e,o,t)}),{})};var a=l;e.exports=a}},o={};function n(e){var r=o[e];if(void 0!==r)return r.exports;var s=o[e]={exports:{}};return t[e](s,s.exports,n),s.exports}n.m=t,e=[],n.O=(t,o,r,s)=>{if(!o){var i=1/0;for(d=0;d<e.length;d++){for(var[o,r,s]=e[d],l=!0,a=0;a<o.length;a++)(!1&s||i>=s)&&Object.keys(n.O).every((e=>n.O[e](o[a])))?o.splice(a--,1):(l=!1,s<i&&(i=s));if(l){e.splice(d--,1);var c=r();void 0!==c&&(t=c)}}return t}s=s||0;for(var d=e.length;d>0&&e[d-1][2]>s;d--)e[d]=e[d-1];e[d]=[o,r,s]},n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var o in t)n.o(t,o)&&!n.o(e,o)&&Object.defineProperty(e,o,{enumerable:!0,get:t[o]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t),n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e={57:0,350:0};n.O.j=t=>0===e[t];var t=(t,o)=>{var r,s,[i,l,a]=o,c=0;if(i.some((t=>0!==e[t]))){for(r in l)n.o(l,r)&&(n.m[r]=l[r]);if(a)var d=a(n)}for(t&&t(o);c<i.length;c++)s=i[c],n.o(e,s)&&e[s]&&e[s][0](),e[s]=0;return n.O(d)},o=globalThis.webpackChunk_woocommerce_email_editor=globalThis.webpackChunk_woocommerce_email_editor||[];o.forEach(t.bind(null,0)),o.push=t.bind(null,o.push.bind(o))})();var r=n.O(void 0,[350],(()=>n(384)));r=n.O(r)})();