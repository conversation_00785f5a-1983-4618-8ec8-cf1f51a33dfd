/*! For license information please see email-editor-integration.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,o,t)=>{var r=t(51609),n=Symbol.for("react.element"),i=Symbol.for("react.fragment"),a=Object.prototype.hasOwnProperty,l=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function c(e,o,t){var r,i={},c=null,d=null;for(r in void 0!==t&&(c=""+t),void 0!==o.key&&(c=""+o.key),void 0!==o.ref&&(d=o.ref),o)a.call(o,r)&&!s.hasOwnProperty(r)&&(i[r]=o[r]);if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===i[r]&&(i[r]=o[r]);return{$$typeof:n,type:e,key:c,ref:d,props:i,_owner:l.current}}o.Fragment=i,o.jsx=c,o.jsxs=c},39793:(e,o,t)=>{e.exports=t(94931)},51609:e=>{e.exports=window.React}},o={};const t=window.wp.hooks,r=window.wp.blocks,n=window.wp.i18n,i=window.wp.element,a=window.wp.data,l=window.wp.components,s=window.wp.editor;var c=function t(r){var n=o[r];if(void 0!==n)return n.exports;var i=o[r]={exports:{}};return e[r](i,i.exports,t),i.exports}(39793);function d(){return(0,c.jsx)("div",{style:{margin:"20vh auto",maxWidth:400,padding:20,backgroundColor:"#fff",borderRadius:4,boxShadow:"0 0 10px 0 rgba(0, 0, 0, 0.1)",textAlign:"center",color:"#000"},children:(0,c.jsxs)(l.__experimentalText,{children:[" ",(0,n.__)("Autogenerated content","woocommerce")," "]})})}const m=(e,o)=>{e?.current?.contentWindow?.document.body&&(e.current.contentWindow.document.body.style.overflow="hidden",e.current.contentWindow.document.body.style.pointerEvents="none",e.current.contentWindow.document.body.style.backgroundColor=o?"#00000059":e.current.contentWindow?.document?.bgColor)};function _(){const{postSlug:e}=(0,a.useSelect)((e=>({postSlug:e(s.store).getCurrentPost()?.slug})),[]),o=(0,i.useRef)(null),[t,r]=(0,i.useState)(!1),l=window.WooCommerceEmailEditor?.block_preview_url;return(0,i.useEffect)((()=>{if(!e)return;const t=(r=e||"",window.WooCommerceEmailEditor.email_types.find((e=>e.value===r))?.id);var r;t&&o.current&&((e,o)=>{e?.current?.contentWindow?.location.replace(o)})(o,`${l}&type=${t}`)}),[e,o,l]),(0,c.jsxs)("div",{style:{position:"relative"},children:[(0,c.jsx)("iframe",{style:{width:"100%",height:o?.current?.contentWindow?.document?.body?.clientHeight||"750px",backgroundColor:"initial",minHeight:"100px"},ref:o,src:`${l}&type=WC_Email_Customer_Processing_Order`,title:(0,n.__)("Email preview frame","woocommerce"),onMouseEnter:()=>{r(!0),m(o,!0)},onMouseLeave:()=>{r(!1),m(o,!1)}}),t&&(0,c.jsx)("div",{style:{position:"absolute",top:"50%",left:"50%",transform:"translate(-50%, -50%)",zIndex:1e3,pointerEvents:"none"},children:(0,c.jsx)(d,{})})]})}const u={title:(0,n.__)("Woo Email Content","woocommerce"),category:"text",attributes:{},edit:function(){return(0,c.jsx)(_,{})},save:function(){return(0,c.jsx)("div",{children:"##WOO_CONTENT##"})}},p="woocommerce/email-editor-integration",w=window.wp.coreData;function h({debouncedRecordEvent:e}){const[o,t]=(0,w.useEntityProp)("postType","wp_template","woocommerce_data"),r=(0,i.useRef)(null),a=(0,i.useCallback)((r=>{t({...o,sender_settings:{...o?.sender_settings,from_name:r}}),e("email_from_name_input_updated",{value:r})}),[o,t]),s=(0,i.useCallback)((n=>{t({...o,sender_settings:{...o?.sender_settings,from_address:n}}),r.current&&(r.current.checkValidity(),r.current.reportValidity()),e("email_from_address_input_updated",{value:n})}),[o,t]);return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("h2",{children:(0,n.__)("Sender Options","woocommerce")}),(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)("p",{children:(0,n.__)("This is how your sender name and email address would appear in outgoing WooCommerce emails.","woocommerce")})}),(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)(l.TextControl,{className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,n.__)("“from” name","woocommerce"),name:"from_name",type:"text",value:o?.sender_settings?.from_name||"",onChange:a})}),(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)(l.TextControl,{ref:r,className:"woocommerce-email-sidebar-template-settings-sender-options-input",label:(0,n.__)("“from” email","woocommerce"),name:"from_email",type:"email",value:o?.sender_settings?.from_address||"",onChange:s,required:!0})})]})}function x(e){var o,t,r="";if("string"==typeof e||"number"==typeof e)r+=e;else if("object"==typeof e)if(Array.isArray(e)){var n=e.length;for(o=0;o<n;o++)e[o]&&(t=x(e[o]))&&(r&&(r+=" "),r+=t)}else for(t in e)e[t]&&(r&&(r+=" "),r+=t);return r}const g=function(){for(var e,o,t=0,r="",n=arguments.length;t<n;t++)(e=arguments[t])&&(o=x(e))&&(r&&(r+=" "),r+=o);return r},b=window.wp.primitives,v=(0,c.jsx)(b.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(b.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})}),f=(0,c.jsx)(b.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(b.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm11.53-1.47-1.06-1.06L11 12.94l-1.47-1.47-1.06 1.06L11 15.06l4.53-4.53Z"})}),j=(0,c.jsx)(b.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,c.jsx)(b.Path,{d:"M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21ZM15.5303 8.46967C15.8232 8.76256 15.8232 9.23744 15.5303 9.53033L13.0607 12L15.5303 14.4697C15.8232 14.7626 15.8232 15.2374 15.5303 15.5303C15.2374 15.8232 14.7626 15.8232 14.4697 15.5303L12 13.0607L9.53033 15.5303C9.23744 15.8232 8.76256 15.8232 8.46967 15.5303C8.17678 15.2374 8.17678 14.7626 8.46967 14.4697L10.9393 12L8.46967 9.53033C8.17678 9.23744 8.17678 8.76256 8.46967 8.46967C8.76256 8.17678 9.23744 8.17678 9.53033 8.46967L12 10.9393L14.4697 8.46967C14.7626 8.17678 15.2374 8.17678 15.5303 8.46967Z"})}),y=(0,c.jsx)(b.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(b.Path,{fillRule:"evenodd",clipRule:"evenodd",d:"M12 18.5a6.5 6.5 0 1 1 0-13 6.5 6.5 0 0 1 0 13ZM4 12a8 8 0 1 1 16 0 8 8 0 0 1-16 0Zm9 1V8h-1.5v3.5h-2V13H13Z"})}),C=[{value:"enabled",label:(0,n.__)("Enabled","woocommerce"),icon:f,description:(0,n.__)("Email would be sent if trigger is met","woocommerce")},{value:"disabled",label:(0,n.__)("Inactive","woocommerce"),icon:j,description:(0,n.__)("Email would not be sent","woocommerce")},{value:"manual",label:(0,n.__)("Manually sent","woocommerce"),icon:y,description:(0,n.__)("Email can only be sent manually from the order screen","woocommerce")}];function E({className:e,recordEvent:o}){var t;const[r]=(0,w.useEntityProp)("postType","woo_email","woocommerce_data"),i=r?.is_manual;let s="enabled";i?s="manual":r?.enabled||(s="disabled");const d=null!==(t=C.find((e=>e.value===s)))&&void 0!==t?t:C[1];return(0,c.jsx)(l.PanelRow,{className:e,children:(0,c.jsxs)(l.Flex,{justify:"start",children:[(0,c.jsx)(l.FlexItem,{className:"editor-post-panel__row-label",children:(0,n.__)("Email Status","woocommerce")}),(0,c.jsx)(l.FlexItem,{children:(0,c.jsx)(l.Dropdown,{popoverProps:{placement:"bottom-start",offset:0,shift:!0},renderToggle:({isOpen:e,onToggle:o})=>(0,c.jsx)(l.Button,{variant:"tertiary",className:"editor-post-status__toggle",icon:d.icon,size:"compact",onClick:o,"aria-label":(0,n.sprintf)((0,n.__)("Change status: %s","woocommerce"),d.label),"aria-expanded":e,disabled:i,children:d.label}),renderContent:({onClose:e})=>(0,c.jsxs)("div",{style:{minWidth:230},children:[(0,c.jsxs)(l.Flex,{justify:"space-between",align:"center",style:{padding:"8px 0"},children:[(0,c.jsx)("h2",{className:"block-editor-inspector-popover-header__heading",style:{margin:0},children:(0,n.__)("Status","woocommerce")}),(0,c.jsx)(l.Button,{size:"small",className:"block-editor-inspector-popover-header__action",label:(0,n.__)("Close","woocommerce"),icon:v,onClick:e})]}),(0,c.jsx)(l.RadioControl,{selected:s,options:C.filter((e=>"manual"!==e.value)).map((e=>({label:e.label,value:e.value,description:e.description}))),onChange:t=>{(e=>{const t=(0,a.select)(w.store).getEditedEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id),r=t?.woocommerce_data||{};(0,a.dispatch)(w.store).editEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id,{woocommerce_data:{...r,enabled:e}}),o("email_status_changed",{status:e?"active":"inactive"})})("enabled"===t),e()},disabled:i})]})})})]})})}const R=({RichTextWithButton:e,recordEvent:o,debouncedRecordEvent:t})=>{var r;const[s]=(0,w.useEntityProp)("postType","woo_email","woocommerce_data"),[d,m]=(0,i.useState)(!!s?.bcc),[_,u]=(0,i.useState)(!!s?.cc);if(!s)return null;const p=(e,o)=>{const t=(0,a.select)(w.store).getEditedEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id),r=t?.woocommerce_data||{};(0,a.dispatch)(w.store).editEntityRecord("postType","woo_email",window.WooCommerceEmailEditor.current_post_id,{woocommerce_data:{...r,[e]:o}})},h=null!==(r=s?.preheader?.length)&&void 0!==r?r:0;return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)("br",{}),"customer_refunded_order"===s.email_type?(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(e,{attributeName:"subject_full",attributeValue:s.subject_full,updateProperty:p,label:(0,n.__)("Full Refund Subject","woocommerce"),placeholder:s.default_subject}),(0,c.jsx)("br",{}),(0,c.jsx)(e,{attributeName:"subject_partial",attributeValue:s.subject_partial,updateProperty:p,label:(0,n.__)("Partial Refund Subject","woocommerce"),placeholder:s.default_subject})]}):(0,c.jsx)(e,{attributeName:"subject",attributeValue:s.subject,updateProperty:p,label:(0,n.__)("Subject","woocommerce"),placeholder:s.default_subject}),(0,c.jsx)("br",{}),(0,c.jsx)(e,{attributeName:"preheader",attributeValue:s.preheader,updateProperty:p,label:(0,n.__)("Preview text","woocommerce"),help:(0,c.jsxs)("span",{className:g("woocommerce-settings-panel__preview-text-length",{"woocommerce-settings-panel__preview-text-length-warning":h>80,"woocommerce-settings-panel__preview-text-length-error":h>150}),children:[h,"/",150]}),placeholder:(0,n.__)("Shown as a preview in the inbox, next to the subject line.","woocommerce")}),(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)(l.BaseControl,{__nextHasNoMarginBottom:!0,label:(0,n.__)("Recipients","woocommerce"),id:"woocommerce-email-editor-recipients",children:null===s.recipient?(0,c.jsx)("p",{className:"woocommerce-email-editor-recipients-help",children:(0,n.__)("This email is sent to Customer.","woocommerce")}):(0,c.jsx)(l.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,name:"recipient","data-testid":"email_recipient",value:s.recipient,onChange:e=>{p("recipient",e)},help:(0,n.__)("Separate with commas to add multiple email addresses.","woocommerce")})})}),(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)(l.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,c.jsx)(l.ToggleControl,{__nextHasNoMarginBottom:!0,name:"add_cc",checked:_,label:(0,n.__)("Add CC","woocommerce"),onChange:e=>{u(e),e||p("cc",""),o("email_cc_toggle_clicked",{isEnabled:e})}})})}),_&&(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)(l.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,c.jsx)(l.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,"data-testid":"email_cc",value:s?.cc||"",onChange:e=>{p("cc",e),t("email_cc_input_updated",{value:e})},help:(0,n.__)("Add recipients who will receive a copy of the email. Separate multiple addresses with commas.","woocommerce")})})}),(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)(l.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,c.jsx)(l.ToggleControl,{__nextHasNoMarginBottom:!0,name:"add_bcc",checked:d,label:(0,n.__)("Add BCC","woocommerce"),onChange:e=>{m(e),e||p("bcc",""),o("email_bcc_toggle_clicked",{isEnabled:e})}})})}),d&&(0,c.jsx)(l.PanelRow,{children:(0,c.jsx)(l.BaseControl,{__nextHasNoMarginBottom:!0,children:(0,c.jsx)(l.TextControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,"data-testid":"email_bcc",value:s?.bcc||"",onChange:e=>{p("bcc",e),t("email_bcc_input_updated",{value:e})},help:(0,n.__)("Add recipients who will receive a hidden copy of the email. Separate multiple addresses with commas.","woocommerce")})})})]})};function P(){return(0,a.select)("core").getEditedEntityRecord("postType",window.WooCommerceEmailEditor.current_post_type,window.WooCommerceEmailEditor.current_post_id)?.woocommerce_data}function S(e){const o=document.createElement("input");return o.type="email",o.value=e,o.checkValidity()}function N(e){return e.split(",").filter((e=>!!e.trim()&&!S(e.trim())))}function T(e,o){return{id:`${e}-email-validation`,testContent:()=>{const o=P();return!(!(e in o)||!o[e])&&N(o[e]).length>0},get message(){var t;const r=N(null!==(t=P()[e])&&void 0!==t?t:"");return(0,n.sprintf)(o,r.join(","))},actions:[]}}const B={id:"sender-email-validation",testContent:()=>{var e;const o=P(),t=null!==(e=o?.sender_settings?.from_address)&&void 0!==e?e:"";return!!t.trim()&&!S(t.trim())},message:(0,n.__)('The "from" email address is invalid. Please enter a valid email address that will appear as the sender in outgoing WooCommerce emails.',"woocommerce"),actions:[]},k=T("recipient",(0,n.__)("One or more Recipient email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce")),W=T("cc",(0,n.__)("One or more CC email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce")),M=T("bcc",(0,n.__)("One or more BCC email addresses are invalid: “%s”. Please enter valid email addresses separated by commas.","woocommerce"));(0,t.addFilter)("woocommerce_email_editor_send_button_label",p,(()=>(0,n.__)("Save email","woocommerce"))),(0,t.addFilter)("woocommerce_email_editor_check_sending_method_configuration_link",p,(()=>"https://woocommerce.com/document/email-faq/")),(0,t.addFilter)("woocommerce_email_editor_trash_modal_should_permanently_delete",p,(()=>!0)),(0,r.registerBlockType)("woo/email-content",u),(0,t.addFilter)("woocommerce_email_editor_setting_sidebar_email_status_component",p,((e,o)=>()=>(0,c.jsx)(E,{recordEvent:o.recordEvent}))),(0,t.addFilter)("woocommerce_email_editor_setting_sidebar_extension_component",p,((e,o)=>()=>(0,c.jsx)(R,{RichTextWithButton:e,recordEvent:o.recordEvent,debouncedRecordEvent:o.debouncedRecordEvent}))),(0,t.addFilter)("woocommerce_email_editor_template_sections","my-plugin/template-settings",((e,o)=>[...e,{id:"my-custom-section",render:()=>(0,c.jsx)(h,{debouncedRecordEvent:o.debouncedRecordEvent})}])),(0,t.addFilter)("woocommerce_email_editor_content_validation_rules",p,(e=>[...e||[],B,k,W,M])),(window.wc=window.wc||{}).emailEditorIntegration={}})();