/*! For license information please see command-palette.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,o,t)=>{var r=t(51609),n=Symbol.for("react.element"),c=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),i=r.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,a={key:!0,ref:!0,__self:!0,__source:!0};function s(e,o,t){var r,s={},d=null,l=null;for(r in void 0!==t&&(d=""+t),void 0!==o.key&&(d=""+o.key),void 0!==o.ref&&(l=o.ref),o)c.call(o,r)&&!a.hasOwnProperty(r)&&(s[r]=o[r]);if(e&&e.defaultProps)for(r in o=e.defaultProps)void 0===s[r]&&(s[r]=o[r]);return{$$typeof:n,type:e,key:d,ref:l,props:s,_owner:i.current}}o.jsx=s,o.jsxs=s},39793:(e,o,t)=>{e.exports=t(94931)},51609:e=>{e.exports=window.React}},o={};const t=window.wp.plugins,r=window.wp.i18n,n=window.wp.primitives;var c=function t(r){var n=o[r];if(void 0!==n)return n.exports;var c=o[r]={exports:{}};return e[r](c,c.exports,t),c.exports}(39793);const i=(0,c.jsxs)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:[(0,c.jsx)(n.Path,{d:"m19 7.5h-7.628c-.3089-.87389-1.1423-1.5-2.122-1.5-.97966 0-1.81309.62611-2.12197 1.5h-2.12803v1.5h2.12803c.30888.87389 1.14231 1.5 2.12197 1.5.9797 0 1.8131-.62611 2.122-1.5h7.628z"}),(0,c.jsx)(n.Path,{d:"m19 15h-2.128c-.3089-.8739-1.1423-1.5-2.122-1.5s-1.8131.6261-2.122 1.5h-7.628v1.5h7.628c.3089.8739 1.1423 1.5 2.122 1.5s1.8131-.6261 2.122-1.5h2.128z"})]}),a=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{fillRule:"evenodd",d:"M5 5.5h14a.5.5 0 01.5.5v1.5a.5.5 0 01-.5.5H5a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 9.232A2 2 0 013 7.5V6a2 2 0 012-2h14a2 2 0 012 2v1.5a2 2 0 01-1 1.732V18a2 2 0 01-2 2H6a2 2 0 01-2-2V9.232zm1.5.268V18a.5.5 0 00.5.5h12a.5.5 0 00.5-.5V9.5h-13z",clipRule:"evenodd"})}),s=(0,c.jsx)(n.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,c.jsx)(n.Path,{d:"M11 12.5V17.5H12.5V12.5H17.5V11H12.5V6H11V11H6V12.5H11Z"})}),d=window.wp.element,l=window.wp.data,m=window.wp.coreData,w=window.wp.url,p=window.wc.tracks,u=window.wp.commands,g=window.wp.htmlEntities,h=({name:e,label:o,icon:t,callback:r,origin:n})=>{(0,l.dispatch)(u.store).registerCommand({name:e,label:(0,g.decodeEntities)(o),icon:t,callback:(...o)=>{(0,p.queueRecordEvent)("woocommerce_command_palette_submit",{name:e,origin:n}),r(...o)}})},_=()=>{const{currentPostType:e}=(0,l.useSelect)((e=>{const o=e("core/editor");if(!o)return{currentPostType:null};const{getCurrentPostType:t}=o;return{currentPostType:t()}})),{editedPostType:o}=(0,l.useSelect)((e=>{const o=e("core/edit-site");if(!o)return{editedPostType:null};const{getEditedPostType:t}=o;return{editedPostType:t()}}));return{editedPostType:o||e}};function y({search:e}){const{editedPostType:o}=_(),t=o?o+"-editor":null,n=(0,d.useRef)(null);(0,d.useEffect)((()=>(""!==e&&(clearTimeout(n.current),n.current=setTimeout((()=>{(0,p.recordEvent)("woocommerce_command_palette_search",{value:e,origin:t})}),300)),()=>{clearTimeout(n.current)})),[e,t]);const c="product",{records:i,isLoading:s}=(0,l.useSelect)((o=>{const{getEntityRecords:t}=o(m.store),r={search:e||void 0,per_page:10,orderby:e?"relevance":"date",status:["publish","future","draft","pending","private"]};return{records:t("postType",c,r),isLoading:!o(m.store).hasFinishedResolution("getEntityRecords",["postType",c,r])}}),[e]);return{commands:(0,d.useMemo)((()=>(null!=i?i:[]).map((e=>({name:c+"-"+e.id,searchLabel:e.title?.rendered+" "+e.id,label:e.title?.rendered?(0,g.decodeEntities)(e.title?.rendered):(0,r.__)("(no title)","woocommerce"),icon:a,callback:({close:o})=>{(0,p.queueRecordEvent)("woocommerce_command_palette_submit",{name:"woocommerce/product",origin:t});const r={post:e.id,action:"edit"},n=(0,w.addQueryArgs)("post.php",r);document.location=n,o()}})))),[i,t]),isLoading:s}}(0,t.registerPlugin)("woocommerce-commands-registration",{render:()=>{const{editedPostType:e}=_(),o=e?e+"-editor":null,{isCommandPaletteOpen:t}=(0,l.useSelect)((e=>{const{isOpen:o}=e(u.store);return{isCommandPaletteOpen:o()}}),[]),n=(0,d.useRef)(!1);return(0,d.useEffect)((()=>{t&&!n.current&&(0,p.recordEvent)("woocommerce_command_palette_open",{origin:o}),n.current=t}),[t,o]),(0,d.useEffect)((()=>{h({name:"woocommerce/add-new-product",label:(0,r.__)("Add new product","woocommerce"),icon:s,callback:()=>{document.location=(0,w.addQueryArgs)("post-new.php",{post_type:"product"})},origin:o}),h({name:"woocommerce/add-new-order",label:(0,r.__)("Add new order","woocommerce"),icon:s,callback:()=>{document.location=(0,w.addQueryArgs)("admin.php",{page:"wc-orders",action:"new"})},origin:o}),h({name:"woocommerce/view-products",label:(0,r.__)("Products","woocommerce"),icon:a,callback:()=>{document.location=(0,w.addQueryArgs)("edit.php",{post_type:"product"})},origin:o}),h({name:"woocommerce/view-orders",label:(0,r.__)("Orders","woocommerce"),icon:a,callback:()=>{document.location=(0,w.addQueryArgs)("admin.php",{page:"wc-orders"})},origin:o}),(0,l.dispatch)(u.store).registerCommandLoader({name:"woocommerce/product",hook:y}),window.hasOwnProperty("wcCommandPaletteSettings")&&window.wcCommandPaletteSettings.hasOwnProperty("settingsTabs")&&Array.isArray(window.wcCommandPaletteSettings.settingsTabs)&&window.wcCommandPaletteSettings.settingsTabs.forEach((e=>{(({label:e,tab:o,origin:t})=>{h({name:`woocommerce/settings-${o}`,label:(0,r.sprintf)((0,r.__)("WooCommerce Settings: %s","woocommerce"),e),icon:i,callback:()=>{document.location=(0,w.addQueryArgs)("admin.php",{page:"wc-settings",tab:o})},origin:t})})({label:e.label,tab:e.key,origin:o})}))}),[o]),null}}),(window.wc=window.wc||{}).commandPalette={}})();