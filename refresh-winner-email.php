<?php
/**
 * Refresh Winner Email System
 * 
 * This script refreshes the winner email system and shows the updated template
 * Run this by visiting: yoursite.com/refresh-winner-email.php
 */

// Load WordPress
require_once('wp-config.php');

// Prevent direct access from non-admin users
if (!current_user_can('administrator')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🔄 Refresh Winner Email System</h1>";

// Clear all email caches
delete_transient('woocommerce_emails');
delete_transient('wc_emails');

// Clear any WooCommerce caches
if (function_exists('wc_delete_shop_order_transients')) {
    wc_delete_shop_order_transients();
}

// Clear object cache
wp_cache_flush();

echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
echo "<strong>✅ All caches cleared</strong>";
echo "</div>";

// Deactivate and reactivate the plugin to force reload
$plugin_file = 'winner-notification-woocommerce/winner-notification-woocommerce.php';

if (is_plugin_active($plugin_file)) {
    deactivate_plugins($plugin_file);
    echo "<div style='color: blue; padding: 10px; border: 1px solid blue; margin: 10px 0;'>";
    echo "<strong>🔄 Plugin deactivated</strong>";
    echo "</div>";
    
    $result = activate_plugin($plugin_file);
    if (!is_wp_error($result)) {
        echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
        echo "<strong>✅ Plugin reactivated</strong>";
        echo "</div>";
    }
}

// Force WooCommerce to reload email classes
if (function_exists('WC')) {
    $mailer = WC()->mailer();
    $emails = $mailer->get_emails();
    
    echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
    echo "<strong>✅ WooCommerce email classes reloaded</strong>";
    echo "</div>";
    
    // Check the updated email settings
    if (isset($emails['WC_Email_Winner_Notification'])) {
        $winner_email = $emails['WC_Email_Winner_Notification'];
        
        echo "<h2>📧 Updated Email Settings</h2>";
        echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
        echo "<p><strong>Subject:</strong> " . $winner_email->get_subject() . "</p>";
        echo "<p><strong>Heading:</strong> " . $winner_email->get_heading() . "</p>";
        echo "<p><strong>Enabled:</strong> " . ($winner_email->is_enabled() ? 'Yes' : 'No') . "</p>";
        echo "</div>";
        
        // Show a preview of the email content
        echo "<h2>📋 Email Preview</h2>";
        echo "<div style='border: 2px solid #0049a3; border-radius: 8px; padding: 20px; margin: 20px 0; background: #f8f9fa;'>";
        echo "<h3 style='color: #0049a3; margin-top: 0;'>Email Header Preview:</h3>";
        echo "<div style='background: linear-gradient(135deg, #0049a3 0%, #00bcd4 100%); padding: 40px 20px; text-align: center; border-radius: 8px; color: white;'>";
        echo "<div style='background: rgba(255, 255, 255, 0.1); padding: 20px; border-radius: 8px; display: inline-block;'>";
        echo "<h1 style='color: #ffffff; font-size: 32px; margin: 0; text-shadow: 0 2px 4px rgba(0,0,0,0.3);'>";
        echo "🎉 YOU'VE WON! 🎉";
        echo "</h1>";
        echo "<p style='color: #ffffff; font-size: 18px; margin: 10px 0 0 0; opacity: 0.9;'>";
        echo "Congratulations on your competition victory!";
        echo "</p>";
        echo "</div>";
        echo "</div>";
        echo "</div>";
        
    } else {
        echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
        echo "<strong>❌ Winner notification email not found</strong>";
        echo "</div>";
    }
}

// Test email sending option
echo "<h2>🧪 Test the Updated Email</h2>";
echo "<p>Send a test email to see the new header design:</p>";

if (isset($_GET['send_test']) && $_GET['send_test'] === '1') {
    $current_user = wp_get_current_user();
    if ($current_user->ID) {
        echo "<p>Sending test email to: " . $current_user->user_email . "</p>";
        
        // Trigger the email
        do_action('competition_winner_selected', $current_user->ID, 1, 'TEST-' . time());
        
        echo "<div style='color: green; padding: 10px; border: 1px solid green; margin: 10px 0;'>";
        echo "<strong>📧 Test email sent! Check your inbox for the updated design.</strong>";
        echo "</div>";
    }
}

echo "<p><a href='?send_test=1' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Send Test Email</a></p>";

echo "<h2>🔧 Actions</h2>";
echo "<p><a href='/wp-admin/admin.php?page=wc-settings&tab=email&section=wc_email_winner_notification' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>Edit Email Settings</a></p>";
echo "<p><a href='/wp-admin/admin.php?page=wc-settings&tab=email' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>All Email Settings</a></p>";

echo "<p style='margin-top: 30px; color: #666;'>The email template has been updated with the new non-repetitive header design!</p>";
?>
