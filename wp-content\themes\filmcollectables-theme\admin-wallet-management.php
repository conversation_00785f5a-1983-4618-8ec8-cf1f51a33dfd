<?php
/**
 * User Wallet Management System
 * Allows administrators to manually adjust user wallet balances
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

/**
 * Add wallet management fields to user profile page
 */
function fc_add_wallet_fields_to_user_profile($user) {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    $wallet_balance = get_user_meta($user->ID, 'wallet_balance', true);
    $wallet_balance = $wallet_balance ? floatval($wallet_balance) : 0.00;
    
    // Get wallet transaction history
    $transactions = fc_get_user_wallet_transactions($user->ID);
    ?>
    <h3>💰 Wallet Management</h3>
    <table class="form-table">
        <tr>
            <th><label for="current_wallet_balance">Current Wallet Balance</label></th>
            <td>
                <strong style="font-size: 18px; color: #0073aa;">£<?php echo number_format($wallet_balance, 2); ?></strong>
                <p class="description">Current balance in the user's wallet</p>
            </td>
        </tr>
        <tr>
            <th><label for="wallet_adjustment_amount">Adjust Balance</label></th>
            <td>
                <input type="number" 
                       name="wallet_adjustment_amount" 
                       id="wallet_adjustment_amount" 
                       step="0.01" 
                       min="-999999" 
                       max="999999" 
                       placeholder="0.00"
                       style="width: 150px;" />
                <select name="wallet_adjustment_type" id="wallet_adjustment_type" style="margin-left: 10px;">
                    <option value="add">Add to balance</option>
                    <option value="subtract">Subtract from balance</option>
                    <option value="set">Set balance to</option>
                </select>
                <p class="description">
                    Enter amount to adjust the wallet balance. Use positive numbers only - the type dropdown determines if it's added or subtracted.
                </p>
            </td>
        </tr>
        <tr>
            <th><label for="wallet_adjustment_reason">Reason for Adjustment</label></th>
            <td>
                <select name="wallet_adjustment_reason" id="wallet_adjustment_reason" style="width: 200px;">
                    <option value="">Select reason...</option>
                    <option value="refund">Refund</option>
                    <option value="promotional_credit">Promotional Credit</option>
                    <option value="customer_service">Customer Service</option>
                    <option value="instant_win_prize">Instant Win Prize</option>
                    <option value="competition_prize">Competition Prize</option>
                    <option value="correction">Balance Correction</option>
                    <option value="other">Other</option>
                </select>
                <input type="text" 
                       name="wallet_adjustment_notes" 
                       id="wallet_adjustment_notes" 
                       placeholder="Additional notes (optional)" 
                       style="width: 300px; margin-left: 10px;" />
                <p class="description">Select the reason for this adjustment and add any additional notes</p>
            </td>
        </tr>
    </table>
    
    <?php if (!empty($transactions)): ?>
    <h4>Recent Wallet Transactions</h4>
    <table class="widefat" style="margin-top: 10px;">
        <thead>
            <tr>
                <th>Date</th>
                <th>Type</th>
                <th>Amount</th>
                <th>Balance After</th>
                <th>Reason</th>
                <th>Notes</th>
                <th>Admin</th>
            </tr>
        </thead>
        <tbody>
            <?php foreach (array_slice($transactions, 0, 10) as $transaction): ?>
            <tr>
                <td><?php echo date('Y-m-d H:i', strtotime($transaction->created_at)); ?></td>
                <td>
                    <?php if ($transaction->amount > 0): ?>
                        <span style="color: #46b450;">+£<?php echo number_format($transaction->amount, 2); ?></span>
                    <?php else: ?>
                        <span style="color: #dc3232;">-£<?php echo number_format(abs($transaction->amount), 2); ?></span>
                    <?php endif; ?>
                </td>
                <td>£<?php echo number_format($transaction->amount, 2); ?></td>
                <td>£<?php echo number_format($transaction->balance_after, 2); ?></td>
                <td><?php echo esc_html($transaction->reason); ?></td>
                <td><?php echo esc_html($transaction->notes); ?></td>
                <td><?php echo esc_html($transaction->admin_user); ?></td>
            </tr>
            <?php endforeach; ?>
        </tbody>
    </table>
    <?php if (count($transactions) > 10): ?>
        <p><em>Showing 10 most recent transactions. Total: <?php echo count($transactions); ?> transactions.</em></p>
    <?php endif; ?>
    <?php endif; ?>
    
    <style>
    .form-table th {
        width: 200px;
    }
    .widefat th, .widefat td {
        padding: 8px;
        border-bottom: 1px solid #ddd;
    }
    .widefat th {
        background: #f1f1f1;
        font-weight: 600;
    }
    </style>
    <?php
}
add_action('show_user_profile', 'fc_add_wallet_fields_to_user_profile');
add_action('edit_user_profile', 'fc_add_wallet_fields_to_user_profile');

/**
 * Save wallet adjustment when user profile is updated
 */
function fc_save_wallet_adjustment($user_id) {
    if (!current_user_can('manage_options')) {
        return;
    }
    
    // Check if wallet adjustment was submitted
    if (!isset($_POST['wallet_adjustment_amount']) || 
        empty($_POST['wallet_adjustment_amount']) || 
        !isset($_POST['wallet_adjustment_type']) ||
        !isset($_POST['wallet_adjustment_reason'])) {
        return;
    }
    
    $amount = floatval($_POST['wallet_adjustment_amount']);
    $type = sanitize_text_field($_POST['wallet_adjustment_type']);
    $reason = sanitize_text_field($_POST['wallet_adjustment_reason']);
    $notes = sanitize_text_field($_POST['wallet_adjustment_notes']);
    
    if ($amount <= 0) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Wallet adjustment amount must be greater than 0.</p></div>';
        });
        return;
    }
    
    if (empty($reason)) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Please select a reason for the wallet adjustment.</p></div>';
        });
        return;
    }
    
    // Get current balance
    $current_balance = get_user_meta($user_id, 'wallet_balance', true);
    $current_balance = $current_balance ? floatval($current_balance) : 0.00;
    
    // Calculate new balance based on adjustment type
    switch ($type) {
        case 'add':
            $new_balance = $current_balance + $amount;
            $transaction_amount = $amount;
            break;
        case 'subtract':
            $new_balance = $current_balance - $amount;
            $transaction_amount = -$amount;
            break;
        case 'set':
            $new_balance = $amount;
            $transaction_amount = $amount - $current_balance;
            break;
        default:
            return;
    }
    
    // Ensure balance doesn't go negative
    if ($new_balance < 0) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Wallet balance cannot be negative. Adjustment not applied.</p></div>';
        });
        return;
    }
    
    // Update wallet balance - delete existing entries first to prevent duplicates
    global $wpdb;
    $wpdb->delete(
        $wpdb->usermeta,
        array('user_id' => $user_id, 'meta_key' => 'wallet_balance'),
        array('%d', '%s')
    );
    add_user_meta($user_id, 'wallet_balance', $new_balance, true);
    wp_cache_delete($user_id, 'user_meta');
    
    // Record transaction
    fc_record_wallet_transaction($user_id, $transaction_amount, $new_balance, $reason, $notes);
    
    // Show success message
    add_action('admin_notices', function() use ($new_balance, $type, $amount) {
        $action_text = '';
        switch ($type) {
            case 'add':
                $action_text = "Added £" . number_format($amount, 2);
                break;
            case 'subtract':
                $action_text = "Subtracted £" . number_format($amount, 2);
                break;
            case 'set':
                $action_text = "Set balance to £" . number_format($amount, 2);
                break;
        }
        echo '<div class="notice notice-success"><p>Wallet updated successfully! ' . $action_text . '. New balance: £' . number_format($new_balance, 2) . '</p></div>';
    });
}
add_action('personal_options_update', 'fc_save_wallet_adjustment');
add_action('edit_user_profile_update', 'fc_save_wallet_adjustment');

/**
 * Record wallet transaction in database
 */
function fc_record_wallet_transaction($user_id, $amount, $balance_after, $reason, $notes = '') {
    global $wpdb;
    
    $current_user = wp_get_current_user();
    $admin_user = $current_user->display_name . ' (' . $current_user->user_login . ')';
    
    // Create transactions table if it doesn't exist
    $table_name = $wpdb->prefix . 'wallet_transactions';
    
    $charset_collate = $wpdb->get_charset_collate();
    $sql = "CREATE TABLE IF NOT EXISTS $table_name (
        id int(11) NOT NULL AUTO_INCREMENT,
        user_id int(11) NOT NULL,
        amount decimal(10,2) NOT NULL,
        balance_after decimal(10,2) NOT NULL,
        reason varchar(100) NOT NULL,
        notes text,
        admin_user varchar(255),
        created_at datetime DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY user_id (user_id)
    ) $charset_collate;";
    
    require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
    dbDelta($sql);
    
    // Insert transaction record
    $wpdb->insert(
        $table_name,
        array(
            'user_id' => $user_id,
            'amount' => $amount,
            'balance_after' => $balance_after,
            'reason' => $reason,
            'notes' => $notes,
            'admin_user' => $admin_user,
            'created_at' => current_time('mysql')
        ),
        array('%d', '%f', '%f', '%s', '%s', '%s', '%s')
    );
}

/**
 * Get user wallet transactions
 */
function fc_get_user_wallet_transactions($user_id) {
    global $wpdb;
    
    $table_name = $wpdb->prefix . 'wallet_transactions';
    
    // Check if table exists
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        return array();
    }
    
    return $wpdb->get_results($wpdb->prepare(
        "SELECT * FROM $table_name WHERE user_id = %d ORDER BY created_at DESC",
        $user_id
    ));
}

/**
 * Add wallet management admin menu
 */
function fc_add_wallet_management_menu() {
    add_users_page(
        'Wallet Management',
        'Wallet Management',
        'manage_options',
        'fc-wallet-management',
        'fc_wallet_management_page'
    );
}
add_action('admin_menu', 'fc_add_wallet_management_menu');

/**
 * Wallet management admin page
 */
function fc_wallet_management_page() {
    if (!current_user_can('manage_options')) {
        wp_die(__('You do not have sufficient permissions to access this page.'));
    }

    // Handle bulk actions
    if (isset($_POST['fc_bulk_wallet_action']) && wp_verify_nonce($_POST['fc_wallet_nonce'], 'fc_wallet_bulk_action')) {
        fc_handle_bulk_wallet_action();
    }

    // Get users with wallet balances
    $users_with_wallets = fc_get_users_with_wallet_balances();
    ?>
    <div class="wrap">
        <h1>💰 Wallet Management</h1>

        <div class="notice notice-info">
            <p><strong>💡 Tip:</strong> You can also manage individual user wallets by editing their user profile in Users → All Users.</p>
        </div>

        <div class="card" style="max-width: 1200px;">
            <h2>Users with Wallet Balances</h2>

            <?php if (empty($users_with_wallets)): ?>
                <p>No users currently have wallet balances.</p>
            <?php else: ?>
                <form method="post">
                    <?php wp_nonce_field('fc_wallet_bulk_action', 'fc_wallet_nonce'); ?>

                    <div style="margin-bottom: 20px;">
                        <select name="bulk_action" style="margin-right: 10px;">
                            <option value="">Bulk Actions</option>
                            <option value="add_credit">Add Credit</option>
                            <option value="subtract_credit">Subtract Credit</option>
                            <option value="set_balance">Set Balance</option>
                            <option value="clear_balance">Clear Balance</option>
                        </select>

                        <input type="number"
                               name="bulk_amount"
                               placeholder="Amount"
                               step="0.01"
                               min="0"
                               style="width: 100px; margin-right: 10px;" />

                        <select name="bulk_reason" style="margin-right: 10px;">
                            <option value="">Select reason...</option>
                            <option value="promotional_credit">Promotional Credit</option>
                            <option value="refund">Refund</option>
                            <option value="customer_service">Customer Service</option>
                            <option value="correction">Balance Correction</option>
                            <option value="other">Other</option>
                        </select>

                        <input type="text"
                               name="bulk_notes"
                               placeholder="Notes (optional)"
                               style="width: 200px; margin-right: 10px;" />

                        <button type="submit"
                                name="fc_bulk_wallet_action"
                                class="button button-primary"
                                onclick="return confirm('Are you sure you want to apply this action to selected users?');">
                            Apply to Selected
                        </button>
                    </div>

                    <table class="widefat">
                        <thead>
                            <tr>
                                <td class="check-column">
                                    <input type="checkbox" id="select-all" />
                                </td>
                                <th>User</th>
                                <th>Email</th>
                                <th>Current Balance</th>
                                <th>Last Transaction</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($users_with_wallets as $user_data): ?>
                            <tr>
                                <th class="check-column">
                                    <input type="checkbox" name="selected_users[]" value="<?php echo $user_data->ID; ?>" />
                                </th>
                                <td>
                                    <strong><?php echo esc_html($user_data->display_name); ?></strong><br>
                                    <small>@<?php echo esc_html($user_data->user_login); ?></small>
                                </td>
                                <td><?php echo esc_html($user_data->user_email); ?></td>
                                <td>
                                    <strong style="color: #0073aa;">£<?php echo number_format($user_data->wallet_balance, 2); ?></strong>
                                </td>
                                <td>
                                    <?php
                                    $last_transaction = fc_get_user_last_transaction($user_data->ID);
                                    if ($last_transaction) {
                                        echo date('Y-m-d', strtotime($last_transaction->created_at)) . '<br>';
                                        echo '<small>' . esc_html($last_transaction->reason) . '</small>';
                                    } else {
                                        echo '<em>No transactions</em>';
                                    }
                                    ?>
                                </td>
                                <td>
                                    <a href="<?php echo admin_url('user-edit.php?user_id=' . $user_data->ID . '#wallet-management'); ?>"
                                       class="button button-small">
                                        Edit Wallet
                                    </a>
                                </td>
                            </tr>
                            <?php endforeach; ?>
                        </tbody>
                    </table>
                </form>
            <?php endif; ?>
        </div>

        <div class="card" style="max-width: 1200px; margin-top: 20px;">
            <h2>📊 Wallet Statistics</h2>
            <?php
            $stats = fc_get_wallet_statistics();
            ?>
            <table class="widefat">
                <tr>
                    <th>Total Users with Wallets</th>
                    <td><?php echo $stats['total_users']; ?></td>
                </tr>
                <tr>
                    <th>Total Wallet Value</th>
                    <td><strong>£<?php echo number_format($stats['total_value'], 2); ?></strong></td>
                </tr>
                <tr>
                    <th>Average Balance</th>
                    <td>£<?php echo number_format($stats['average_balance'], 2); ?></td>
                </tr>
                <tr>
                    <th>Highest Balance</th>
                    <td>£<?php echo number_format($stats['highest_balance'], 2); ?></td>
                </tr>
                <tr>
                    <th>Total Transactions Today</th>
                    <td><?php echo $stats['transactions_today']; ?></td>
                </tr>
            </table>
        </div>
    </div>

    <script>
    document.getElementById('select-all').addEventListener('change', function() {
        const checkboxes = document.querySelectorAll('input[name="selected_users[]"]');
        checkboxes.forEach(checkbox => {
            checkbox.checked = this.checked;
        });
    });
    </script>

    <style>
    .card {
        background: #fff;
        border: 1px solid #ccd0d4;
        border-radius: 4px;
        padding: 20px;
        margin-bottom: 20px;
    }
    .card h2 {
        margin-top: 0;
        color: #23282d;
    }
    .widefat th, .widefat td {
        padding: 12px;
        border-bottom: 1px solid #ddd;
    }
    .widefat th {
        background: #f1f1f1;
        font-weight: 600;
    }
    .check-column {
        width: 40px;
    }
    </style>
    <?php
}

/**
 * Handle bulk wallet actions
 */
function fc_handle_bulk_wallet_action() {
    if (!isset($_POST['selected_users']) || empty($_POST['selected_users'])) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Please select at least one user.</p></div>';
        });
        return;
    }

    $action = sanitize_text_field($_POST['bulk_action']);
    $amount = floatval($_POST['bulk_amount']);
    $reason = sanitize_text_field($_POST['bulk_reason']);
    $notes = sanitize_text_field($_POST['bulk_notes']);
    $selected_users = array_map('intval', $_POST['selected_users']);

    if (empty($action)) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Please select a bulk action.</p></div>';
        });
        return;
    }

    if (in_array($action, ['add_credit', 'subtract_credit', 'set_balance']) && $amount <= 0) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Please enter a valid amount.</p></div>';
        });
        return;
    }

    if (empty($reason)) {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-error"><p>Please select a reason for the adjustment.</p></div>';
        });
        return;
    }

    $success_count = 0;
    $error_count = 0;

    foreach ($selected_users as $user_id) {
        $current_balance = get_user_meta($user_id, 'wallet_balance', true);
        $current_balance = $current_balance ? floatval($current_balance) : 0.00;

        switch ($action) {
            case 'add_credit':
                $new_balance = $current_balance + $amount;
                $transaction_amount = $amount;
                break;
            case 'subtract_credit':
                $new_balance = $current_balance - $amount;
                $transaction_amount = -$amount;
                break;
            case 'set_balance':
                $new_balance = $amount;
                $transaction_amount = $amount - $current_balance;
                break;
            case 'clear_balance':
                $new_balance = 0.00;
                $transaction_amount = -$current_balance;
                break;
            default:
                continue 2;
        }

        // Ensure balance doesn't go negative
        if ($new_balance < 0) {
            $error_count++;
            continue;
        }

        // Update wallet balance - delete existing entries first to prevent duplicates
        $wpdb->delete(
            $wpdb->usermeta,
            array('user_id' => $user_id, 'meta_key' => 'wallet_balance'),
            array('%d', '%s')
        );
        add_user_meta($user_id, 'wallet_balance', $new_balance, true);
        wp_cache_delete($user_id, 'user_meta');

        // Record transaction
        fc_record_wallet_transaction($user_id, $transaction_amount, $new_balance, $reason, $notes);

        $success_count++;
    }

    add_action('admin_notices', function() use ($success_count, $error_count, $action) {
        if ($success_count > 0) {
            echo '<div class="notice notice-success"><p>Successfully updated ' . $success_count . ' user wallet(s).</p></div>';
        }
        if ($error_count > 0) {
            echo '<div class="notice notice-warning"><p>' . $error_count . ' wallet(s) could not be updated (would result in negative balance).</p></div>';
        }
    });
}

/**
 * Get users with wallet balances
 */
function fc_get_users_with_wallet_balances() {
    global $wpdb;

    return $wpdb->get_results("
        SELECT u.ID, u.user_login, u.user_email, u.display_name,
               COALESCE(um.meta_value, 0) as wallet_balance
        FROM {$wpdb->users} u
        LEFT JOIN {$wpdb->usermeta} um ON u.ID = um.user_id AND um.meta_key = 'wallet_balance'
        WHERE COALESCE(um.meta_value, 0) > 0
        ORDER BY CAST(COALESCE(um.meta_value, 0) AS DECIMAL(10,2)) DESC
    ");
}

/**
 * Get user's last transaction
 */
function fc_get_user_last_transaction($user_id) {
    global $wpdb;

    $table_name = $wpdb->prefix . 'wallet_transactions';

    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") != $table_name) {
        return null;
    }

    return $wpdb->get_row($wpdb->prepare(
        "SELECT * FROM $table_name WHERE user_id = %d ORDER BY created_at DESC LIMIT 1",
        $user_id
    ));
}

/**
 * Get wallet statistics
 */
function fc_get_wallet_statistics() {
    global $wpdb;

    $stats = array();

    // Total users with wallets
    $stats['total_users'] = $wpdb->get_var("
        SELECT COUNT(*)
        FROM {$wpdb->usermeta}
        WHERE meta_key = 'wallet_balance'
        AND CAST(meta_value AS DECIMAL(10,2)) > 0
    ");

    // Total wallet value
    $stats['total_value'] = $wpdb->get_var("
        SELECT SUM(CAST(meta_value AS DECIMAL(10,2)))
        FROM {$wpdb->usermeta}
        WHERE meta_key = 'wallet_balance'
    ");
    $stats['total_value'] = $stats['total_value'] ? floatval($stats['total_value']) : 0.00;

    // Average balance
    $stats['average_balance'] = $stats['total_users'] > 0 ? $stats['total_value'] / $stats['total_users'] : 0.00;

    // Highest balance
    $stats['highest_balance'] = $wpdb->get_var("
        SELECT MAX(CAST(meta_value AS DECIMAL(10,2)))
        FROM {$wpdb->usermeta}
        WHERE meta_key = 'wallet_balance'
    ");
    $stats['highest_balance'] = $stats['highest_balance'] ? floatval($stats['highest_balance']) : 0.00;

    // Transactions today
    $table_name = $wpdb->prefix . 'wallet_transactions';
    if ($wpdb->get_var("SHOW TABLES LIKE '$table_name'") == $table_name) {
        $stats['transactions_today'] = $wpdb->get_var("
            SELECT COUNT(*)
            FROM $table_name
            WHERE DATE(created_at) = CURDATE()
        ");
    } else {
        $stats['transactions_today'] = 0;
    }

    return $stats;
}

/**
 * Add wallet balance column to users list table
 */
function fc_add_wallet_column_to_users_table($columns) {
    $columns['wallet_balance'] = 'Wallet Balance';
    return $columns;
}
add_filter('manage_users_columns', 'fc_add_wallet_column_to_users_table');

/**
 * Display wallet balance in users list table
 */
function fc_display_wallet_column_content($value, $column_name, $user_id) {
    if ($column_name == 'wallet_balance') {
        $balance = get_user_meta($user_id, 'wallet_balance', true);
        $balance = $balance ? floatval($balance) : 0.00;

        if ($balance > 0) {
            return '<strong style="color: #0073aa;">£' . number_format($balance, 2) . '</strong>';
        } else {
            return '<span style="color: #666;">£0.00</span>';
        }
    }
    return $value;
}
add_filter('manage_users_custom_column', 'fc_display_wallet_column_content', 10, 3);

/**
 * Make wallet balance column sortable
 */
function fc_make_wallet_column_sortable($columns) {
    $columns['wallet_balance'] = 'wallet_balance';
    return $columns;
}
add_filter('manage_users_sortable_columns', 'fc_make_wallet_column_sortable');

/**
 * Handle wallet balance column sorting
 */
function fc_handle_wallet_column_sorting($query) {
    if (!is_admin()) {
        return;
    }

    $orderby = $query->get('orderby');
    if ($orderby == 'wallet_balance') {
        $query->set('meta_key', 'wallet_balance');
        $query->set('orderby', 'meta_value_num');
    }
}
add_action('pre_get_users', 'fc_handle_wallet_column_sorting');

/**
 * Add quick wallet actions to user row actions
 */
function fc_add_wallet_user_row_actions($actions, $user_object) {
    if (current_user_can('manage_options')) {
        $balance = get_user_meta($user_object->ID, 'wallet_balance', true);
        $balance = $balance ? floatval($balance) : 0.00;

        $actions['wallet'] = '<a href="' . admin_url('user-edit.php?user_id=' . $user_object->ID . '#wallet-management') . '">Wallet (£' . number_format($balance, 2) . ')</a>';
    }
    return $actions;
}
add_filter('user_row_actions', 'fc_add_wallet_user_row_actions', 10, 2);
