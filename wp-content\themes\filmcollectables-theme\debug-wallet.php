<?php
/**
 * Temporary debug page for wallet balance
 * Access via: /wp-content/themes/filmcollectables-theme/debug-wallet.php
 */

// Load WordPress
require_once('../../../wp-load.php');

// Check if user is logged in and is admin
if (!is_user_logged_in() || !current_user_can('administrator')) {
    die('Access denied. Please log in as administrator.');
}

$user_id = get_current_user_id();

echo "<h1>Wallet Balance Debug for User ID: $user_id</h1>";

// Get balance using different methods
echo "<h2>Method 1: WordPress get_user_meta()</h2>";
$wp_balance = get_user_meta($user_id, 'wallet_balance', true);
echo "Balance: £" . number_format(floatval($wp_balance), 2) . "<br>";

echo "<h2>Method 2: Our get_user_wallet_balance() function</h2>";
$our_balance = get_user_wallet_balance($user_id);
echo "Balance: £" . number_format($our_balance, 2) . "<br>";

echo "<h2>Method 3: Direct database query</h2>";
global $wpdb;
$all_balances = $wpdb->get_results($wpdb->prepare(
    "SELECT umeta_id, meta_value, meta_key FROM {$wpdb->usermeta} 
     WHERE user_id = %d AND meta_key = 'wallet_balance' 
     ORDER BY umeta_id DESC",
    $user_id
));

echo "Number of wallet_balance entries: " . count($all_balances) . "<br>";
foreach ($all_balances as $entry) {
    echo "Entry ID: {$entry->umeta_id}, Value: £" . number_format(floatval($entry->meta_value), 2) . "<br>";
}

echo "<h2>Method 4: All user meta for wallet</h2>";
$all_meta = get_user_meta($user_id);
if (isset($all_meta['wallet_balance'])) {
    echo "Wallet balance meta entries: " . count($all_meta['wallet_balance']) . "<br>";
    foreach ($all_meta['wallet_balance'] as $index => $value) {
        echo "Index $index: £" . number_format(floatval($value), 2) . "<br>";
    }
} else {
    echo "No wallet_balance meta found<br>";
}

echo "<h2>Cleanup Test</h2>";
echo "<a href='?cleanup=1'>Click here to cleanup duplicate entries</a><br>";

if (isset($_GET['cleanup'])) {
    $cleaned = cleanup_duplicate_wallet_entries($user_id);
    if ($cleaned !== false) {
        echo "Cleaned up duplicates. Final balance: £" . number_format(floatval($cleaned), 2) . "<br>";
    } else {
        echo "No duplicates found to clean up.<br>";
    }
    echo "<a href='?'>Refresh page</a>";
}
?>
