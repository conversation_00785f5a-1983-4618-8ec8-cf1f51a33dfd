(()=>{"use strict";var t={n:e=>{var o=e&&e.__esModule?()=>e.default:()=>e;return t.d(o,{a:o}),o},d:(e,o)=>{for(var n in o)t.o(o,n)&&!t.o(e,n)&&Object.defineProperty(e,n,{enumerable:!0,get:o[n]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const e=window.wp.i18n,o=window.wp.domReady;var n=t.n(o);const r=window.wc.wcSettings;n()((()=>{const t=document.querySelector(".wc-actions");if(t){const o=document.querySelector(".wc-actions .button-primary");o&&(o.classList.remove("button"),o.classList.remove("button-primary"));const n=document.createElement("a");n.classList.add("button"),n.classList.add("button-primary"),n.setAttribute("href",(0,r.getAdminLink)("admin.php?page=wc-admin")),n.innerText=(0,e.__)("Continue setup","woocommerce"),t.appendChild(n)}})),(window.wc=window.wc||{}).onboardingProductImportNotice={}})();