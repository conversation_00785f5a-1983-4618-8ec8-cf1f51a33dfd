"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[3024],{5299:(e,t,c)=>{c.d(t,{A:()=>o});var r=c(7723);const o=({defaultTitle:e=(0,r.__)("Step","woocommerce"),defaultDescription:t=(0,r.__)("Step description text.","woocommerce"),defaultShowStepNumber:c=!0})=>({title:{type:"string",default:e},description:{type:"string",default:t},showStepNumber:{type:"boolean",default:c}})},184:(e,t,c)=>{c.r(t),c.d(t,{default:()=>F});var r=c(4921),o=c(1616),n=c(4656),a=c(7143),s=c(7594),i=c(8331),l=c(4199),p=c(7723),d=c(6087),u=c(6473),m=c(5460),k=c(910),g=c(8537),h=c(5703),f=c(4530),_=c(9835),x=c(3932),b=c(1e3),C=c(4923),y=c(9194),w=c(4007),S=c(790);const v=(e,t)=>{const c=(0,h.getSetting)("displayCartPricesIncludingTax",!1)?parseInt(e.price,10)+parseInt(e.taxes,10):parseInt(e.price,10),r=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_location"===e.key));return t?t.value:""}return""})(e),o=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_address"===e.key));return t?t.value:""}return""})(e),a=(e=>{if(e?.meta_data){const t=e.meta_data.find((e=>"pickup_details"===e.key));return t?t.value:""}return""})(e),s=e?.selected;let i=(0,S.jsx)("em",{children:(0,p.__)("free","woocommerce")});return c>0&&(i=1===t?(0,S.jsx)(n.FormattedMonetaryAmount,{currency:(0,k.getCurrencyFromPriceResponse)(e),value:c}):(0,d.createInterpolateElement)(/* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */ /* translators: <price/> is the price of the package, <packageCount/> is the number of packages. These must appear in the translated string. */
(0,p._n)("<price/> x <packageCount/> package","<price/> x <packageCount/> packages",t,"woocommerce"),{price:(0,S.jsx)(n.FormattedMonetaryAmount,{currency:(0,k.getCurrencyFromPriceResponse)(e),value:c}),packageCount:(0,S.jsx)(S.Fragment,{children:t})})),{value:e.rate_id,label:r?(0,g.decodeEntities)(r):(0,g.decodeEntities)(e.name),secondaryLabel:i,description:o?(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(f.A,{icon:_.A,className:"wc-block-editor-components-block-icon"}),(0,g.decodeEntities)(o)]}):void 0,secondaryDescription:s&&a?(0,S.jsx)(y.A,{maxLines:2,children:(0,g.decodeEntities)(a)}):void 0}},P=()=>{const{shippingRates:e,selectShippingRate:t}=(0,u.m)(),c=(0,d.useMemo)((()=>(e[0]?.shipping_rates||[]).filter(x.J_)),[e]),[r,o]=(0,d.useState)((()=>{var e;return null!==(e=c.find((e=>e.selected))?.rate_id)&&void 0!==e?e:c[0]?.rate_id})),n=(0,d.useCallback)((e=>{o(e),t(e)}),[o,t]);(0,d.useEffect)((()=>{r&&t(r)}),[]),(0,d.useEffect)((()=>{const e=c.find((e=>e.selected)),t=e?.rate_id;t&&t!==r&&o(t)}),[c]);const{extensions:a,receiveCart:s,...i}=(0,m.V)(),l={extensions:a,cart:i,components:{ShippingRatesControlPackage:w.A,LocalPickupSelect:C.G},renderPickupLocation:v};return(0,S.jsxs)(S.Fragment,{children:[(0,S.jsx)(b.ExperimentalOrderLocalPickupPackages.Slot,{...l}),(0,S.jsx)(b.ExperimentalOrderLocalPickupPackages,{children:(0,S.jsx)(C.G,{title:e[0].name,selectedOption:null!=r?r:"",renderPickupLocation:v,pickupLocations:c,packageCount:(0,x.T4)(e),onChange:e=>n(e)})})]})},j={...(0,c(5299).A)({defaultTitle:(0,p.__)("Pickup locations","woocommerce"),defaultDescription:""}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}},F=(0,o.withFilteredAttributes)(j)((({title:e,description:t,children:c,className:o})=>{const{checkoutIsProcessing:p,prefersCollection:d}=(0,a.useSelect)((e=>{const t=e(s.checkoutStore);return{checkoutIsProcessing:t.isProcessing(),prefersCollection:t.prefersCollection()}})),{showFormStepNumbers:u}=(0,l.O)();return d&&i.F7?(0,S.jsxs)(n.FormStep,{id:"pickup-options",disabled:p,className:(0,r.A)("wc-block-checkout__pickup-options",o),title:e,description:t,showStepNumber:u,children:[(0,S.jsx)(P,{}),c]}):null}))}}]);