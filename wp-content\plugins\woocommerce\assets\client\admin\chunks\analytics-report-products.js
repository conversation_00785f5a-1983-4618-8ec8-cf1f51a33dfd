"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[5845],{74767:(e,t,o)=>{o.d(t,{A:()=>m});var r=o(86087),s=o(66087),a=o(56427),i=o(18537),c=o(98846),n=o(96476),l=o(39793);class m extends r.Component{getCategoryAncestorIds(e,t){const o=[];let r=e.parent;for(;r;)o.unshift(r),r=t.get(r).parent;return o}getCategoryAncestors(e,t){const o=this.getCategoryAncestorIds(e,t);if(o.length)return 1===o.length?t.get((0,s.first)(o)).name+" › ":2===o.length?t.get((0,s.first)(o)).name+" › "+t.get((0,s.last)(o)).name+" › ":t.get((0,s.first)(o)).name+" … "+t.get((0,s.last)(o)).name+" › "}render(){const{categories:e,category:t,query:o}=this.props,r=(0,n.getPersistedQuery)(o);return t?(0,l.jsxs)("div",{className:"woocommerce-table__breadcrumbs",children:[(0,i.decodeEntities)(this.getCategoryAncestors(t,e)),(0,l.jsx)(c.Link,{href:(0,n.getNewPath)(r,"/analytics/categories",{filter:"single_category",categories:t.id}),type:"wc-admin",children:(0,i.decodeEntities)(t.name)})]}):(0,l.jsx)(a.Spinner,{})}}},95519:(e,t,o)=>{o.d(t,{Qc:()=>u,eg:()=>l,uW:()=>p});var r=o(27723),s=o(52619),a=o(47143),i=o(27752),c=o(33958);const{addCesSurveyForAnalytics:n}=(0,a.dispatch)(i.STORE_KEY),l=(0,s.applyFilters)("woocommerce_admin_products_report_charts",[{key:"items_sold",label:(0,r.__)("Items sold","woocommerce"),order:"desc",orderby:"items_sold",type:"number"},{key:"net_revenue",label:(0,r.__)("Net sales","woocommerce"),order:"desc",orderby:"net_revenue",type:"currency"},{key:"orders_count",label:(0,r.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),m={label:(0,r.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,r.__)("All products","woocommerce"),value:"all"},{label:(0,r.__)("Single product","woocommerce"),value:"select_product",chartMode:"item-comparison",subFilters:[{component:"Search",value:"single_product",chartMode:"item-comparison",path:["select_product"],settings:{type:"products",param:"products",getLabels:c.p0,labels:{placeholder:(0,r.__)("Type to search for a product","woocommerce"),button:(0,r.__)("Single product","woocommerce")}}}]},{label:(0,r.__)("Comparison","woocommerce"),value:"compare-products",chartMode:"item-comparison",settings:{type:"products",param:"products",getLabels:c.p0,labels:{helpText:(0,r.__)("Check at least two products below to compare","woocommerce"),placeholder:(0,r.__)("Search for products to compare","woocommerce"),title:(0,r.__)("Compare Products","woocommerce"),update:(0,r.__)("Compare","woocommerce")},onClick:n}}]},d={showFilters:e=>"single_product"===e.filter&&!!e.products&&e["is-variable"],staticParams:["filter","products","chartType","paged","per_page"],param:"filter-variations",filters:[{label:(0,r.__)("All variations","woocommerce"),chartMode:"item-comparison",value:"all"},{label:(0,r.__)("Single variation","woocommerce"),value:"select_variation",subFilters:[{component:"Search",value:"single_variation",path:["select_variation"],settings:{type:"variations",param:"variations",getLabels:c.b8,labels:{placeholder:(0,r.__)("Type to search for a variation","woocommerce"),button:(0,r.__)("Single variation","woocommerce")}}}]},{label:(0,r.__)("Comparison","woocommerce"),chartMode:"item-comparison",value:"compare-variations",settings:{type:"variations",param:"variations",getLabels:c.b8,labels:{helpText:(0,r.__)("Check at least two variations below to compare","woocommerce"),placeholder:(0,r.__)("Search for variations to compare","woocommerce"),title:(0,r.__)("Compare Variations","woocommerce"),update:(0,r.__)("Compare","woocommerce")}}}]},u=(0,s.applyFilters)("woocommerce_admin_products_report_advanced_filters",{filters:{},title:(0,r._x)("Products Match <select/> Filters","A sentence describing filters for Products. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce")});Object.keys(u.filters).length&&(m.filters.push({label:(0,r.__)("Advanced Filters","woocommerce"),value:"advanced"}),d.filters.push({label:(0,r.__)("Advanced Filters","woocommerce"),value:"advanced"}));const p=(0,s.applyFilters)("woocommerce_admin_products_report_filters",[m,d])},60781:(e,t,o)=>{o.r(t),o.d(t,{default:()=>b});var r=o(27723),s=o(86087),a=o(29491),i=o(40314),c=o(98846),n=o(47143),l=o(95519),m=o(95272),d=o(94413),u=o(55737),p=o(68224),_=o(86771),g=o(88711),y=o(39793);class h extends s.Component{getChartMeta(){const{query:e,isSingleProductView:t,isSingleProductVariable:o}=this.props,s="compare-products"===e.filter&&e.products&&e.products.split(",").length>1||t&&o?"item-comparison":"time-comparison";return{compareObject:t&&o?"variations":"products",itemsLabel:t&&o?(0,r.__)("%d variations","woocommerce"):(0,r.__)("%d products","woocommerce"),mode:s}}render(){const{compareObject:e,itemsLabel:t,mode:o}=this.getChartMeta(),{path:r,query:a,isError:i,isRequesting:n,isSingleProductVariable:h}=this.props;if(i)return(0,y.jsx)(c.AnalyticsError,{});const b={...a};return"item-comparison"===o&&(b.segmentby="products"===e?"product":"variation"),(0,y.jsxs)(s.Fragment,{children:[(0,y.jsx)(g.A,{query:a,path:r,filters:l.uW,advancedFilters:l.Qc,report:"products"}),(0,y.jsx)(p.A,{mode:o,charts:l.eg,endpoint:"products",query:b,selectedChart:(0,m.A)(a.chart,l.eg),filters:l.uW,advancedFilters:l.Qc}),(0,y.jsx)(u.A,{charts:l.eg,mode:o,filters:l.uW,advancedFilters:l.Qc,endpoint:"products",isRequesting:n,itemsLabel:t,path:r,query:b,selectedChart:(0,m.A)(b.chart,l.eg)}),h?(0,y.jsx)(_.A,{baseSearchQuery:{filter:"single_product"},isRequesting:n,query:a,filters:l.uW,advancedFilters:l.Qc}):(0,y.jsx)(d.A,{isRequesting:n,query:a,filters:l.uW,advancedFilters:l.Qc})]})}}const b=(0,a.compose)((0,n.withSelect)(((e,t)=>{const{query:o,isRequesting:r}=t,s=!o.search&&o.products&&1===o.products.split(",").length;if(r)return{query:{...o},isSingleProductView:s,isRequesting:r};if(s){const{getItems:t,isResolving:r,getItemsError:a}=e(i.itemsStore),c=parseInt(o.products,10),n={include:c},l=t("products",n),m=l&&l.get(c)&&"variable"===l.get(c).type,d=r("getItems",["products",n]),u=Boolean(a("products",n));return{query:{...o,"is-variable":m},isSingleProductView:s,isRequesting:d,isSingleProductVariable:m,isError:u}}return{query:o,isSingleProductView:s}})))(h)},94413:(e,t,o)=>{o.d(t,{A:()=>k});var r=o(27723),s=o(86087),a=o(29491),i=o(18537),c=o(47143),n=o(66087),l=o(96476),m=o(98846),d=o(43577),u=o(15703),p=o(40314),_=o(94111),g=o(74767),y=o(43128),h=o(97605),b=o(56109),w=o(39793);const v=(0,b.Qk)("manageStock","no"),f=(0,b.Qk)("stockStatuses",{});class S extends s.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,r.__)("Product title","woocommerce"),key:"product_name",required:!0,isLeftAligned:!0,isSortable:!0},{label:(0,r.__)("SKU","woocommerce"),key:"sku",hiddenByDefault:!0,isSortable:!0},{label:(0,r.__)("Items sold","woocommerce"),key:"items_sold",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,r.__)("Net sales","woocommerce"),screenReaderLabel:(0,r.__)("Net sales","woocommerce"),key:"net_revenue",required:!0,isSortable:!0,isNumeric:!0},{label:(0,r.__)("Orders","woocommerce"),key:"orders_count",isSortable:!0,isNumeric:!0},{label:(0,r.__)("Category","woocommerce"),key:"product_cat"},{label:(0,r.__)("Variations","woocommerce"),key:"variations",isSortable:!0},"yes"===v?{label:(0,r.__)("Status","woocommerce"),key:"stock_status"}:null,"yes"===v?{label:(0,r.__)("Stock","woocommerce"),key:"stock",isNumeric:!0}:null].filter(Boolean)}getRowsContent(e=[]){const{query:t}=this.props,o=(0,l.getPersistedQuery)(t),{render:s,formatDecimal:a,getCurrencyConfig:c}=this.context,p=c();return(0,n.map)(e,(e=>{const{product_id:c,items_sold:n,net_revenue:_,orders_count:h}=e,b=e.extended_info||{},{category_ids:S,low_stock_amount:k,manage_stock:C,sku:x,stock_status:q,stock_quantity:A,variations:P=[]}=b,R=(0,i.decodeEntities)(b.name),F=(0,l.getNewPath)(o,"/analytics/orders",{filter:"advanced",product_includes:c}),j=(0,l.getNewPath)(o,"/analytics/products",{filter:"single_product",products:c}),{categories:V}=this.props,L=S&&V&&S.map((e=>V.get(e))).filter(Boolean)||[],N=(0,y.n)(q,A,k)?(0,w.jsx)(m.Link,{href:(0,u.getAdminLink)("post.php?action=edit&post="+c),type:"wp-admin",children:(0,r._x)("Low","Indication of a low quantity","woocommerce")}):f[q];return[{display:(0,w.jsx)(m.Link,{href:j,type:"wc-admin",children:R}),value:R},{display:x,value:x},{display:(0,d.formatValue)(p,"number",n),value:n},{display:s(_),value:a(_)},{display:(0,w.jsx)(m.Link,{href:F,type:"wc-admin",children:h}),value:h},{display:(0,w.jsxs)("div",{className:"woocommerce-table__product-categories",children:[L[0]&&(0,w.jsx)(g.A,{category:L[0],categories:V}),L.length>1&&(0,w.jsx)(m.Tag,{label:(0,r.sprintf)((0,r._x)("+%d more","categories","woocommerce"),L.length-1),popoverContents:L.map((e=>(0,w.jsx)(g.A,{category:e,categories:V,query:t},e.id)))})]}),value:L.map((e=>e.name)).join(", ")},{display:(0,d.formatValue)(p,"number",P.length),value:P.length},"yes"===v?{display:C?N:(0,r.__)("N/A","woocommerce"),value:C?f[q]:null}:null,"yes"===v?{display:C?(0,d.formatValue)(p,"number",A):(0,r.__)("N/A","woocommerce"),value:A}:null].filter(Boolean)}))}getSummary(e){const{products_count:t=0,items_sold:o=0,net_revenue:s=0,orders_count:a=0}=e,{formatAmount:i,getCurrencyConfig:c}=this.context,n=c();return[{label:(0,r._n)("Product","Products",t,"woocommerce"),value:(0,d.formatValue)(n,"number",t)},{label:(0,r._n)("Item sold","Items sold",o,"woocommerce"),value:(0,d.formatValue)(n,"number",o)},{label:(0,r.__)("Net sales","woocommerce"),value:i(s)},{label:(0,r._n)("Order","Orders",a,"woocommerce"),value:(0,d.formatValue)(n,"number",a)}]}render(){const{advancedFilters:e,baseSearchQuery:t,filters:o,hideCompare:s,isRequesting:a,query:i}=this.props,c={helpText:(0,r.__)("Check at least two products below to compare","woocommerce"),placeholder:(0,r.__)("Search by product name or SKU","woocommerce")};return(0,w.jsx)(h.A,{compareBy:s?void 0:"products",endpoint:"products",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["products_count","items_sold","net_revenue","orders_count"],itemIdField:"product_id",isRequesting:a,labels:c,query:i,searchBy:"products",baseSearchQuery:t,tableQuery:{orderby:i.orderby||"items_sold",order:i.order||"desc",extended_info:!0,segmentby:i.segmentby},title:(0,r.__)("Products","woocommerce"),columnPrefsKey:"products_report_columns",filters:o,advancedFilters:e})}}S.contextType=_.CurrencyContext;const k=(0,a.compose)((0,c.withSelect)(((e,t)=>{const{query:o,isRequesting:r}=t;if(r||o.search&&(!o.products||!o.products.length))return{};const{getItems:s,getItemsError:a,isResolving:i}=e(p.itemsStore),c={per_page:-1};return{categories:s("categories",c),isError:Boolean(a("categories",c)),isRequesting:i("getItems",["categories",c])}})))(S)},86771:(e,t,o)=>{o.d(t,{A:()=>v});var r=o(27723),s=o(52619),a=o(86087),i=o(66087),c=o(98846),n=o(96476),l=o(43577),m=o(15703),d=o(94111),u=o(97605),p=o(43128),_=o(33958),g=o(56109),y=o(39793);const h=(0,g.Qk)("manageStock","no"),b=(0,g.Qk)("stockStatuses",{});class w extends a.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,r.__)("Product / Variation title","woocommerce"),key:"name",required:!0,isLeftAligned:!0},{label:(0,r.__)("SKU","woocommerce"),key:"sku",hiddenByDefault:!0,isSortable:!0},{label:(0,r.__)("Items sold","woocommerce"),key:"items_sold",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,r.__)("Net sales","woocommerce"),screenReaderLabel:(0,r.__)("Net sales","woocommerce"),key:"net_revenue",required:!0,isSortable:!0,isNumeric:!0},{label:(0,r.__)("Orders","woocommerce"),key:"orders_count",isSortable:!0,isNumeric:!0},"yes"===h?{label:(0,r.__)("Status","woocommerce"),key:"stock_status"}:null,"yes"===h?{label:(0,r.__)("Stock","woocommerce"),key:"stock",isNumeric:!0}:null].filter(Boolean)}getRowsContent(e=[]){const{query:t}=this.props,o=(0,n.getPersistedQuery)(t),{formatAmount:s,formatDecimal:a,getCurrencyConfig:d}=this.context;return(0,i.map)(e,(e=>{const{items_sold:t,net_revenue:i,orders_count:u,product_id:g,variation_id:w}=e,v=e.extended_info||{},{stock_status:f,stock_quantity:S,low_stock_amount:k,deleted:C,sku:x}=v,q=(A=e,(0,_.xP)(A.extended_info||{}));var A;const P=(0,n.getNewPath)(o,"/analytics/orders",{filter:"advanced",variation_includes:w}),R=(0,m.getAdminLink)(`post.php?post=${g}&action=edit`);return[{display:C?q+" "+(0,r.__)("(Deleted)","woocommerce"):(0,y.jsx)(c.Link,{href:R,type:"wp-admin",children:q}),value:q},{display:x,value:x},{display:(0,l.formatValue)(d(),"number",t),value:t},{display:s(i),value:a(i)},{display:(0,y.jsx)(c.Link,{href:P,type:"wc-admin",children:u}),value:u},"yes"===h?{display:(0,p.n)(f,S,k)?(0,y.jsx)(c.Link,{href:R,type:"wp-admin",children:(0,r._x)("Low","Indication of a low quantity","woocommerce")}):b[f],value:b[f]}:null,"yes"===h?{display:S,value:S}:null].filter(Boolean)}))}getSummary(e){const{query:t}=this.props,{variations_count:o=0,items_sold:a=0,net_revenue:i=0,orders_count:c=0}=e,{formatAmount:n,getCurrencyConfig:m}=this.context,d=m();return[{label:(0,s.applyFilters)("experimental_woocommerce_admin_variations_report_table_summary_variations_count_label",(0,r._n)("variation sold","variations sold",o,"woocommerce"),o,t),value:(0,l.formatValue)(d,"number",o)},{label:(0,r._n)("item sold","items sold",a,"woocommerce"),value:(0,l.formatValue)(d,"number",a)},{label:(0,r.__)("net sales","woocommerce"),value:n(i)},{label:(0,r._n)("orders","orders",c,"woocommerce"),value:(0,l.formatValue)(d,"number",c)}]}render(){const{advancedFilters:e,baseSearchQuery:t,filters:o,isRequesting:a,query:i}=this.props,c={helpText:(0,r.__)("Check at least two variations below to compare","woocommerce"),placeholder:(0,r.__)("Search by variation name or SKU","woocommerce")};return(0,y.jsx)(u.A,{baseSearchQuery:t,compareBy:"variations",compareParam:"filter-variations",endpoint:"variations",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,isRequesting:a,itemIdField:"variation_id",labels:c,query:i,getSummary:this.getSummary,summaryFields:["variations_count","items_sold","net_revenue","orders_count"],tableQuery:{orderby:i.orderby||"items_sold",order:i.order||"desc",extended_info:!0,product_includes:i.product_includes,variations:i.variations},title:(0,s.applyFilters)("experimental_woocommerce_admin_variations_report_table_title",(0,r.__)("Variations","woocommerce"),i),columnPrefsKey:"variations_report_columns",filters:o,advancedFilters:e})}}w.contextType=d.CurrencyContext;const v=w}}]);