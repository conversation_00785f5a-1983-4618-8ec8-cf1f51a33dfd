"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[4037],{94804:(e,s,i)=>{i.r(s),i.d(s,{default:()=>b});var l=i(4921),c=i(41616),t=i(14656),n=i(87792),o=i(47143),r=i(47594),d=i(42425),h=i(47015),p=i(94199),u=i(37853),a=i(10790);const b=(0,c.withFilteredAttributes)(h.A)((({title:e,description:s,children:i,className:c})=>{const{showFormStepNumbers:h}=(0,p.O)(),b=(0,o.useSelect)((e=>e(r.checkoutStore).isProcessing())),{showBillingFields:g,forcedBillingAddress:k,useBillingAsShipping:w}=(0,n.C)();return g||w?(e=(0,u.y)(e,k),s=(0,u.q)(s,k),(0,a.jsxs)(t.FormStep,{id:"billing-fields",disabled:b,className:(0,l.A)("wc-block-checkout__billing-fields",c),title:e,description:s,showStepNumber:h,children:[(0,a.jsx)(d.A,{}),i]})):null}))}}]);