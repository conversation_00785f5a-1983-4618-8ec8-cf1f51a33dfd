"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[6620],{59255:(e,t,s)=>{s.d(t,{W:()=>i});var o=s(56427),n=s(39793);const i=({size:e="medium"})=>(0,n.jsx)("div",{className:`woocommerce-field-placeholder woocommerce-field-placeholder--${e}`,children:(0,n.jsx)(o.Placeholder,{})})},32905:(e,t,s)=>{s.d(t,{w:()=>c});var o=s(56427),n=s(51609),i=s(39793);const c=({children:e})=>(0,i.jsx)(i.Fragment,{children:e});c.Layout=({children:e})=>((0,n.useEffect)((()=>{const e=document.getElementById("wpbody");e&&e.querySelector(".settings-layout")&&e.classList.add("has-settings-layout")}),[]),(0,i.jsx)("div",{className:"settings-layout",children:e})),c.Section=({title:e,description:t,children:s,id:o})=>(0,i.jsxs)("div",{className:"settings-section",id:o,children:[(0,i.jsxs)("div",{className:"settings-section__details",children:[(0,i.jsx)("h2",{children:e}),(0,i.jsx)("p",{children:t})]}),(0,i.jsx)("div",{className:"settings-section__controls",children:s})]}),c.Actions=({children:e})=>(0,i.jsx)(o.Card,{className:"settings-card__wrapper ",children:(0,i.jsx)(o.CardBody,{className:"form__actions",children:e})}),c.Form=({children:e,onSubmit:t})=>(0,i.jsx)("form",{onSubmit:t,className:"settings-form",children:e})},73057:(e,t,s)=>{s.r(t),s.d(t,{SettingsPaymentsCheque:()=>m,default:()=>h});var o=s(56427),n=s(27723),i=s(47143),c=s(40314),a=s(86087),r=s(32905),l=s(59255),d=s(39793);const m=()=>{const{createSuccessNotice:e,createErrorNotice:t}=(0,i.useDispatch)("core/notices"),{chequeSettings:s,isLoading:m}=(0,i.useSelect)((e=>({chequeSettings:e(c.paymentGatewaysStore).getPaymentGateway("cheque"),isLoading:!e(c.paymentGatewaysStore).hasFinishedResolution("getPaymentGateway",["cheque"])})),[]),{updatePaymentGateway:h,invalidateResolutionForStoreSelector:u}=(0,i.useDispatch)(c.paymentGatewaysStore),[p,g]=(0,a.useState)({}),[w,_]=(0,a.useState)(!1),[y,x]=(0,a.useState)(!1);return(0,a.useEffect)((()=>{s&&g({enabled:s.enabled,title:s.settings.title.value,description:s.description,instructions:s.settings.instructions.value})}),[s]),(0,d.jsx)(r.w,{children:(0,d.jsx)(r.w.Layout,{children:(0,d.jsxs)(r.w.Form,{onSubmit:o=>{o.preventDefault(),(()=>{if(!s)return;_(!0);const o={title:String(p.title),instructions:String(p.instructions)};h("cheque",{enabled:Boolean(p.enabled),description:String(p.description),settings:o}).then((()=>{u("getPaymentGateway"),e((0,n.__)("Settings updated successfully","woocommerce")),_(!1),x(!1)})).catch((()=>{t((0,n.__)("Failed to update settings","woocommerce")),_(!1)}))})()},children:[(0,d.jsxs)(r.w.Section,{title:(0,n.__)("Enable and customise","woocommerce"),description:(0,n.__)("Choose how you want to present check payments to your customers during checkout.","woocommerce"),children:[m?(0,d.jsx)(l.W,{size:"small"}):(0,d.jsx)(o.CheckboxControl,{label:(0,n.__)("Enable check payments","woocommerce"),checked:Boolean(p.enabled),onChange:e=>{g({...p,enabled:e}),x(!0)}}),m?(0,d.jsx)(l.W,{size:"medium"}):(0,d.jsx)(o.TextControl,{label:(0,n.__)("Title","woocommerce"),help:(0,n.__)("Payment method name that the customer will see during checkout.","woocommerce"),placeholder:(0,n.__)("Check payments","woocommerce"),value:String(p.title),onChange:e=>{g({...p,title:e}),x(!0)}}),m?(0,d.jsx)(l.W,{size:"large"}):(0,d.jsx)(o.TextareaControl,{label:(0,n.__)("Description","woocommerce"),help:(0,n.__)("Payment method description that the customer will see during checkout.","woocommerce"),value:String(p.description),onChange:e=>{g({...p,description:e}),x(!0)}}),m?(0,d.jsx)(l.W,{size:"large"}):(0,d.jsx)(o.TextareaControl,{label:(0,n.__)("Instructions","woocommerce"),help:(0,n.__)("Instructions that will be added to the thank you page and emails.","woocommerce"),value:String(p.instructions),onChange:e=>{g({...p,instructions:e}),x(!0)}})]}),(0,d.jsx)(r.w.Actions,{children:(0,d.jsx)(o.Button,{variant:"primary",type:"submit",isBusy:w,disabled:w||!y,children:(0,n.__)("Save changes","woocommerce")})})]})})})},h=m}}]);