/*! For license information please see print-shipping-label-banner.js.LICENSE.txt */
(()=>{"use strict";var e={94931:(e,t,n)=>{var s=n(51609),i=Symbol.for("react.element"),o=(Symbol.for("react.fragment"),Object.prototype.hasOwnProperty),r=s.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,c={key:!0,ref:!0,__self:!0,__source:!0};function a(e,t,n){var s,a={},l=null,p=null;for(s in void 0!==n&&(l=""+n),void 0!==t.key&&(l=""+t.key),void 0!==t.ref&&(p=t.ref),t)o.call(t,s)&&!c.hasOwnProperty(s)&&(a[s]=t[s]);if(e&&e.defaultProps)for(s in t=e.defaultProps)void 0===a[s]&&(a[s]=t[s]);return{$$typeof:i,type:e,key:l,ref:p,props:a,_owner:r.current}}t.jsx=a,t.jsxs=a},39793:(e,t,n)=>{e.exports=n(94931)},51609:e=>{e.exports=window.React}},t={};function n(s){var i=t[s];if(void 0!==i)return i.exports;var o=t[s]={exports:{}};return e[s](o,o.exports,n),o.exports}n.n=e=>{var t=e&&e.__esModule?()=>e.default:()=>e;return n.d(t,{a:t}),t},n.d=(e,t)=>{for(var s in t)n.o(t,s)&&!n.o(e,s)&&Object.defineProperty(e,s,{enumerable:!0,get:t[s]})},n.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const s=window.wp.element,i=window.wc.data,o=window.wp.i18n,r=window.wp.components,c=window.wp.compose;var a=n(51609);function l(e){return e.startsWith("{{/")?{type:"componentClose",value:e.replace(/\W/g,"")}:e.endsWith("/}}")?{type:"componentSelfClosing",value:e.replace(/\W/g,"")}:e.startsWith("{{")?{type:"componentOpen",value:e.replace(/\W/g,"")}:{type:"string",value:e}}function p(e,t){let n,s,i=[];for(let o=0;o<e.length;o++){const r=e[o];if("string"!==r.type){if(void 0===t[r.value])throw new Error(`Invalid interpolation, missing component node: \`${r.value}\``);if("object"!=typeof t[r.value])throw new Error(`Invalid interpolation, component node must be a ReactElement or null: \`${r.value}\``);if("componentClose"===r.type)throw new Error(`Missing opening component token: \`${r.value}\``);if("componentOpen"===r.type){n=t[r.value],s=o;break}i.push(t[r.value])}else i.push(r.value)}if(n){const o=function(e,t){const n=t[e];let s=0;for(let i=e+1;i<t.length;i++){const e=t[i];if(e.value===n.value){if("componentOpen"===e.type){s++;continue}if("componentClose"===e.type){if(0===s)return i;s--}}}throw new Error("Missing closing component token `"+n.value+"`")}(s,e),r=p(e.slice(s+1,o),t),c=(0,a.cloneElement)(n,{},r);if(i.push(c),o<e.length-1){const n=p(e.slice(o+1),t);i=i.concat(n)}}return i=i.filter(Boolean),0===i.length?null:1===i.length?i[0]:(0,a.createElement)(a.Fragment,null,...i)}function d(e){const{mixedString:t,components:n,throwErrors:s}=e;if(!n)return t;if("object"!=typeof n){if(s)throw new Error(`Interpolation Error: unable to process \`${t}\` because components is not an object`);return t}const i=function(e){return e.split(/(\{\{\/?\s*\w+\s*\/?\}\})/g).map(l)}(t);try{return p(i,n)}catch(e){if(s)throw new Error(`Interpolation Error: unable to process \`${t}\` because of error \`${e.message}\``);return t}}const m=window.wp.data,h=window.wc.tracks,u=window.wc.wcSettings,w=window.wc.components;var g=n(39793);class _ extends s.Component{setDismissed=e=>{this.props.updateOptions({woocommerce_shipping_dismissed_timestamp:e})};hideBanner=()=>{document.getElementById("woocommerce-admin-print-label").style.display="none"};remindMeLaterClicked=()=>{const{onCloseAll:e,trackElementClicked:t}=this.props;this.setDismissed(Date.now()),e(),this.hideBanner(),t("shipping_banner_dismiss_modal_remind_me_later")};closeForeverClicked=()=>{const{onCloseAll:e,trackElementClicked:t}=this.props;this.setDismissed(-1),e(),this.hideBanner(),t("shipping_banner_dismiss_modal_close_forever")};render(){const{onClose:e,visible:t}=this.props;return t?(0,g.jsxs)(r.Modal,{title:(0,o.__)("Are you sure?","woocommerce"),onRequestClose:e,className:"wc-admin-shipping-banner__dismiss-modal",children:[(0,g.jsx)("p",{className:"wc-admin-shipping-banner__dismiss-modal-help-text",children:(0,o.__)("With WooCommerce Shipping you can Print shipping labels from your WooCommerce dashboard at the lowest USPS rates.","woocommerce")}),(0,g.jsxs)("div",{className:"wc-admin-shipping-banner__dismiss-modal-actions",children:[(0,g.jsx)(r.Button,{isSecondary:!0,onClick:this.remindMeLaterClicked,children:(0,o.__)("Remind me later","woocommerce")}),(0,g.jsx)(r.Button,{isPrimary:!0,onClick:this.closeForeverClicked,children:(0,o.__)("I don't need this","woocommerce")})]})]}):null}}const b=(0,c.compose)((0,m.withDispatch)((e=>{const{updateOptions:t}=e(i.optionsStore);return{updateOptions:t}})))(_),v=(0,s.forwardRef)((function({icon:e,size:t=24,...n},i){return(0,s.cloneElement)(e,{width:t,height:t,...n,ref:i})})),S=window.wp.primitives,f=(0,g.jsx)(S.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"-2 -2 24 24",children:(0,g.jsx)(S.Path,{d:"M10 2c4.42 0 8 3.58 8 8s-3.58 8-8 8-8-3.58-8-8 3.58-8 8-8zm1.13 9.38l.35-6.46H8.52l.35 6.46h2.26zm-.09 3.36c.24-.23.37-.55.37-.96 0-.42-.12-.74-.36-.97s-.59-.35-1.06-.35-.82.12-1.07.35-.37.55-.37.97c0 .***********.***********.34 1.06.34s.8-.11 1.05-.34z"})}),y="download",k="install",C="activate",E="setup",x="start",j={[y]:(0,o.__)("download","woocommerce"),[k]:(0,o.__)("install","woocommerce"),[C]:(0,o.__)("activate","woocommerce"),[E]:(0,o.__)("set up","woocommerce"),[x]:(0,o.__)("start","woocommerce")};function P({isSetupError:e,errorReason:t}){return e?(0,g.jsxs)("div",{className:"wc-admin-shipping-banner-install-error",children:[(0,g.jsx)(v,{icon:f,className:"warning-icon"}),(e=>{const t=e in j?j[e]:j[E];return(0,o.sprintf)((0,o.__)("Unable to %s the plugin. Refresh the page and try again.","woocommerce"),t)})(t)]}):null}const B=window.wp.apiFetch;var L=n.n(B);const I=(0,u.getSetting)("wcAssetUrl",""),A="woocommerce-shipping",W="woocommerce-services";class M extends s.Component{constructor(e){super(e),this.state={showShippingBanner:!0,isDismissModalOpen:!1,setupErrorReason:E,wcsAssetsLoaded:!1,wcsAssetsLoading:!1,wcsSetupError:!1,isShippingLabelButtonBusy:!1,isWcsModalOpen:!1}}componentDidMount(){const{showShippingBanner:e}=this.state;e&&this.trackImpression()}isSetupError=()=>this.state.wcsSetupError;closeDismissModal=()=>{this.setState({isDismissModalOpen:!1}),this.trackElementClicked("shipping_banner_dismiss_modal_close_button")};openDismissModal=()=>{this.setState({isDismissModalOpen:!0}),this.trackElementClicked("shipping_banner_dimiss")};hideBanner=()=>{this.setState({showShippingBanner:!1})};createShippingLabelClicked=()=>{const{activePlugins:e}=this.props;this.setState({isShippingLabelButtonBusy:!0}),this.trackElementClicked("shipping_banner_create_label"),e.includes(A)?this.acceptTosAndGetWCSAssets():this.installAndActivatePlugins(A)};async installAndActivatePlugins(e){const{installPlugins:t,activatePlugins:n,isRequesting:s,activePlugins:i,isWcstCompatible:o,isIncompatibleWCShippingInstalled:r}=this.props;if(s)return!1;!0===(await t([e])).success?!0===(await n([e])).success?r?window.location.reload(!0):!i.includes(A)&&o?this.acceptTosAndGetWCSAssets():this.setState({showShippingBanner:!1}):this.setState({setupErrorReason:C,wcsSetupError:!0}):this.setState({setupErrorReason:k,wcsSetupError:!0})}woocommerceServiceLinkClicked=()=>{this.trackElementClicked("shipping_banner_woocommerce_service_link")};trackBannerEvent=(e,t={})=>{const{activePlugins:n,isJetpackConnected:s}=this.props;(0,h.recordEvent)(e,{banner_name:"wcadmin_install_wcs_prompt",jetpack_installed:n.includes("jetpack"),jetpack_connected:s,wcs_installed:n.includes(A),...t})};trackImpression=()=>{this.trackBannerEvent("banner_impression")};trackElementClicked=e=>{this.trackBannerEvent("banner_element_clicked",{element:e})};acceptTosAndGetWCSAssets=()=>L()({path:"/wcshipping/v1/tos",method:"POST",data:{accepted:!0}}).then((()=>function(e){const t=`wcshipping/v1/config/label-purchase/${e}`;return L()({path:t,method:"GET"})}(this.props.orderId))).then((e=>(window.WCShipping_Config=e.config,e))).then((()=>L()({path:"/wcshipping/v1/assets",method:"GET"}))).then((e=>this.loadWcsAssets(e))).catch((()=>{this.setState({wcsSetupError:!0})}));generateMetaBoxHtml(e,t,n){return`\n<div id="${e}" class="postbox">\n\t<div class="postbox-header">\n\t\t<h2 class="hndle"><span>${t}</span></h2>\n\t\t<div class="handle-actions">\n\t\t\t<button type="button" class="handlediv" aria-expanded="true">\n\t\t\t\t<span class="screen-reader-text">${(0,o.__)("Toggle panel:","woocommerce")} ${t}</span>\n\t\t\t\t<span class="toggle-indicator" aria-hidden="true"></span>\n\t\t\t</button>\n\t\t</div>\n\t</div>\n\t<div class="inside">\n\t\t<div class="wcc-root woocommerce woocommerce-shipping-shipping-label" id="woocommerce-shipping-shipping-label-${n.context}"></div>\n\t</div>\n</div>\n`}loadWcsAssets({assets:e}){if(this.state.wcsAssetsLoaded||this.state.wcsAssetsLoading)return void this.openWcsModal();this.setState({wcsAssetsLoading:!0});const t="woocommerce-order-label",n="woocommerce-order-shipment-tracking",s=e.wcshipping_create_label_script,i=e.wcshipping_create_label_style,r=e.wcshipping_shipment_tracking_script,c=e.wcshipping_shipment_tracking_style,{activePlugins:a}=this.props;document.getElementById(t)?.remove();const l=this.generateMetaBoxHtml(t,(0,o.__)("Shipping Label","woocommerce"),{context:"shipping_label"});document.getElementById("woocommerce-order-data").insertAdjacentHTML("beforebegin",l),document.getElementById(n)?.remove();const p=this.generateMetaBoxHtml(n,(0,o.__)("Shipment Tracking","woocommerce"),{context:"shipment_tracking"});document.getElementById("woocommerce-order-actions").insertAdjacentHTML("afterend",p),window.jQuery&&(window.jQuery("#normal-sortables").sortable("refresh"),window.jQuery("#side-sortables").sortable("refresh"),window.jQuery("#woocommerce-order-label").hide()),document.querySelectorAll('script[src*="/woocommerce-services/"]').forEach((e=>e.remove?.())),document.querySelectorAll('link[href*="/woocommerce-services/"]').forEach((e=>e.remove?.())),Promise.all([new Promise(((e,t)=>{const n=document.createElement("script");n.src=s,n.async=!0,n.onload=e,n.onerror=t,document.body.appendChild(n)})),new Promise(((e,t)=>{const n=document.createElement("script");n.src=r,n.async=!0,n.onload=e,n.onerror=t,document.body.appendChild(n)})),new Promise(((e,t)=>{if(""!==i){const n=document.createElement("link");n.rel="stylesheet",n.type="text/css",n.href=i,n.media="all",n.onload=e,n.onerror=t,n.id="wcshipping-injected-styles",document.head.appendChild(n)}else e()})),new Promise(((e,t)=>{if(""!==c){const n=document.createElement("link");n.rel="stylesheet",n.type="text/css",n.href=c,n.media="all",n.onload=e,n.onerror=t,n.id="wcshipping-injected-styles",document.head.appendChild(n)}else e()}))]).then((()=>{this.setState({wcsAssetsLoaded:!0,wcsAssetsLoading:!1,isShippingLabelButtonBusy:!1}),window.jQuery&&window.jQuery("#woocommerce-order-label").show(),document.getElementById("woocommerce-admin-print-label").style.display="none",a.includes(W)||this.openWcsModal()}))}openWcsModal(){const e="#woocommerce-shipping-shipping-label-shipping_label button";if(window.MutationObserver){var t,n;new window.MutationObserver(((t,n)=>{const s=document.querySelector(e);s&&(s.click(),n.disconnect())})).observe(null!==(t=null!==(n=document.getElementById("woocommerce-shipping-shipping-label-shipping_label"))&&void 0!==n?n:document.getElementById("wpbody-content"))&&void 0!==t?t:document.body,{childList:!0,subtree:!0})}else{const t=setInterval((()=>{const n=document.querySelector(e);n&&(n.click(),clearInterval(t))}),300)}}render(){const{isDismissModalOpen:e,showShippingBanner:t,isShippingLabelButtonBusy:n}=this.state,{isWcstCompatible:s}=this.props;if(!t&&!s)return document.getElementById("woocommerce-admin-print-label").classList.add("error"),(0,g.jsx)("p",{children:(0,g.jsx)("strong",{children:d({mixedString:(0,o.__)("Please {{pluginPageLink}}update{{/pluginPageLink}} the WooCommerce Shipping & Tax plugin to the latest version to ensure compatibility with WooCommerce Shipping.","woocommerce"),components:{pluginPageLink:(0,g.jsx)(w.Link,{href:(0,u.getAdminLink)("plugins.php"),target:"_blank",type:"wp-admin"})}})})});if(!t)return null;const{actionButtonLabel:i,headline:c}=this.props;return(0,g.jsxs)("div",{children:[(0,g.jsxs)("div",{className:"wc-admin-shipping-banner-container",children:[(0,g.jsx)("img",{className:"wc-admin-shipping-banner-illustration",src:I+"images/shippingillustration.svg",alt:(0,o.__)("Shipping ","woocommerce")}),(0,g.jsxs)("div",{className:"wc-admin-shipping-banner-blob",children:[(0,g.jsx)("h3",{children:c}),(0,g.jsx)("p",{children:d({mixedString:(0,o.sprintf)((0,o.__)('By clicking "%s", {{wcsLink}}WooCommerce Shipping{{/wcsLink}} will be installed and you agree to its {{tosLink}}Terms of Service{{/tosLink}}.',"woocommerce"),i),components:{tosLink:(0,g.jsx)(r.ExternalLink,{href:"https://wordpress.com/tos",target:"_blank",type:"external"}),wcsLink:(0,g.jsx)(r.ExternalLink,{href:"https://woocommerce.com/products/shipping/?utm_medium=product",target:"_blank",type:"external",onClick:this.woocommerceServiceLinkClicked})}})}),(0,g.jsx)(P,{isSetupError:this.isSetupError(),errorReason:this.state.setupErrorReason})]}),(0,g.jsx)(r.Button,{disabled:n,isPrimary:!0,isBusy:n,onClick:this.createShippingLabelClicked,children:i}),(0,g.jsx)("button",{onClick:this.openDismissModal,type:"button",className:"notice-dismiss",disabled:this.state.isShippingLabelButtonBusy,children:(0,g.jsx)("span",{className:"screen-reader-text",children:(0,o.__)("Close Print Label Banner.","woocommerce")})})]}),(0,g.jsx)(b,{visible:e,onClose:this.closeDismissModal,onCloseAll:this.hideBanner,trackElementClicked:this.trackElementClicked})]})}}const O=(0,c.compose)((0,m.withSelect)((e=>{const{isPluginsRequesting:t,isJetpackConnected:n,getActivePlugins:s}=e(i.pluginsStore),r=t("activatePlugins")||t("installPlugins"),c=s(),a=c.includes(W)?(0,o.__)("Install WooCommerce Shipping","woocommerce"):(0,o.__)("Create shipping label","woocommerce"),l=c.includes(W)?(0,o.__)("Print discounted shipping labels with a click, now with the dedicated plugin!","woocommerce"):(0,o.__)("Print discounted shipping labels with a click.","woocommerce");return{isRequesting:r,isJetpackConnected:n(),activePlugins:c,actionButtonLabel:a,headline:l,orderId:parseInt(window.wcShippingCoreData.order_id,10),isWcstCompatible:[1,"1"].includes(window.wcShippingCoreData.is_wcst_compatible),isIncompatibleWCShippingInstalled:[1,"1"].includes(window.wcShippingCoreData.is_incompatible_wcshipping_installed)}})),(0,m.withDispatch)((e=>{const{activatePlugins:t,installPlugins:n}=e(i.pluginsStore);return{activatePlugins:t,installPlugins:n}})))(M),D=["wcAdminSettings","preloadSettings"],R=(0,u.getSetting)("admin",{}),T=Object.keys(R).reduce(((e,t)=>(D.includes(t)||(e[t]=R[t]),e)),{}),N={onboarding:{profile:"Deprecated: wcSettings.admin.onboarding.profile is deprecated. It is planned to be released in WooCommerce 10.0.0. Please use `getProfileItems` from the onboarding store. See https://github.com/woocommerce/woocommerce/tree/trunk/packages/js/data/src/onboarding for more information.",euCountries:"Deprecated: wcSettings.admin.onboarding.euCountries is deprecated. Please use `/wc/v3/data/continents/eu` from the REST API. See https://woocommerce.github.io/woocommerce-rest-api-docs/#list-all-continents for more information.",localInfo:'Deprecated: wcSettings.admin.onboarding.localInfo is deprecated. Please use `include WC()->plugin_path() . "/i18n/locale-info.php"` instead.',currencySymbols:'"Deprecated: wcSettings.admin.onboarding.currencySymbols is deprecated. Please use get_woocommerce_currency_symbols() function instead.'}};function $(e,t=!1,n=e=>e,s=N){if(D.includes(e))throw new Error((0,o.__)("Mutable settings should be accessed via data store.","woocommerce"));return n(T.hasOwnProperty(e)?T[e]:t,t)}(0,u.getSetting)("adminUrl"),(0,u.getSetting)("countries"),(0,u.getSetting)("currency"),(0,u.getSetting)("locale"),(0,u.getSetting)("siteTitle"),(0,u.getSetting)("wcAssetUrl"),$("orderStatuses");const q=document.getElementById("wc-admin-shipping-banner-root"),H=q.dataset.args&&JSON.parse(q.dataset.args)||{},U=(0,i.withPluginsHydration)({...$("plugins"),jetpackStatus:$("dataEndpoints",{}).jetpackStatus})(O);(0,s.createRoot)(q).render((0,g.jsx)(U,{itemsCount:H.items})),(window.wc=window.wc||{}).printShippingLabelBanner={}})();