"use strict";var __importDefault=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.Edit=Edit;const block_templates_1=require("@woocommerce/block-templates"),components_1=require("@woocommerce/components"),tracks_1=require("@woocommerce/tracks"),components_2=require("@wordpress/components"),core_data_1=require("@wordpress/core-data"),element_1=require("@wordpress/element"),i18n_1=require("@wordpress/i18n"),moment_1=__importDefault(require("moment")),date_1=require("@wordpress/date"),use_product_edits_1=require("../../../hooks/use-product-edits"),validation_context_1=require("../../../contexts/validation-context");function Edit({attributes:e,clientId:t,context:o}){const r=(0,block_templates_1.useWooBlockProps)(e),{hasEdit:a}=(0,use_product_edits_1.useProductEdits)(),n=(0,date_1.getSettings)().formats.datetime,[s,_]=(0,element_1.useState)(!1),[c]=(0,core_data_1.useEntityProp)("postType",o.postType||"product","sale_price"),l=Number.parseFloat(c||"0")>0,[m,i]=(0,core_data_1.useEntityProp)("postType",o.postType||"product","date_on_sale_from_gmt"),[d,u]=(0,core_data_1.useEntityProp)("postType",o.postType||"product","date_on_sale_to_gmt"),p=(0,moment_1.default)().startOf("minute").toISOString();(0,element_1.useEffect)((()=>{a("sale_price")&&!l&&(_(!1),i(""),u(""))}),[l]),(0,element_1.useEffect)((()=>{(m||d)&&_(!0)}),[m,d]);const f=(0,moment_1.default)(m,moment_1.default.ISO_8601,!0),h=(0,moment_1.default)(d,moment_1.default.ISO_8601,!0),{ref:w,error:g,validate:b}=(0,validation_context_1.useValidation)(`date_on_sale_from_gmt-${t}`,(async function(){if(s&&m){if(!f.isValid())return{message:(0,i18n_1.__)("Please enter a valid date.","woocommerce")};if(f.isAfter(h))return{message:(0,i18n_1.__)("The start date of the sale must be before the end date.","woocommerce")}}}),[s,m,f,h]),{ref:E,error:k,validate:v}=(0,validation_context_1.useValidation)(`date_on_sale_to_gmt-${t}`,(async function(){if(s&&d){if(!h.isValid())return{message:(0,i18n_1.__)("Please enter a valid date.","woocommerce")};if(h.isBefore(f))return{message:(0,i18n_1.__)("The end date of the sale must be after the start date.","woocommerce")}}}),[s,m,f,h]);return(0,element_1.createElement)("div",{...r},(0,element_1.createElement)(components_2.ToggleControl,{label:(0,i18n_1.__)("Schedule sale","woocommerce"),checked:s,onChange:function(e){(0,tracks_1.recordEvent)("product_pricing_schedule_sale_toggle_click",{enabled:e}),_(e),e?(i(p),u("")):(i(""),u(""))},disabled:!l}),s&&(0,element_1.createElement)("div",{className:"wp-block-columns wp-block-woocommerce-product-schedule-sale-fields__content"},(0,element_1.createElement)("div",{className:"wp-block-column"},(0,element_1.createElement)(components_1.DateTimePickerControl,{ref:w,label:(0,i18n_1.__)("From","woocommerce"),placeholder:(0,i18n_1.__)("Sale start date and time (optional)","woocommerce"),dateTimeFormat:n,currentDate:m,onChange:i,className:g&&"has-error",help:g,onBlur:()=>b()})),(0,element_1.createElement)("div",{className:"wp-block-column"},(0,element_1.createElement)(components_1.DateTimePickerControl,{ref:E,label:(0,i18n_1.__)("To","woocommerce"),placeholder:(0,i18n_1.__)("Sale end date and time (optional)","woocommerce"),dateTimeFormat:n,currentDate:d,onChange:e=>u((0,moment_1.default)(e).startOf("minute").toISOString()),onBlur:()=>v(),className:k&&"has-error",help:k}))))}