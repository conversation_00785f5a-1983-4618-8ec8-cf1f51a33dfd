var t={d:(e,n)=>{for(var r in n)t.o(n,r)&&!t.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:n[r]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)},e={};t.d(e,{zj:()=>Qe,SD:()=>ne,V6:()=>re,$K:()=>oe,vT:()=>Xe,jb:()=>Mn,yT:()=>se,M_:()=>Ze,hb:()=>fe,vJ:()=>_e,ip:()=>le,Nf:()=>ae,Kr:()=>pe,li:()=>ut,J0:()=>it,FH:()=>ce,v4:()=>ue,mh:()=>ve});var n,r,o,i,s,u,c,l,_,a,f,p,h,v={},d=[],y=/acit|ex(?:s|g|n|p|$)|rph|grid|ows|mnc|ntw|ine[ch]|zoo|^ord|itera/i,g=Array.isArray;function m(t,e){for(var n in e)t[n]=e[n];return t}function w(t){t&&t.parentNode&&t.parentNode.removeChild(t)}function b(t,e,r){var o,i,s,u={};for(s in e)"key"==s?o=e[s]:"ref"==s?i=e[s]:u[s]=e[s];if(arguments.length>2&&(u.children=arguments.length>3?n.call(arguments,2):r),"function"==typeof t&&null!=t.defaultProps)for(s in t.defaultProps)void 0===u[s]&&(u[s]=t.defaultProps[s]);return S(t,u,o,i,null)}function S(t,e,n,i,s){var u={type:t,props:e,key:n,ref:i,__k:null,__:null,__b:0,__e:null,__c:null,constructor:void 0,__v:null==s?++o:s,__i:-1,__u:0};return null==s&&null!=r.vnode&&r.vnode(u),u}function k(t){return t.children}function x(t,e){this.props=t,this.context=e}function E(t,e){if(null==e)return t.__?E(t.__,t.__i+1):null;for(var n;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e)return n.__e;return"function"==typeof t.type?E(t):null}function P(t){var e,n;if(null!=(t=t.__)&&null!=t.__c){for(t.__e=t.__c.base=null,e=0;e<t.__k.length;e++)if(null!=(n=t.__k[e])&&null!=n.__e){t.__e=t.__c.base=n.__e;break}return P(t)}}function T(t){(!t.__d&&(t.__d=!0)&&s.push(t)&&!C.__r++||u!==r.debounceRendering)&&((u=r.debounceRendering)||c)(C)}function C(){var t,e,n,o,i,u,c,_;for(s.sort(l);t=s.shift();)t.__d&&(e=s.length,o=void 0,u=(i=(n=t).__v).__e,c=[],_=[],n.__P&&((o=m({},i)).__v=i.__v+1,r.vnode&&r.vnode(o),W(n.__P,o,i,n.__n,n.__P.namespaceURI,32&i.__u?[u]:null,c,null==u?E(i):u,!!(32&i.__u),_),o.__v=i.__v,o.__.__k[o.__i]=o,L(c,o,_),o.__e!=u&&P(o)),s.length>e&&s.sort(l));C.__r=0}function N(t,e,n,r,o,i,s,u,c,l,_){var a,f,p,h,y,g,m=r&&r.__k||d,w=e.length;for(c=U(n,e,m,c),a=0;a<w;a++)null!=(p=n.__k[a])&&(f=-1===p.__i?v:m[p.__i]||v,p.__i=a,g=W(t,p,f,o,i,s,u,c,l,_),h=p.__e,p.ref&&f.ref!=p.ref&&(f.ref&&R(f.ref,null,p),_.push(p.ref,p.__c||h,p)),null==y&&null!=h&&(y=h),4&p.__u||f.__k===p.__k?c=O(p,c,t):"function"==typeof p.type&&void 0!==g?c=g:h&&(c=h.nextSibling),p.__u&=-7);return n.__e=y,c}function U(t,e,n,r){var o,i,s,u,c,l=e.length,_=n.length,a=_,f=0;for(t.__k=[],o=0;o<l;o++)null!=(i=e[o])&&"boolean"!=typeof i&&"function"!=typeof i?(u=o+f,(i=t.__k[o]="string"==typeof i||"number"==typeof i||"bigint"==typeof i||i.constructor==String?S(null,i,null,null,null):g(i)?S(k,{children:i},null,null,null):void 0===i.constructor&&i.__b>0?S(i.type,i.props,i.key,i.ref?i.ref:null,i.__v):i).__=t,i.__b=t.__b+1,s=null,-1!==(c=i.__i=$(i,n,u,a))&&(a--,(s=n[c])&&(s.__u|=2)),null==s||null===s.__v?(-1==c&&f--,"function"!=typeof i.type&&(i.__u|=4)):c!==u&&(c==u-1?f--:c==u+1?f++:(c>u?f--:f++,i.__u|=4))):i=t.__k[o]=null;if(a)for(o=0;o<_;o++)null!=(s=n[o])&&!(2&s.__u)&&(s.__e==r&&(r=E(s)),A(s,s));return r}function O(t,e,n){var r,o;if("function"==typeof t.type){for(r=t.__k,o=0;r&&o<r.length;o++)r[o]&&(r[o].__=t,e=O(r[o],e,n));return e}t.__e!=e&&(e&&t.type&&!n.contains(e)&&(e=E(t)),n.insertBefore(t.__e,e||null),e=t.__e);do{e=e&&e.nextSibling}while(null!=e&&8===e.nodeType);return e}function $(t,e,n,r){var o=t.key,i=t.type,s=n-1,u=n+1,c=e[n];if(null===c||c&&o==c.key&&i===c.type&&!(2&c.__u))return n;if(("function"!=typeof i||i===k||o)&&r>(null==c||2&c.__u?0:1))for(;s>=0||u<e.length;){if(s>=0){if((c=e[s])&&!(2&c.__u)&&o==c.key&&i===c.type)return s;s--}if(u<e.length){if((c=e[u])&&!(2&c.__u)&&o==c.key&&i===c.type)return u;u++}}return-1}function M(t,e,n){"-"===e[0]?t.setProperty(e,null==n?"":n):t[e]=null==n?"":"number"!=typeof n||y.test(e)?n:n+"px"}function j(t,e,n,r,o){var i;t:if("style"===e)if("string"==typeof n)t.style.cssText=n;else{if("string"==typeof r&&(t.style.cssText=r=""),r)for(e in r)n&&e in n||M(t.style,e,"");if(n)for(e in n)r&&n[e]===r[e]||M(t.style,e,n[e])}else if("o"===e[0]&&"n"===e[1])i=e!==(e=e.replace(_,"$1")),e=e.toLowerCase()in t||"onFocusOut"===e||"onFocusIn"===e?e.toLowerCase().slice(2):e.slice(2),t.l||(t.l={}),t.l[e+i]=n,n?r?n.u=r.u:(n.u=a,t.addEventListener(e,i?p:f,i)):t.removeEventListener(e,i?p:f,i);else{if("http://www.w3.org/2000/svg"==o)e=e.replace(/xlink(H|:h)/,"h").replace(/sName$/,"s");else if("width"!=e&&"height"!=e&&"href"!=e&&"list"!=e&&"form"!=e&&"tabIndex"!=e&&"download"!=e&&"rowSpan"!=e&&"colSpan"!=e&&"role"!=e&&"popover"!=e&&e in t)try{t[e]=null==n?"":n;break t}catch(t){}"function"==typeof n||(null==n||!1===n&&"-"!==e[4]?t.removeAttribute(e):t.setAttribute(e,"popover"==e&&1==n?"":n))}}function H(t){return function(e){if(this.l){var n=this.l[e.type+t];if(null==e.t)e.t=a++;else if(e.t<n.u)return;return n(r.event?r.event(e):e)}}}function W(t,e,n,o,i,s,u,c,l,_){var a,f,p,h,v,d,y,b,S,E,P,T,C,U,O,$,M,j=e.type;if(void 0!==e.constructor)return null;128&n.__u&&(l=!!(32&n.__u),s=[c=e.__e=n.__e]),(a=r.__b)&&a(e);t:if("function"==typeof j)try{if(b=e.props,S="prototype"in j&&j.prototype.render,E=(a=j.contextType)&&o[a.__c],P=a?E?E.props.value:a.__:o,n.__c?y=(f=e.__c=n.__c).__=f.__E:(S?e.__c=f=new j(b,P):(e.__c=f=new x(b,P),f.constructor=j,f.render=F),E&&E.sub(f),f.props=b,f.state||(f.state={}),f.context=P,f.__n=o,p=f.__d=!0,f.__h=[],f._sb=[]),S&&null==f.__s&&(f.__s=f.state),S&&null!=j.getDerivedStateFromProps&&(f.__s==f.state&&(f.__s=m({},f.__s)),m(f.__s,j.getDerivedStateFromProps(b,f.__s))),h=f.props,v=f.state,f.__v=e,p)S&&null==j.getDerivedStateFromProps&&null!=f.componentWillMount&&f.componentWillMount(),S&&null!=f.componentDidMount&&f.__h.push(f.componentDidMount);else{if(S&&null==j.getDerivedStateFromProps&&b!==h&&null!=f.componentWillReceiveProps&&f.componentWillReceiveProps(b,P),!f.__e&&(null!=f.shouldComponentUpdate&&!1===f.shouldComponentUpdate(b,f.__s,P)||e.__v===n.__v)){for(e.__v!==n.__v&&(f.props=b,f.state=f.__s,f.__d=!1),e.__e=n.__e,e.__k=n.__k,e.__k.some((function(t){t&&(t.__=e)})),T=0;T<f._sb.length;T++)f.__h.push(f._sb[T]);f._sb=[],f.__h.length&&u.push(f);break t}null!=f.componentWillUpdate&&f.componentWillUpdate(b,f.__s,P),S&&null!=f.componentDidUpdate&&f.__h.push((function(){f.componentDidUpdate(h,v,d)}))}if(f.context=P,f.props=b,f.__P=t,f.__e=!1,C=r.__r,U=0,S){for(f.state=f.__s,f.__d=!1,C&&C(e),a=f.render(f.props,f.state,f.context),O=0;O<f._sb.length;O++)f.__h.push(f._sb[O]);f._sb=[]}else do{f.__d=!1,C&&C(e),a=f.render(f.props,f.state,f.context),f.state=f.__s}while(f.__d&&++U<25);f.state=f.__s,null!=f.getChildContext&&(o=m(m({},o),f.getChildContext())),S&&!p&&null!=f.getSnapshotBeforeUpdate&&(d=f.getSnapshotBeforeUpdate(h,v)),c=N(t,g($=null!=a&&a.type===k&&null==a.key?a.props.children:a)?$:[$],e,n,o,i,s,u,c,l,_),f.base=e.__e,e.__u&=-161,f.__h.length&&u.push(f),y&&(f.__E=f.__=null)}catch(t){if(e.__v=null,l||null!=s)if(t.then){for(e.__u|=l?160:128;c&&8===c.nodeType&&c.nextSibling;)c=c.nextSibling;s[s.indexOf(c)]=null,e.__e=c}else for(M=s.length;M--;)w(s[M]);else e.__e=n.__e,e.__k=n.__k;r.__e(t,e,n)}else null==s&&e.__v===n.__v?(e.__k=n.__k,e.__e=n.__e):c=e.__e=I(n.__e,e,n,o,i,s,u,l,_);return(a=r.diffed)&&a(e),128&e.__u?void 0:c}function L(t,e,n){for(var o=0;o<n.length;o++)R(n[o],n[++o],n[++o]);r.__c&&r.__c(e,t),t.some((function(e){try{t=e.__h,e.__h=[],t.some((function(t){t.call(e)}))}catch(t){r.__e(t,e.__v)}}))}function I(t,e,o,i,s,u,c,l,_){var a,f,p,h,d,y,m,b=o.props,S=e.props,k=e.type;if("svg"===k?s="http://www.w3.org/2000/svg":"math"===k?s="http://www.w3.org/1998/Math/MathML":s||(s="http://www.w3.org/1999/xhtml"),null!=u)for(a=0;a<u.length;a++)if((d=u[a])&&"setAttribute"in d==!!k&&(k?d.localName===k:3===d.nodeType)){t=d,u[a]=null;break}if(null==t){if(null===k)return document.createTextNode(S);t=document.createElementNS(s,k,S.is&&S),l&&(r.__m&&r.__m(e,u),l=!1),u=null}if(null===k)b===S||l&&t.data===S||(t.data=S);else{if(u=u&&n.call(t.childNodes),b=o.props||v,!l&&null!=u)for(b={},a=0;a<t.attributes.length;a++)b[(d=t.attributes[a]).name]=d.value;for(a in b)if(d=b[a],"children"==a);else if("dangerouslySetInnerHTML"==a)p=d;else if(!(a in S)){if("value"==a&&"defaultValue"in S||"checked"==a&&"defaultChecked"in S)continue;j(t,a,null,d,s)}for(a in S)d=S[a],"children"==a?h=d:"dangerouslySetInnerHTML"==a?f=d:"value"==a?y=d:"checked"==a?m=d:l&&"function"!=typeof d||b[a]===d||j(t,a,d,b[a],s);if(f)l||p&&(f.__html===p.__html||f.__html===t.innerHTML)||(t.innerHTML=f.__html),e.__k=[];else if(p&&(t.innerHTML=""),N(t,g(h)?h:[h],e,o,i,"foreignObject"===k?"http://www.w3.org/1999/xhtml":s,u,c,u?u[0]:o.__k&&E(o,0),l,_),null!=u)for(a=u.length;a--;)w(u[a]);l||(a="value","progress"===k&&null==y?t.removeAttribute("value"):void 0!==y&&(y!==t[a]||"progress"===k&&!y||"option"===k&&y!==b[a])&&j(t,a,y,b[a],s),a="checked",void 0!==m&&m!==t[a]&&j(t,a,m,b[a],s))}return t}function R(t,e,n){try{if("function"==typeof t){var o="function"==typeof t.__u;o&&t.__u(),o&&null==e||(t.__u=t(e))}else t.current=e}catch(t){r.__e(t,n)}}function A(t,e,n){var o,i;if(r.unmount&&r.unmount(t),(o=t.ref)&&(o.current&&o.current!==t.__e||R(o,null,e)),null!=(o=t.__c)){if(o.componentWillUnmount)try{o.componentWillUnmount()}catch(t){r.__e(t,e)}o.base=o.__P=null}if(o=t.__k)for(i=0;i<o.length;i++)o[i]&&A(o[i],e,n||"function"!=typeof t.type);n||w(t.__e),t.__c=t.__=t.__e=void 0}function F(t,e,n){return this.constructor(t,n)}function G(t,e,o){var i,s,u,c;e===document&&(e=document.documentElement),r.__&&r.__(t,e),s=(i="function"==typeof o)?null:o&&o.__k||e.__k,u=[],c=[],W(e,t=(!i&&o||e).__k=b(k,null,[t]),s||v,v,e.namespaceURI,!i&&o?[o]:s?null:e.firstChild?n.call(e.childNodes):null,u,!i&&o?o:s?s.__e:e.firstChild,i,c),L(u,t,c)}function D(t,e){G(t,e,D)}function B(t,e,r){var o,i,s,u,c=m({},t.props);for(s in t.type&&t.type.defaultProps&&(u=t.type.defaultProps),e)"key"==s?o=e[s]:"ref"==s?i=e[s]:c[s]=void 0===e[s]&&void 0!==u?u[s]:e[s];return arguments.length>2&&(c.children=arguments.length>3?n.call(arguments,2):r),S(t.type,c,o||t.key,i||t.ref,null)}n=d.slice,r={__e:function(t,e,n,r){for(var o,i,s;e=e.__;)if((o=e.__c)&&!o.__)try{if((i=o.constructor)&&null!=i.getDerivedStateFromError&&(o.setState(i.getDerivedStateFromError(t)),s=o.__d),null!=o.componentDidCatch&&(o.componentDidCatch(t,r||{}),s=o.__d),s)return o.__E=o}catch(e){t=e}throw t}},o=0,i=function(t){return null!=t&&null==t.constructor},x.prototype.setState=function(t,e){var n;n=null!=this.__s&&this.__s!==this.state?this.__s:this.__s=m({},this.state),"function"==typeof t&&(t=t(m({},n),this.props)),t&&m(n,t),null!=t&&this.__v&&(e&&this._sb.push(e),T(this))},x.prototype.forceUpdate=function(t){this.__v&&(this.__e=!0,t&&this.__h.push(t),T(this))},x.prototype.render=k,s=[],c="function"==typeof Promise?Promise.prototype.then.bind(Promise.resolve()):setTimeout,l=function(t,e){return t.__v.__b-e.__v.__b},C.__r=0,_=/(PointerCapture)$|Capture$/i,a=0,f=H(!1),p=H(!0),h=0;var V,z,J,K,q=0,Q=[],X=r,Y=X.__b,Z=X.__r,tt=X.diffed,et=X.__c,nt=X.unmount,rt=X.__;function ot(t,e){X.__h&&X.__h(z,t,q||e),q=0;var n=z.__H||(z.__H={__:[],__h:[]});return t>=n.__.length&&n.__.push({}),n.__[t]}function it(t){return q=1,function(t,e){var n=ot(V++,2);if(n.t=t,!n.__c&&(n.__=[yt(void 0,e),function(t){var e=n.__N?n.__N[0]:n.__[0],r=n.t(e,t);e!==r&&(n.__N=[r,n.__[1]],n.__c.setState({}))}],n.__c=z,!z.u)){var r=function(t,e,r){if(!n.__c.__H)return!0;var i=n.__c.__H.__.filter((function(t){return!!t.__c}));if(i.every((function(t){return!t.__N})))return!o||o.call(this,t,e,r);var s=n.__c.props!==t;return i.forEach((function(t){if(t.__N){var e=t.__[0];t.__=t.__N,t.__N=void 0,e!==t.__[0]&&(s=!0)}})),o&&o.call(this,t,e,r)||s};z.u=!0;var o=z.shouldComponentUpdate,i=z.componentWillUpdate;z.componentWillUpdate=function(t,e,n){if(this.__e){var s=o;o=void 0,r(t,e,n),o=s}i&&i.call(this,t,e,n)},z.shouldComponentUpdate=r}return n.__N||n.__}(yt,t)}function st(t,e){var n=ot(V++,3);!X.__s&&dt(n.__H,e)&&(n.__=t,n.i=e,z.__H.__h.push(n))}function ut(t){return q=5,ct((function(){return{current:t}}),[])}function ct(t,e){var n=ot(V++,7);return dt(n.__H,e)&&(n.__=t(),n.__H=e,n.__h=t),n.__}function lt(t,e){return q=8,ct((function(){return t}),e)}function _t(t){var e=z.context[t.__c],n=ot(V++,9);return n.c=t,e?(null==n.__&&(n.__=!0,e.sub(z)),e.props.value):t.__}function at(){for(var t;t=Q.shift();)if(t.__P&&t.__H)try{t.__H.__h.forEach(ht),t.__H.__h.forEach(vt),t.__H.__h=[]}catch(e){t.__H.__h=[],X.__e(e,t.__v)}}X.__b=function(t){z=null,Y&&Y(t)},X.__=function(t,e){t&&e.__k&&e.__k.__m&&(t.__m=e.__k.__m),rt&&rt(t,e)},X.__r=function(t){Z&&Z(t),V=0;var e=(z=t.__c).__H;e&&(J===z?(e.__h=[],z.__h=[],e.__.forEach((function(t){t.__N&&(t.__=t.__N),t.i=t.__N=void 0}))):(e.__h.forEach(ht),e.__h.forEach(vt),e.__h=[],V=0)),J=z},X.diffed=function(t){tt&&tt(t);var e=t.__c;e&&e.__H&&(e.__H.__h.length&&(1!==Q.push(e)&&K===X.requestAnimationFrame||((K=X.requestAnimationFrame)||pt)(at)),e.__H.__.forEach((function(t){t.i&&(t.__H=t.i),t.i=void 0}))),J=z=null},X.__c=function(t,e){e.some((function(t){try{t.__h.forEach(ht),t.__h=t.__h.filter((function(t){return!t.__||vt(t)}))}catch(n){e.some((function(t){t.__h&&(t.__h=[])})),e=[],X.__e(n,t.__v)}})),et&&et(t,e)},X.unmount=function(t){nt&&nt(t);var e,n=t.__c;n&&n.__H&&(n.__H.__.forEach((function(t){try{ht(t)}catch(t){e=t}})),n.__H=void 0,e&&X.__e(e,n.__v))};var ft="function"==typeof requestAnimationFrame;function pt(t){var e,n=function(){clearTimeout(r),ft&&cancelAnimationFrame(e),setTimeout(t)},r=setTimeout(n,100);ft&&(e=requestAnimationFrame(n))}function ht(t){var e=z,n=t.__c;"function"==typeof n&&(t.__c=void 0,n()),z=e}function vt(t){var e=z;t.__c=t.__(),z=e}function dt(t,e){return!t||t.length!==e.length||e.some((function(e,n){return e!==t[n]}))}function yt(t,e){return"function"==typeof e?e(t):e}var gt=Symbol.for("preact-signals");function mt(){if(xt>1)xt--;else{for(var t,e=!1;void 0!==kt;){var n=kt;for(kt=void 0,Et++;void 0!==n;){var r=n.o;if(n.o=void 0,n.f&=-3,!(8&n.f)&&Ut(n))try{n.c()}catch(n){e||(t=n,e=!0)}n=r}}if(Et=0,xt--,e)throw t}}function wt(t){if(xt>0)return t();xt++;try{return t()}finally{mt()}}var bt,St=void 0,kt=void 0,xt=0,Et=0,Pt=0;function Tt(t){if(void 0!==St){var e=t.n;if(void 0===e||e.t!==St)return e={i:0,S:t,p:St.s,n:void 0,t:St,e:void 0,x:void 0,r:e},void 0!==St.s&&(St.s.n=e),St.s=e,t.n=e,32&St.f&&t.S(e),e;if(-1===e.i)return e.i=0,void 0!==e.n&&(e.n.p=e.p,void 0!==e.p&&(e.p.n=e.n),e.p=St.s,e.n=void 0,St.s.n=e,St.s=e),e}}function Ct(t){this.v=t,this.i=0,this.n=void 0,this.t=void 0}function Nt(t){return new Ct(t)}function Ut(t){for(var e=t.s;void 0!==e;e=e.n)if(e.S.i!==e.i||!e.S.h()||e.S.i!==e.i)return!0;return!1}function Ot(t){for(var e=t.s;void 0!==e;e=e.n){var n=e.S.n;if(void 0!==n&&(e.r=n),e.S.n=e,e.i=-1,void 0===e.n){t.s=e;break}}}function $t(t){for(var e=t.s,n=void 0;void 0!==e;){var r=e.p;-1===e.i?(e.S.U(e),void 0!==r&&(r.n=e.n),void 0!==e.n&&(e.n.p=r)):n=e,e.S.n=e.r,void 0!==e.r&&(e.r=void 0),e=r}t.s=n}function Mt(t){Ct.call(this,void 0),this.x=t,this.s=void 0,this.g=Pt-1,this.f=4}function jt(t){return new Mt(t)}function Ht(t){var e=t.u;if(t.u=void 0,"function"==typeof e){xt++;var n=St;St=void 0;try{e()}catch(e){throw t.f&=-2,t.f|=8,Wt(t),e}finally{St=n,mt()}}}function Wt(t){for(var e=t.s;void 0!==e;e=e.n)e.S.U(e);t.x=void 0,t.s=void 0,Ht(t)}function Lt(t){if(St!==this)throw new Error("Out-of-order effect");$t(this),St=t,this.f&=-2,8&this.f&&Wt(this),mt()}function It(t){this.x=t,this.u=void 0,this.s=void 0,this.o=void 0,this.f=32}function Rt(t){var e=new It(t);try{e.c()}catch(t){throw e.d(),t}return e.d.bind(e)}function At(t,e){r[t]=e.bind(null,r[t]||function(){})}function Ft(t){bt&&bt(),bt=t&&t.S()}function Gt(t){var e=this,n=t.data,r=function(t){return ct((function(){return Nt(t)}),[])}(n);r.value=n;var o=ct((function(){for(var t=e.__v;t=t.__;)if(t.__c){t.__c.__$f|=4;break}return e.__$u.c=function(){var t,n=e.__$u.S(),r=o.value;n(),i(r)||3!==(null==(t=e.base)?void 0:t.nodeType)?(e.__$f|=1,e.setState({})):e.base.data=r},jt((function(){var t=r.value.value;return 0===t?0:!0===t?"":t||""}))}),[]);return o.value}function Dt(t,e,n,r){var o=e in t&&void 0===t.ownerSVGElement,i=Nt(n);return{o:function(t,e){i.value=t,r=e},d:Rt((function(){var n=i.value.value;r[e]!==n&&(r[e]=n,o?t[e]=n:n?t.setAttribute(e,n):t.removeAttribute(e))}))}}Ct.prototype.brand=gt,Ct.prototype.h=function(){return!0},Ct.prototype.S=function(t){this.t!==t&&void 0===t.e&&(t.x=this.t,void 0!==this.t&&(this.t.e=t),this.t=t)},Ct.prototype.U=function(t){if(void 0!==this.t){var e=t.e,n=t.x;void 0!==e&&(e.x=n,t.e=void 0),void 0!==n&&(n.e=e,t.x=void 0),t===this.t&&(this.t=n)}},Ct.prototype.subscribe=function(t){var e=this;return Rt((function(){var n=e.value,r=St;St=void 0;try{t(n)}finally{St=r}}))},Ct.prototype.valueOf=function(){return this.value},Ct.prototype.toString=function(){return this.value+""},Ct.prototype.toJSON=function(){return this.value},Ct.prototype.peek=function(){var t=St;St=void 0;try{return this.value}finally{St=t}},Object.defineProperty(Ct.prototype,"value",{get:function(){var t=Tt(this);return void 0!==t&&(t.i=this.i),this.v},set:function(t){if(t!==this.v){if(Et>100)throw new Error("Cycle detected");this.v=t,this.i++,Pt++,xt++;try{for(var e=this.t;void 0!==e;e=e.x)e.t.N()}finally{mt()}}}}),(Mt.prototype=new Ct).h=function(){if(this.f&=-3,1&this.f)return!1;if(32==(36&this.f))return!0;if(this.f&=-5,this.g===Pt)return!0;if(this.g=Pt,this.f|=1,this.i>0&&!Ut(this))return this.f&=-2,!0;var t=St;try{Ot(this),St=this;var e=this.x();(16&this.f||this.v!==e||0===this.i)&&(this.v=e,this.f&=-17,this.i++)}catch(t){this.v=t,this.f|=16,this.i++}return St=t,$t(this),this.f&=-2,!0},Mt.prototype.S=function(t){if(void 0===this.t){this.f|=36;for(var e=this.s;void 0!==e;e=e.n)e.S.S(e)}Ct.prototype.S.call(this,t)},Mt.prototype.U=function(t){if(void 0!==this.t&&(Ct.prototype.U.call(this,t),void 0===this.t)){this.f&=-33;for(var e=this.s;void 0!==e;e=e.n)e.S.U(e)}},Mt.prototype.N=function(){if(!(2&this.f)){this.f|=6;for(var t=this.t;void 0!==t;t=t.x)t.t.N()}},Object.defineProperty(Mt.prototype,"value",{get:function(){if(1&this.f)throw new Error("Cycle detected");var t=Tt(this);if(this.h(),void 0!==t&&(t.i=this.i),16&this.f)throw this.v;return this.v}}),It.prototype.c=function(){var t=this.S();try{if(8&this.f)return;if(void 0===this.x)return;var e=this.x();"function"==typeof e&&(this.u=e)}finally{t()}},It.prototype.S=function(){if(1&this.f)throw new Error("Cycle detected");this.f|=1,this.f&=-9,Ht(this),Ot(this),xt++;var t=St;return St=this,Lt.bind(this,t)},It.prototype.N=function(){2&this.f||(this.f|=2,this.o=kt,kt=this)},It.prototype.d=function(){this.f|=8,1&this.f||Wt(this)},Gt.displayName="_st",Object.defineProperties(Ct.prototype,{constructor:{configurable:!0,value:void 0},type:{configurable:!0,value:Gt},props:{configurable:!0,get:function(){return{data:this}}},__b:{configurable:!0,value:1}}),At("__b",(function(t,e){if("string"==typeof e.type){var n,r=e.props;for(var o in r)if("children"!==o){var i=r[o];i instanceof Ct&&(n||(e.__np=n={}),n[o]=i,r[o]=i.peek())}}t(e)})),At("__r",(function(t,e){Ft();var n,r=e.__c;r&&(r.__$f&=-2,void 0===(n=r.__$u)&&(r.__$u=n=function(){var t;return Rt((function(){t=this})),t.c=function(){r.__$f|=1,r.setState({})},t}())),Ft(n),t(e)})),At("__e",(function(t,e,n,r){Ft(),t(e,n,r)})),At("diffed",(function(t,e){var n;if(Ft(),"string"==typeof e.type&&(n=e.__e)){var r=e.__np,o=e.props;if(r){var i=n.U;if(i)for(var s in i){var u=i[s];void 0===u||s in r||(u.d(),i[s]=void 0)}else n.U=i={};for(var c in r){var l=i[c],_=r[c];void 0===l?(l=Dt(n,c,_,o),i[c]=l):l.o(_,o)}}}t(e)})),At("unmount",(function(t,e){if("string"==typeof e.type){var n=e.__e;if(n){var r=n.U;if(r)for(var o in n.U=void 0,r){var i=r[o];i&&i.d()}}}else{var s=e.__c;if(s){var u=s.__$u;u&&(s.__$u=void 0,u.d())}}t(e)})),At("__h",(function(t,e,n,r){(r<3||9===r)&&(e.__$f|=2),t(e,n,r)})),x.prototype.shouldComponentUpdate=function(t,e){var n=this.__$u;if(!(n&&void 0!==n.s||4&this.__$f))return!0;if(3&this.__$f)return!0;for(var r in e)return!0;for(var o in t)if("__source"!==o&&t[o]!==this.props[o])return!0;for(var i in this.props)if(!(i in t))return!0;return!1};const Bt=[],Vt=()=>Bt.slice(-1)[0],zt=t=>{Bt.push(t)},Jt=()=>{Bt.pop()},Kt=[],qt=()=>Kt.slice(-1)[0],Qt=t=>{Kt.push(t)},Xt=()=>{Kt.pop()},Yt=new WeakMap,Zt=()=>{throw new Error("Please use `data-wp-bind` to modify the attributes of an element.")},te={get(t,e,n){const r=Reflect.get(t,e,n);return r&&"object"==typeof r?ee(r):r},set:Zt,deleteProperty:Zt},ee=t=>(Yt.has(t)||Yt.set(t,new Proxy(t,te)),Yt.get(t)),ne=t=>qt().context[t||Vt()],re=()=>{const t=qt(),{ref:e,attributes:n}=t;return Object.freeze({ref:e.current,attributes:ee(n)})},oe=t=>qt().serverContext[t||Vt()],ie=t=>new Promise((e=>{const n=()=>{clearTimeout(r),window.cancelAnimationFrame(o),setTimeout((()=>{t(),e()}))},r=setTimeout(n,100),o=window.requestAnimationFrame(n)})),se="function"==typeof window.scheduler?.yield?window.scheduler.yield.bind(window.scheduler):()=>new Promise((t=>{setTimeout(t,0)}));function ue(t){const e=qt(),n=Vt();let r;if(r="GeneratorFunction"===t?.constructor?.name?async(...r)=>{const o=t(...r);let i,s,u;for(;;){zt(n),Qt(e);try{s=u?o.throw(u):o.next(i),u=void 0}catch(t){throw t}finally{Xt(),Jt()}try{i=await s.value}catch(t){u=t}if(s.done){if(u)throw u;break}}return i}:(...r)=>{zt(n),Qt(e);try{return t(...r)}finally{Jt(),Xt()}},t.sync){const t=r;return t.sync=!0,t}return r}function ce(t){!function(t){st((()=>{let e=null,n=!1;return e=function(t,e){let n=()=>{};const r=Rt((function(){return n=this.c.bind(this),this.x=t,this.c=e,t()}));return{flush:n,dispose:r}}(t,(async()=>{e&&!n&&(n=!0,await ie(e.flush),n=!1)})),e.dispose}),[])}(ue(t))}function le(t){st(ue(t),[])}function _e(t,e){st(ue(t),e)}function ae(t,e){!function(t,e){var n=ot(V++,4);!X.__s&&dt(n.__H,e)&&(n.__=t,n.i=e,z.__h.push(n))}(ue(t),e)}function fe(t,e){return lt(ue(t),e)}function pe(t,e){return ct(ue(t),e)}new Set;const he=t=>Boolean(t&&"object"==typeof t&&t.constructor===Object);function ve(t){const e=t;return e.sync=!0,e}const de=new WeakMap,ye=new WeakMap,ge=new WeakMap,me=new Set([Object,Array]),we=(t,e,n)=>{if(!ke(e))throw Error("This object cannot be proxified.");if(!de.has(e)){const r=new Proxy(e,n);de.set(e,r),ye.set(r,e),ge.set(r,t)}return de.get(e)},be=t=>de.get(t),Se=t=>ge.get(t),ke=t=>"object"==typeof t&&null!==t&&!ge.has(t)&&me.has(t.constructor),xe={};class Ee{constructor(t){this.owner=t,this.computedsByScope=new WeakMap}setValue(t){this.update({value:t})}setGetter(t){this.update({get:t})}getComputed(){const t=qt()||xe;if(this.valueSignal||this.getterSignal||this.update({}),!this.computedsByScope.has(t)){const e=()=>{const t=this.getterSignal?.value;return t?t.call(this.owner):this.valueSignal?.value};zt(Se(this.owner)),this.computedsByScope.set(t,jt(ue(e))),Jt()}return this.computedsByScope.get(t)}update({get:t,value:e}){this.valueSignal?e===this.valueSignal.peek()&&t===this.getterSignal.peek()||wt((()=>{this.valueSignal.value=e,this.getterSignal.value=t})):(this.valueSignal=Nt(e),this.getterSignal=Nt(t))}}const Pe=new Set(Object.getOwnPropertyNames(Symbol).map((t=>Symbol[t])).filter((t=>"symbol"==typeof t))),Te=new WeakMap,Ce=(t,e)=>Te.has(t)&&Te.get(t).has(e),Ne=new WeakSet,Ue=(t,e,n)=>{Te.has(t)||Te.set(t,new Map),e="number"==typeof e?`${e}`:e;const r=Te.get(t);if(!r.has(e)){const o=Se(t),i=new Ee(t);if(r.set(e,i),n){const{get:e,value:r}=n;if(e)i.setGetter(e);else{const e=Ne.has(t);i.setValue(ke(r)?Me(o,r,{readOnly:e}):r)}}}return r.get(e)},Oe=new WeakMap;const $e={get(t,e,n){if(!t.hasOwnProperty(e)&&e in t||"symbol"==typeof e&&Pe.has(e))return Reflect.get(t,e,n);const r=Object.getOwnPropertyDescriptor(t,e),o=Ue(n,e,r).getComputed().value;if("function"==typeof o){const t=Se(n);return(...e)=>{zt(t);try{return o.call(n,...e)}finally{Jt()}}}return o},set(t,e,n,r){if(Ne.has(r))return!1;zt(Se(r));try{return Reflect.set(t,e,n,r)}finally{Jt()}},defineProperty(t,e,n){if(Ne.has(be(t)))return!1;const r=!(e in t),o=Reflect.defineProperty(t,e,n);if(o){const o=be(t),i=Ue(o,e),{get:s,value:u}=n;if(s)i.setGetter(s);else{const t=Se(o);i.setValue(ke(u)?Me(t,u):u)}r&&Oe.has(t)&&Oe.get(t).value++,Array.isArray(t)&&Te.get(o)?.has("length")&&Ue(o,"length").setValue(t.length)}return o},deleteProperty(t,e){if(Ne.has(be(t)))return!1;const n=Reflect.deleteProperty(t,e);return n&&(Ue(be(t),e).setValue(void 0),Oe.has(t)&&Oe.get(t).value++),n},ownKeys:t=>(Oe.has(t)||Oe.set(t,Nt(0)),Oe._=Oe.get(t).value,Reflect.ownKeys(t))},Me=(t,e,n)=>{const r=we(t,e,$e);return n?.readOnly&&Ne.add(r),r},je=(t,e,n=!0)=>{if(!he(t)||!he(e))return;let r=!1;for(const o in e){const i=!(o in t);r=r||i;const s=Object.getOwnPropertyDescriptor(e,o),u=be(t),c=!!u&&Ce(u,o)&&Ue(u,o);if("function"==typeof s.get||"function"==typeof s.set)(n||i)&&(Object.defineProperty(t,o,{...s,configurable:!0,enumerable:!0}),s.get&&c&&c.setGetter(s.get));else if(he(e[o])){const r=Object.getOwnPropertyDescriptor(t,o)?.value;if(i||n&&!he(r)){if(t[o]={},c){const e=Se(u);c.setValue(Me(e,t[o]))}je(t[o],e[o],n)}else he(r)&&je(t[o],e[o],n)}else if((n||i)&&(Object.defineProperty(t,o,s),c)){const{value:t}=s,e=Se(u);c.setValue(ke(t)?Me(e,t):t)}}r&&Oe.has(t)&&Oe.get(t).value++},He=(t,e,n=!0)=>wt((()=>{return je((r=t,ye.get(r)||t),e,n);var r})),We=new WeakSet,Le={get:(t,e,n)=>{const r=Reflect.get(t,e),o=Se(n);if(void 0===r&&We.has(n)){const n={};return Reflect.set(t,e,n),Ie(o,n,!1)}if("function"==typeof r){zt(o);const t=ue(r);return Jt(),t}return he(r)&&ke(r)?Ie(o,r,!1):r}},Ie=(t,e,n=!0)=>{const r=we(t,e,Le);return r&&n&&We.add(r),r},Re=new WeakMap,Ae=new WeakMap,Fe=new WeakSet,Ge=Reflect.getOwnPropertyDescriptor,De={get:(t,e)=>{const n=Ae.get(t),r=t[e];return e in t?r:n[e]},set:(t,e,n)=>{const r=Ae.get(t);return(e in t||!(e in r)?t:r)[e]=n,!0},ownKeys:t=>[...new Set([...Object.keys(Ae.get(t)),...Object.keys(t)])],getOwnPropertyDescriptor:(t,e)=>Ge(t,e)||Ge(Ae.get(t),e),has:(t,e)=>Reflect.has(t,e)||Reflect.has(Ae.get(t),e)},Be=(t,e={})=>{if(Fe.has(t))throw Error("This object cannot be proxified.");if(Ae.set(t,e),!Re.has(t)){const e=new Proxy(t,De);Re.set(t,e),Fe.add(e)}return Re.get(t)},Ve=new Map,ze=new Map,Je=new Map,Ke=new Map,qe=new Map,Qe=t=>Ke.get(t||Vt())||{},Xe=t=>{const e=t||Vt();return qe.has(e)||qe.set(e,Me(e,{},{readOnly:!0})),qe.get(e)},Ye="I acknowledge that using a private store means my plugin will inevitably break on the next store release.";function Ze(t,{state:e={},...n}={},{lock:r=!1}={}){if(Ve.has(t)){if(r===Ye||Je.has(t)){const e=Je.get(t);if(r!==Ye&&(!0===r||r!==e))throw e?Error("Cannot unlock a private store with an invalid lock code"):Error("Cannot lock a public store")}else Je.set(t,r);const o=ze.get(t);He(o,n),He(o.state,e)}else{r!==Ye&&Je.set(t,r);const o={state:Me(t,he(e)?e:{}),...n},i=Ie(t,o);ze.set(t,o),Ve.set(t,i)}return Ve.get(t)}const tn=(t=document)=>{var e;const n=null!==(e=t.getElementById("wp-script-module-data-@wordpress/interactivity"))&&void 0!==e?e:t.getElementById("wp-interactivity-data");if(n?.textContent)try{return JSON.parse(n.textContent)}catch{}return{}},en=t=>{he(t?.state)&&Object.entries(t.state).forEach((([t,e])=>{const n=Ze(t,{},{lock:Ye});He(n.state,e,!1),He(Xe(t),e)})),he(t?.config)&&Object.entries(t.config).forEach((([t,e])=>{Ke.set(t,e)}))},nn=tn();function rn(t){return null!==t.suffix}function on(t){return null===t.suffix}en(nn);const sn=function(t,e){var n={__c:e="__cC"+h++,__:{client:{},server:{}},Consumer:function(t,e){return t.children(e)},Provider:function(t){var n,r;return this.getChildContext||(n=new Set,(r={})[e]=this,this.getChildContext=function(){return r},this.componentWillUnmount=function(){n=null},this.shouldComponentUpdate=function(t){this.props.value!==t.value&&n.forEach((function(t){t.__e=!0,T(t)}))},this.sub=function(t){n.add(t);var e=t.componentWillUnmount;t.componentWillUnmount=function(){n&&n.delete(t),e&&e.call(t)}}),t.children}};return n.Provider.__=n.Consumer.contextType=n}(),un={},cn={},ln=(t,e,{priority:n=10}={})=>{un[t]=e,cn[t]=n},an=({scope:t})=>(e,...n)=>{let{value:r,namespace:o}=e;if("string"!=typeof r)throw new Error("The `value` prop should be a string path");const i="!"===r[0]&&!!(r=r.slice(1));Qt(t);const s=((t,e)=>{if(!e)return;let n=Ve.get(e);void 0===n&&(n=Ze(e,{},{lock:Ye}));const r={...n,context:qt().context[e]};try{return t.split(".").reduce(((t,e)=>t[e]),r)}catch(t){}})(r,o);if("function"==typeof s){if(i){const t=!s(...n);return Xt(),t}return Xt(),(...e)=>{Qt(t);const n=s(...e);return Xt(),n}}const u=s;return Xt(),i?!u:u},fn=({directives:t,priorityLevels:[e,...n],element:r,originalProps:o,previousScope:i})=>{const s=ut({}).current;s.evaluate=lt(an({scope:s}),[]);const{client:u,server:c}=_t(sn);s.context=u,s.serverContext=c,s.ref=i?.ref||ut(null),r=B(r,{ref:s.ref}),s.attributes=r.props;const l=n.length>0?b(fn,{directives:t,priorityLevels:n,element:r,originalProps:o,previousScope:s}):r,_={...o,children:l},a={directives:t,props:_,element:r,context:sn,evaluate:s.evaluate};Qt(s);for(const t of e){const e=un[t]?.(a);void 0!==e&&(_.children=e)}return Xt(),_.children},pn=r.vnode;function hn(t){return he(t)?Object.fromEntries(Object.entries(t).map((([t,e])=>[t,hn(e)]))):Array.isArray(t)?t.map((t=>hn(t))):t}function vn(t){return new Proxy(t,{get(t,e,n){const r=t[e];return r instanceof Function?function(...e){return r.apply(this===n?t:this,e)}:r}})}r.vnode=t=>{if(t.props.__directives){const e=t.props,n=e.__directives;n.key&&(t.key=n.key.find(on).value),delete e.__directives;const r=(t=>{const e=Object.keys(t).reduce(((t,e)=>{if(un[e]){const n=cn[e];(t[n]=t[n]||[]).push(e)}return t}),{});return Object.entries(e).sort((([t],[e])=>parseInt(t)-parseInt(e))).map((([,t])=>t))})(n);r.length>0&&(t.props={directives:n,priorityLevels:r,originalProps:e,type:t.type,element:b(t.type,e),top:!0},t.type=fn)}pn&&pn(t)};const dn=/(?:([\u0080-\uFFFF\w-%@]+) *:? *([^{;]+?);|([^;}{]*?) *{)|(}\s*)/g,yn=/\/\*[^]*?\*\/|  +/g,gn=/\n+/g,mn=t=>({directives:e,evaluate:n})=>{e[`on-${t}`].filter(rn).forEach((e=>{const r=e.suffix.split("--",1)[0];le((()=>{const o=t=>{const r=n(e);"function"==typeof r&&(r?.sync||(t=vn(t)),r(t))},i="window"===t?window:document;return i.addEventListener(r,o),()=>i.removeEventListener(r,o)}))}))},wn=t=>({directives:e,evaluate:n})=>{e[`on-async-${t}`].filter(rn).forEach((e=>{const r=e.suffix.split("--",1)[0];le((()=>{const o=async t=>{await se();const r=n(e);"function"==typeof r&&r(t)},i="window"===t?window:document;return i.addEventListener(r,o,{passive:!0}),()=>i.removeEventListener(r,o)}))}))},bn="wp",Sn=`data-${bn}-ignore`,kn=`data-${bn}-interactive`,xn=`data-${bn}-`,En=[],Pn=new RegExp(`^data-${bn}-([a-z0-9]+(?:-[a-z0-9]+)*)(?:--([a-z0-9_-]+))?$`,"i"),Tn=/^([\w_\/-]+)::(.+)$/,Cn=new WeakSet;function Nn(t){const e=new Set,n=new Set,r=document.createTreeWalker(t,205),o=function t(o){const{nodeType:i}=o;if(3===i)return o.data;if(4===i)return n.add(o),o.nodeValue;if(8===i||7===i)return e.add(o),null;const s=o,{attributes:u}=s,c=s.localName,l={},_=[],a=[];let f=!1,p=!1;for(let t=0;t<u.length;t++){const e=u[t].name,n=u[t].value;if(e[xn.length]&&e.slice(0,xn.length)===xn)if(e===Sn)f=!0;else{var h,v;const t=Tn.exec(n),r=null!==(h=t?.[1])&&void 0!==h?h:null;let o=null!==(v=t?.[2])&&void 0!==v?v:n;try{const t=JSON.parse(o);d=t,o=Boolean(d&&"object"==typeof d&&d.constructor===Object)?t:o}catch{}if(e===kn){p=!0;const t="string"==typeof o?o:"string"==typeof o?.namespace?o.namespace:null;En.push(t)}else a.push([e,r,o])}else if("ref"===e)continue;l[e]=n}var d;if(f&&!p)return[b(c,{...l,innerHTML:s.innerHTML,__directives:{ignore:!0}})];if(p&&Cn.add(s),a.length&&(l.__directives=a.reduce(((t,[e,n,r])=>{const o=Pn.exec(e);if(null===o)return t;const i=o[1]||"",s=o[2]||null;var u;return t[i]=t[i]||[],t[i].push({namespace:null!=n?n:null!==(u=En[En.length-1])&&void 0!==u?u:null,value:r,suffix:s}),t}),{})),"template"===c)l.content=[...s.content.childNodes].map((t=>Nn(t)));else{let e=r.firstChild();if(e){for(;e;){const n=t(e);n&&_.push(n),e=r.nextSibling()}r.parentNode()}}return p&&En.pop(),b(c,l,_)}(r.currentNode);return e.forEach((t=>t.remove())),n.forEach((t=>{var e;return t.replaceWith(new window.Text(null!==(e=t.nodeValue)&&void 0!==e?e:""))})),o}const Un=new WeakMap,On=t=>{if(!t.parentElement)throw Error("The passed region should be an element with a parent.");return Un.has(t)||Un.set(t,((t,e)=>{const n=(e=[].concat(e))[e.length-1].nextSibling;function r(e,r){t.insertBefore(e,r||n)}return t.__k={nodeType:1,parentNode:t,firstChild:e[0],childNodes:e,insertBefore:r,appendChild:r,removeChild(e){t.removeChild(e)}}})(t.parentElement,t)),Un.get(t)},$n=new WeakMap,Mn=t=>{if("I acknowledge that using private APIs means my theme or plugin will inevitably break in the next version of WordPress."===t)return{directivePrefix:bn,getRegionRootFragment:On,initialVdom:$n,toVdom:Nn,directive:ln,getNamespace:Vt,h:b,cloneElement:B,render:G,proxifyState:Me,parseServerData:tn,populateServerData:en,batch:wt};throw new Error("Forbidden access.")};ln("context",(({directives:{context:t},props:{children:e},context:n})=>{const{Provider:r}=n,o=t.find(on),{client:i,server:s}=_t(n),u=o.namespace,c=ut(Me(u,{})),l=ut(Me(u,{},{readOnly:!0}));return b(r,{value:ct((()=>{const t={client:{...i},server:{...s}};if(o){const{namespace:e,value:n}=o;he(n),He(c.current,hn(n),!1),He(l.current,hn(n)),t.client[e]=Be(c.current,i[e]),t.server[e]=Be(l.current,s[e])}return t}),[o,i,s])},e)}),{priority:5}),ln("watch",(({directives:{watch:t},evaluate:e})=>{t.forEach((t=>{ce((()=>{globalThis.IS_GUTENBERG_PLUGIN;let n=e(t);return"function"==typeof n&&(n=n()),globalThis.IS_GUTENBERG_PLUGIN,n}))}))})),ln("init",(({directives:{init:t},evaluate:e})=>{t.forEach((t=>{le((()=>{globalThis.IS_GUTENBERG_PLUGIN;let n=e(t);return"function"==typeof n&&(n=n()),globalThis.IS_GUTENBERG_PLUGIN,n}))}))})),ln("on",(({directives:{on:t},element:e,evaluate:n})=>{const r=new Map;t.filter(rn).forEach((t=>{const e=t.suffix.split("--")[0];r.has(e)||r.set(e,new Set),r.get(e).add(t)})),r.forEach(((t,r)=>{const o=e.props[`on${r}`];e.props[`on${r}`]=e=>{t.forEach((t=>{o&&o(e),globalThis.IS_GUTENBERG_PLUGIN;const r=n(t);"function"==typeof r&&(r?.sync||(e=vn(e)),r(e)),globalThis.IS_GUTENBERG_PLUGIN}))}}))})),ln("on-async",(({directives:{"on-async":t},element:e,evaluate:n})=>{const r=new Map;t.filter(rn).forEach((t=>{const e=t.suffix.split("--")[0];r.has(e)||r.set(e,new Set),r.get(e).add(t)})),r.forEach(((t,r)=>{const o=e.props[`on${r}`];e.props[`on${r}`]=e=>{o&&o(e),t.forEach((async t=>{await se();const r=n(t);"function"==typeof r&&r(e)}))}}))})),ln("on-window",mn("window")),ln("on-document",mn("document")),ln("on-async-window",wn("window")),ln("on-async-document",wn("document")),ln("class",(({directives:{class:t},element:e,evaluate:n})=>{t.filter(rn).forEach((t=>{const r=t.suffix;let o=n(t);"function"==typeof o&&(o=o());const i=e.props.class||"",s=new RegExp(`(^|\\s)${r}(\\s|$)`,"g");o?s.test(i)||(e.props.class=i?`${i} ${r}`:r):e.props.class=i.replace(s," ").trim(),le((()=>{o?e.ref.current.classList.add(r):e.ref.current.classList.remove(r)}))}))})),ln("style",(({directives:{style:t},element:e,evaluate:n})=>{t.filter(rn).forEach((t=>{const r=t.suffix;let o=n(t);"function"==typeof o&&(o=o()),e.props.style=e.props.style||{},"string"==typeof e.props.style&&(e.props.style=(t=>{const e=[{}];let n,r;for(;n=dn.exec(t.replace(yn,""));)n[4]?e.shift():n[3]?(r=n[3].replace(gn," ").trim(),e.unshift(e[0][r]=e[0][r]||{})):e[0][n[1]]=n[2].replace(gn," ").trim();return e[0]})(e.props.style)),o?e.props.style[r]=o:delete e.props.style[r],le((()=>{o?e.ref.current.style[r]=o:e.ref.current.style.removeProperty(r)}))}))})),ln("bind",(({directives:{bind:t},element:e,evaluate:n})=>{t.filter(rn).forEach((t=>{const r=t.suffix;let o=n(t);"function"==typeof o&&(o=o()),e.props[r]=o,le((()=>{const t=e.ref.current;if("style"!==r){if("width"!==r&&"height"!==r&&"href"!==r&&"list"!==r&&"form"!==r&&"tabIndex"!==r&&"download"!==r&&"rowSpan"!==r&&"colSpan"!==r&&"role"!==r&&r in t)try{return void(t[r]=null==o?"":o)}catch(t){}null==o||!1===o&&"-"!==r[4]?t.removeAttribute(r):t.setAttribute(r,o)}else"string"==typeof o&&(t.style.cssText=o)}))}))})),ln("ignore",(({element:{type:t,props:{innerHTML:e,...n}}})=>b(t,{dangerouslySetInnerHTML:{__html:ct((()=>e),[])},...n}))),ln("text",(({directives:{text:t},element:e,evaluate:n})=>{const r=t.find(on);if(r)try{let t=n(r);"function"==typeof t&&(t=t()),e.props.children="object"==typeof t?null:t.toString()}catch(t){e.props.children=null}else e.props.children=null})),ln("run",(({directives:{run:t},evaluate:e})=>{t.forEach((t=>{let n=e(t);return"function"==typeof n&&(n=n()),n}))})),ln("each",(({directives:{each:t,"each-key":e},context:n,element:r,evaluate:o})=>{if("template"!==r.type)return;const{Provider:i}=n,s=_t(n),[u]=t,{namespace:c}=u;let l=o(u);if("function"==typeof l&&(l=l()),"function"!=typeof l?.[Symbol.iterator])return;const _=rn(u)?u.suffix.replace(/^-+|-+$/g,"").toLowerCase().replace(/-([a-z])/g,(function(t,e){return e.toUpperCase()})):"item",a=[];for(const t of l){const n=Be(Me(c,{}),s.client[c]),o={client:{...s.client,[c]:n},server:{...s.server}};o.client[c][_]=t;const u={...qt(),context:o.client,serverContext:o.server},l=e?an({scope:u})(e[0]):t;a.push(b(i,{value:o,key:l},r.props.content))}return a}),{priority:20}),ln("each-child",(()=>null),{priority:1}),(async()=>{const t=document.querySelectorAll(`[data-${bn}-interactive]`);await new Promise((t=>{setTimeout(t,0)}));for(const e of t)if(!Cn.has(e)){await se();const t=On(e),n=Nn(e);$n.set(e,n),await se(),D(n,t)}})();var jn=e.zj,Hn=e.SD,Wn=e.V6,Ln=e.$K,In=e.vT,Rn=e.jb,An=e.yT,Fn=e.M_,Gn=e.hb,Dn=e.vJ,Bn=e.ip,Vn=e.Nf,zn=e.Kr,Jn=e.li,Kn=e.J0,qn=e.FH,Qn=e.v4,Xn=e.mh;export{jn as getConfig,Hn as getContext,Wn as getElement,Ln as getServerContext,In as getServerState,Rn as privateApis,An as splitTask,Fn as store,Gn as useCallback,Dn as useEffect,Bn as useInit,Vn as useLayoutEffect,zn as useMemo,Jn as useRef,Kn as useState,qn as useWatch,Qn as withScope,Xn as withSyncEvent};