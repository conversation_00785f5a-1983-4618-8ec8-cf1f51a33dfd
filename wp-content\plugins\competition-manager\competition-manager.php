<?php
/**
 * Plugin Name: Competition Manager
 * Plugin URI: https://yoursite.com
 * Description: Custom plugin for managing competitions, instant wins, and ticket sales for the React competition website.
 * Version: 1.0.0
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('COMPETITION_MANAGER_VERSION', '2.0.0');
define('COMPETITION_MANAGER_PLUGIN_DIR', plugin_dir_path(__FILE__));
define('COMPETITION_MANAGER_PLUGIN_URL', plugin_dir_url(__FILE__));

// Include required files
// Removed old instant win manager - functionality moved to main CompetitionManager class
if (file_exists(COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-winner-manager.php')) {
    require_once COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-winner-manager.php';
}
if (file_exists(COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-ajax-handlers.php')) {
    require_once COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-ajax-handlers.php';
}
if (file_exists(COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-woocommerce-integration.php')) {
    require_once COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-woocommerce-integration.php';
}
if (file_exists(COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-user-registration.php')) {
    require_once COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-user-registration.php';
}

// Main plugin class
class CompetitionManager {
    
    public function __construct() {
        add_action('init', array($this, 'init'));
        add_action('rest_api_init', array($this, 'register_rest_routes'));
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));

        // Add admin action to clean up custom fields
        add_action('wp_ajax_cleanup_competition_fields', array($this, 'ajax_cleanup_custom_fields'));

        // Initialize default questions if needed
        add_action('admin_init', array($this, 'maybe_initialize_questions'));
    }
    
    public function init() {
        // Include required files
        require_once COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-woocommerce-integration.php';
        require_once COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-user-registration.php';
        require_once COMPETITION_MANAGER_PLUGIN_DIR . 'includes/class-woocommerce-email-integration.php';

        // Include migration helper (admin only)
        if (is_admin()) {
            require_once COMPETITION_MANAGER_PLUGIN_DIR . 'migration-helper.php';
        }

        // Register post types for instant wins, questions, and winners
        $this->register_post_types();

        // Flush rewrite rules on activation
        register_activation_hook(__FILE__, array($this, 'flush_rewrite_rules_on_activation'));

        // Clear email cache on activation
        register_activation_hook(__FILE__, array($this, 'clear_email_cache_on_activation'));

        // Initialize integrations
        // Competition_WooCommerce_Integration disabled - main CompetitionManager handles all functionality
        new Competition_User_Registration();

        // Create competition product category
        add_action('init', array($this, 'create_competition_category'));

        // Add custom fields support
        add_theme_support('post-thumbnails');

        // Load admin scripts and styles
        add_action('admin_enqueue_scripts', array($this, 'admin_scripts'));

        // Add WooCommerce product meta boxes for competitions
        add_action('add_meta_boxes', array($this, 'add_competition_product_meta_boxes'));
        add_action('save_post', array($this, 'save_competition_product_meta_boxes'));

        // Add meta boxes for other post types
        add_action('add_meta_boxes', array($this, 'add_instant_win_meta_boxes'));
        add_action('add_meta_boxes', array($this, 'add_question_meta_boxes'));
        add_action('save_post', array($this, 'save_instant_win_meta_boxes'));
        add_action('save_post', array($this, 'save_question_meta_boxes'));

        // Add custom columns to WooCommerce products admin
        add_filter('manage_edit-product_columns', array($this, 'add_competition_product_columns'));
        add_action('manage_product_posts_custom_column', array($this, 'competition_product_column_content'), 10, 2);

        // Add custom admin menu
        add_action('admin_menu', array($this, 'add_competition_admin_menu'));

        // Add export functionality
        add_action('admin_init', array($this, 'handle_ticket_export'));
        add_action('admin_init', array($this, 'handle_bulk_ticket_export'));

        // AJAX handlers for cart functionality
        add_action('wp_ajax_competition_add_to_cart', array($this, 'ajax_add_to_cart'));
        add_action('wp_ajax_nopriv_competition_add_to_cart', array($this, 'ajax_add_to_cart'));

        // Admin action to create products for existing competitions
        add_action('wp_ajax_create_competition_products', array($this, 'ajax_create_products'));
        add_action('wp_ajax_hide_competition_products', array($this, 'ajax_hide_products'));

        // AJAX handlers for manual winner addition
        add_action('wp_ajax_get_ticket_user_details', array($this, 'get_ticket_user_details'));
        add_action('wp_ajax_add_manual_winner', array($this, 'add_manual_winner'));
        add_action('wp_ajax_debug_competition_database', array($this, 'debug_competition_database'));
        add_action('wp_ajax_flush_winner_rewrite_rules', array($this, 'flush_winner_rewrite_rules'));
        add_action('wp_ajax_test_winner_email', array($this, 'test_winner_email'));



        // Enqueue scripts for countdown timers
        add_action('wp_enqueue_scripts', array($this, 'enqueue_countdown_scripts'));

        // Hide competition products from WooCommerce
        add_filter('woocommerce_product_query', array($this, 'hide_competition_products_from_shop'));
        add_filter('woocommerce_shortcode_products_query', array($this, 'hide_competition_products_from_shortcodes'));
        add_action('template_redirect', array($this, 'prevent_direct_product_access'));
        add_action('pre_get_posts', array($this, 'hide_competition_products_everywhere'));

        // Order completion hooks for ticket generation
        add_action('woocommerce_order_status_completed', array($this, 'generate_tickets_on_order_completion'));
        add_action('woocommerce_order_status_processing', array($this, 'generate_tickets_on_order_completion'));
        add_action('woocommerce_payment_complete', array($this, 'generate_tickets_on_payment_complete'));

        // We use the dashboard for tickets display, no separate endpoint needed

        // Add tickets to order summary
        add_action('woocommerce_order_details_after_order_table', array($this, 'display_order_tickets'));
    }
    
    /**
     * Create competition product category
     */
    public function create_competition_category() {
        // Check if competitions category exists
        $competition_category = get_term_by('slug', 'competitions', 'product_cat');

        if (!$competition_category) {
            // Create the competitions category
            $result = wp_insert_term(
                'Competitions',
                'product_cat',
                array(
                    'slug' => 'competitions',
                    'description' => 'Competition entry tickets and prizes'
                )
            );

            if (!is_wp_error($result)) {
                // Set category meta for identification
                update_term_meta($result['term_id'], '_is_competition_category', 'yes');
            }
        }
    }

    /**
     * Register post types for competition management
     */
    public function register_post_types() {
        // Instant Win post type
        register_post_type('instant_win', array(
            'labels' => array(
                'name' => 'Instant Wins',
                'singular_name' => 'Instant Win',
                'add_new' => 'Add New Instant Win',
                'add_new_item' => 'Add New Instant Win',
                'edit_item' => 'Edit Instant Win',
                'new_item' => 'New Instant Win',
                'view_item' => 'View Instant Win',
                'search_items' => 'Search Instant Wins',
                'not_found' => 'No instant wins found',
                'not_found_in_trash' => 'No instant wins found in trash'
            ),
            'public' => false,
            'show_ui' => true,
            'supports' => array('title', 'editor', 'custom-fields'),
            'show_in_rest' => false,
            'capability_type' => 'post',
            'hierarchical' => false,
            'show_in_menu' => false, // Hide from main menu, will show in our custom menu
        ));

        // Competition Questions post type
        register_post_type('competition_question', array(
            'labels' => array(
                'name' => 'Competition Questions',
                'singular_name' => 'Competition Question',
                'add_new' => 'Add New Question',
                'add_new_item' => 'Add New Question',
                'edit_item' => 'Edit Question',
                'new_item' => 'New Question',
                'view_item' => 'View Question',
                'search_items' => 'Search Questions',
                'not_found' => 'No questions found',
                'not_found_in_trash' => 'No questions found in trash'
            ),
            'public' => false,
            'show_ui' => true,
            'supports' => array('title', 'editor', 'custom-fields'),
            'show_in_rest' => false,
            'capability_type' => 'post',
            'hierarchical' => false,
            'show_in_menu' => false, // Hide from main menu, will show in our custom menu
        ));

        // Winner post type (keep this for winner announcements)
        register_post_type('winner', array(
            'labels' => array(
                'name' => 'Winners',
                'singular_name' => 'Winner',
                'add_new' => 'Add New Winner',
                'add_new_item' => 'Add New Winner',
                'edit_item' => 'Edit Winner',
                'new_item' => 'New Winner',
                'view_item' => 'View Winner',
                'search_items' => 'Search Winners',
                'not_found' => 'No winners found',
                'not_found_in_trash' => 'No winners found in trash'
            ),
            'public' => true,
            'has_archive' => true,
            'supports' => array('title', 'editor', 'thumbnail', 'custom-fields'),
            'show_in_rest' => true,
            'rest_base' => 'winners',
            'capability_type' => 'post',
            'hierarchical' => false,
            'rewrite' => array('slug' => 'winners'),
            'show_in_menu' => false, // Hide from main menu, will show in our custom menu
        ));
    }

    /**
     * Add competition meta boxes to WooCommerce products
     */
    public function add_competition_product_meta_boxes() {
        add_meta_box(
            'competition_details',
            'Competition Settings',
            array($this, 'competition_product_meta_box'),
            'product',
            'normal',
            'high'
        );
    }

    /**
     * Competition product meta box content
     */
    public function competition_product_meta_box($post) {
        wp_nonce_field('competition_product_meta_box', 'competition_product_meta_box_nonce');

        // Get current values
        $is_competition = get_post_meta($post->ID, '_is_competition', true);
        $start_date = get_post_meta($post->ID, '_competition_start_date', true);
        $end_date = get_post_meta($post->ID, '_competition_end_date', true);
        $max_tickets = get_post_meta($post->ID, '_competition_max_tickets', true);
        $sold_tickets = get_post_meta($post->ID, '_competition_sold_tickets', true) ?: 0;
        $question_set = get_post_meta($post->ID, '_competition_question_set', true);

        // Get predefined questions
        $predefined_questions = get_option('competition_predefined_questions', array());

        // Instant wins data
        $instant_wins_enabled = get_post_meta($post->ID, '_instant_wins_enabled', true);
        $instant_win_prizes = get_post_meta($post->ID, '_instant_win_prizes', true) ?: array();

        ?>
        <table class="form-table">
            <tr>
                <th><label for="is_competition">Is Competition Product</label></th>
                <td>
                    <input type="checkbox" id="is_competition" name="is_competition" value="yes" <?php checked($is_competition, 'yes'); ?> />
                    <span class="description">Check this to enable competition features for this product</span>
                </td>
            </tr>
            <tr class="competition-field" style="<?php echo $is_competition !== 'yes' ? 'display:none;' : ''; ?>">
                <th><label for="competition_start_date">Start Date & Time</label></th>
                <td>
                    <input type="datetime-local" id="competition_start_date" name="competition_start_date" value="<?php echo esc_attr($start_date ? date('Y-m-d\TH:i', strtotime($start_date)) : ''); ?>" />
                    <span class="description">When the competition becomes available for entry (leave blank to start immediately)</span>
                </td>
            </tr>
            <tr class="competition-field" style="<?php echo $is_competition !== 'yes' ? 'display:none;' : ''; ?>">
                <th><label for="competition_end_date">End Date & Time</label></th>
                <td>
                    <input type="datetime-local" id="competition_end_date" name="competition_end_date" value="<?php echo esc_attr($end_date ? date('Y-m-d\TH:i', strtotime($end_date)) : ''); ?>" />
                    <span class="description">When the competition ends</span>
                </td>
            </tr>
            <tr class="competition-field" style="<?php echo $is_competition !== 'yes' ? 'display:none;' : ''; ?>">
                <th><label for="competition_max_tickets">Maximum Tickets</label></th>
                <td>
                    <input type="number" id="competition_max_tickets" name="competition_max_tickets" value="<?php echo esc_attr($max_tickets); ?>" min="1" />
                    <span class="description">Total number of tickets available</span>
                </td>
            </tr>
            <tr class="competition-field" style="<?php echo $is_competition !== 'yes' ? 'display:none;' : ''; ?>">
                <th><label>Tickets Sold</label></th>
                <td>
                    <strong><?php echo esc_html($sold_tickets); ?></strong> / <?php echo esc_html($max_tickets ?: '0'); ?>
                    <span class="description">Current ticket sales</span>
                </td>
            </tr>
            <tr class="competition-field" style="<?php echo $is_competition !== 'yes' ? 'display:none;' : ''; ?>">
                <th><label for="competition_question_set">Skill Question</label></th>
                <td>
                    <select id="competition_question_set" name="competition_question_set" style="width: 100%;">
                        <option value="">Random Question (Auto-assigned)</option>
                        <?php if (!empty($predefined_questions)): ?>
                            <?php foreach ($predefined_questions as $id => $question_data): ?>
                                <option value="<?php echo esc_attr($id); ?>" <?php selected($question_set, $id); ?>>
                                    <?php echo esc_html(substr($question_data['question'], 0, 80) . (strlen($question_data['question']) > 80 ? '...' : '')); ?>
                                </option>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </select>
                    <span class="description">Select a predefined question or leave blank for random assignment. <a href="<?php echo admin_url('edit.php?post_type=competition_question'); ?>" target="_blank">Manage Questions</a></span>

                    <?php if (!empty($predefined_questions) && $question_set && isset($predefined_questions[$question_set])): ?>
                        <div id="question-preview" style="margin-top: 10px; padding: 10px; background: #f9f9f9; border-radius: 4px;">
                            <strong>Selected Question:</strong><br>
                            <?php echo esc_html($predefined_questions[$question_set]['question']); ?><br><br>
                            <strong>Answers:</strong><br>
                            A) <?php echo esc_html($predefined_questions[$question_set]['answer_a']); ?><br>
                            B) <?php echo esc_html($predefined_questions[$question_set]['answer_b']); ?><br>
                            C) <?php echo esc_html($predefined_questions[$question_set]['answer_c']); ?><br>
                            <em>Correct: <?php echo chr(64 + $predefined_questions[$question_set]['correct_answer']); ?>)</em>
                        </div>
                    <?php endif; ?>
                </td>
            </tr>
            <tr class="competition-field" style="<?php echo $is_competition !== 'yes' ? 'display:none;' : ''; ?>">
                <th><label>Instant Wins (Optional)</label></th>
                <td>
                    <div style="margin-bottom: 15px;">
                        <label>
                            <input type="checkbox" name="instant_wins_enabled" value="1" <?php checked($instant_wins_enabled, '1'); ?> />
                            <strong>Enable Instant Wins for this competition</strong>
                        </label>
                        <p class="description">Allow users to win instant prizes on specific ticket numbers</p>
                    </div>

                    <div id="instant-win-prizes" style="<?php echo !$instant_wins_enabled ? 'display:none;' : ''; ?>">
                        <?php if (!empty($instant_win_prizes)): ?>
                            <?php foreach ($instant_win_prizes as $index => $prize): ?>
                                <div class="instant-win-prize" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 4px; background: #f9f9f9;">
                                    <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                                        <input type="text" name="instant_win_prizes[<?php echo $index; ?>][name]" value="<?php echo esc_attr($prize['name'] ?? ''); ?>" placeholder="Prize Name (e.g., £10 Amazon Voucher)" style="width: 100%;" />
                                        <input type="number" name="instant_win_prizes[<?php echo $index; ?>][value]" value="<?php echo esc_attr($prize['value'] ?? ''); ?>" placeholder="Prize Value (£)" step="0.01" style="width: 100%;" />
                                    </div>
                                    <div style="margin-bottom: 10px;">
                                        <select name="instant_win_prizes[<?php echo $index; ?>][type]" style="width: 100%;">
                                            <option value="site_credit" <?php selected($prize['type'] ?? 'site_credit', 'site_credit'); ?>>Site Credit</option>
                                            <option value="physical" <?php selected($prize['type'] ?? 'site_credit', 'physical'); ?>>Physical Prize</option>
                                        </select>
                                    </div>
                                    <div style="margin-bottom: 10px;">
                                        <input type="number" name="instant_win_prizes[<?php echo $index; ?>][quantity]" value="<?php echo esc_attr($prize['quantity'] ?? 1); ?>" placeholder="Quantity" min="1" style="width: 100%;" />
                                        <small style="color: #666;">Number of this prize available</small>
                                    </div>
                                    <div style="margin-bottom: 10px;">
                                        <input type="text" name="instant_win_prizes[<?php echo $index; ?>][ticket_numbers]" value="<?php echo esc_attr(is_array($prize['ticket_numbers'] ?? '') ? implode(',', $prize['ticket_numbers']) : ''); ?>" placeholder="Auto-assigned when competition is published" style="width: 100%; background: #f0f0f0;" readonly />
                                        <small style="color: #666;">Ticket numbers are automatically assigned randomly when the competition is published</small>
                                    </div>
                                    <button type="button" class="remove-prize" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Remove Prize</button>
                                </div>
                            <?php endforeach; ?>
                        <?php endif; ?>
                    </div>

                    <div style="<?php echo !$instant_wins_enabled ? 'display:none;' : ''; ?>">
                        <button type="button" id="add-instant-win-prize" class="button" style="margin-top: 10px;">Add Instant Win Prize</button>
                        <p class="description">Add prizes that can be won instantly on specific ticket numbers</p>
                    </div>
                </td>
            </tr>
        </table>

        <script>
        jQuery(document).ready(function($) {
            $('#is_competition').change(function() {
                if ($(this).is(':checked')) {
                    $('.competition-field').show();
                } else {
                    $('.competition-field').hide();
                }
            });

            // Toggle instant wins section
            $('input[name="instant_wins_enabled"]').change(function() {
                if ($(this).is(':checked')) {
                    $('#instant-win-prizes').show();
                    $(this).closest('td').find('div').last().show();
                } else {
                    $('#instant-win-prizes').hide();
                    $(this).closest('td').find('div').last().hide();
                }
            });

            // Question preview functionality
            $('#competition_question_set').change(function() {
                var questionId = $(this).val();
                if (questionId) {
                    // Show loading or fetch question details via AJAX if needed
                    // For now, we'll reload the page to show the preview
                    // In a production environment, you might want to use AJAX
                } else {
                    $('#question-preview').hide();
                }
            });

            // Add new prize
            $('#add-instant-win-prize').click(function() {
                var index = $('.instant-win-prize').length;
                var prizeHtml = '<div class="instant-win-prize" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 4px; background: #f9f9f9;">' +
                    '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">' +
                    '<input type="text" name="instant_win_prizes[' + index + '][name]" placeholder="Prize Name (e.g., £10 Amazon Voucher)" style="width: 100%;" />' +
                    '<input type="number" name="instant_win_prizes[' + index + '][value]" placeholder="Prize Value (£)" step="0.01" style="width: 100%;" />' +
                    '</div>' +
                    '<div style="margin-bottom: 10px;">' +
                    '<select name="instant_win_prizes[' + index + '][type]" style="width: 100%;">' +
                    '<option value="site_credit">Site Credit</option>' +
                    '<option value="physical">Physical Prize</option>' +
                    '</select>' +
                    '</div>' +
                    '<div style="margin-bottom: 10px;">' +
                    '<input type="number" name="instant_win_prizes[' + index + '][quantity]" placeholder="Quantity" min="1" value="1" style="width: 100%;" />' +
                    '<small style="color: #666;">Number of this prize available</small>' +
                    '</div>' +
                    '<div style="margin-bottom: 10px;">' +
                    '<input type="text" name="instant_win_prizes[' + index + '][ticket_numbers]" placeholder="Auto-assigned when competition is published" style="width: 100%; background: #f0f0f0;" readonly />' +
                    '<small style="color: #666;">Ticket numbers are automatically assigned randomly when the competition is published</small>' +
                    '</div>' +
                    '<button type="button" class="remove-prize" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Remove Prize</button>' +
                    '</div>';
                $('#instant-win-prizes').append(prizeHtml);
            });

            // Remove prize
            $(document).on('click', '.remove-prize', function() {
                $(this).closest('.instant-win-prize').remove();
            });
        });
        </script>
        <?php
    }

    /**
     * Save competition product meta box data
     */
    public function save_competition_product_meta_boxes($post_id) {
        // Check nonce
        if (!isset($_POST['competition_product_meta_box_nonce']) || !wp_verify_nonce($_POST['competition_product_meta_box_nonce'], 'competition_product_meta_box')) {
            return;
        }

        // Check permissions
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check if this is a product
        if (get_post_type($post_id) !== 'product') {
            return;
        }

        // Save competition settings
        $is_competition = isset($_POST['is_competition']) ? 'yes' : 'no';
        update_post_meta($post_id, '_is_competition', $is_competition);

        if ($is_competition === 'yes') {
            // Save competition-specific fields
            if (isset($_POST['competition_start_date'])) {
                update_post_meta($post_id, '_competition_start_date', sanitize_text_field($_POST['competition_start_date']));
            }

            if (isset($_POST['competition_end_date'])) {
                update_post_meta($post_id, '_competition_end_date', sanitize_text_field($_POST['competition_end_date']));
            }

            if (isset($_POST['competition_max_tickets'])) {
                $max_tickets = intval($_POST['competition_max_tickets']);
                update_post_meta($post_id, '_competition_max_tickets', $max_tickets);

                // Update WooCommerce stock quantity to match max tickets
                $sold_tickets = intval(get_post_meta($post_id, '_competition_sold_tickets', true));
                $available_tickets = $max_tickets - $sold_tickets;
                update_post_meta($post_id, '_stock', $available_tickets);
                update_post_meta($post_id, '_manage_stock', 'yes');
            }

            // Handle question set
            if (isset($_POST['competition_question_set'])) {
                $question_set = sanitize_text_field($_POST['competition_question_set']);
                if (!empty($question_set)) {
                    update_post_meta($post_id, '_competition_question_set', $question_set);

                    // Also save the actual question data for easy access
                    $predefined_questions = get_option('competition_predefined_questions', array());
                    if (isset($predefined_questions[$question_set])) {
                        $question_data = $predefined_questions[$question_set];
                        update_post_meta($post_id, '_competition_question', $question_data['question']);
                        update_post_meta($post_id, '_competition_answer_1', $question_data['answer_a']);
                        update_post_meta($post_id, '_competition_answer_2', $question_data['answer_b']);
                        update_post_meta($post_id, '_competition_answer_3', $question_data['answer_c']);
                        update_post_meta($post_id, '_competition_correct_answer', $question_data['correct_answer']);
                    }
                } else {
                    // If question set is empty, assign a random one
                    $predefined_questions = get_option('competition_predefined_questions', array());
                    if (!empty($predefined_questions)) {
                        $random_question_id = array_rand($predefined_questions);
                        update_post_meta($post_id, '_competition_question_set', $random_question_id);

                        // Save the random question data
                        $question_data = $predefined_questions[$random_question_id];
                        update_post_meta($post_id, '_competition_question', $question_data['question']);
                        update_post_meta($post_id, '_competition_answer_1', $question_data['answer_a']);
                        update_post_meta($post_id, '_competition_answer_2', $question_data['answer_b']);
                        update_post_meta($post_id, '_competition_answer_3', $question_data['answer_c']);
                        update_post_meta($post_id, '_competition_correct_answer', $question_data['correct_answer']);
                    } else {
                        delete_post_meta($post_id, '_competition_question_set');
                    }
                }
            }

            // Save instant wins settings
            $instant_wins_enabled = isset($_POST['instant_wins_enabled']) ? '1' : '0';
            update_post_meta($post_id, '_instant_wins_enabled', $instant_wins_enabled);

            if ($instant_wins_enabled && isset($_POST['instant_win_prizes']) && is_array($_POST['instant_win_prizes'])) {
                $instant_win_prizes = array();
                $max_tickets = intval($_POST['competition_max_tickets'] ?? 100);

                foreach ($_POST['instant_win_prizes'] as $prize_data) {
                    if (!empty($prize_data['name']) && !empty($prize_data['value'])) {
                        $quantity = intval($prize_data['quantity'] ?? 1);

                        // Auto-assign random ticket numbers if not already assigned
                        $ticket_numbers = array();
                        if (empty($prize_data['ticket_numbers'])) {
                            // Generate random ticket numbers for this prize
                            for ($i = 0; $i < $quantity; $i++) {
                                $ticket_numbers[] = rand(1, $max_tickets);
                            }
                        } else {
                            // Parse existing ticket numbers
                            $ticket_numbers = array_map('intval', explode(',', $prize_data['ticket_numbers']));
                        }

                        $instant_win_prizes[] = array(
                            'name' => sanitize_text_field($prize_data['name']),
                            'value' => floatval($prize_data['value']),
                            'type' => sanitize_text_field($prize_data['type'] ?? 'site_credit'),
                            'quantity' => $quantity,
                            'ticket_numbers' => $ticket_numbers
                        );
                    }
                }

                update_post_meta($post_id, '_instant_win_prizes', $instant_win_prizes);
            } else {
                // Clear instant win prizes if disabled
                delete_post_meta($post_id, '_instant_win_prizes');
            }

            // Set product as virtual (tickets are virtual)
            update_post_meta($post_id, '_virtual', 'yes');

            // Add to competitions category
            $competition_category = get_term_by('slug', 'competitions', 'product_cat');
            if ($competition_category) {
                wp_set_post_terms($post_id, array($competition_category->term_id), 'product_cat', true);
            }
        }
    }

    /**
     * Add custom columns to WooCommerce products admin for competitions
     */
    public function add_competition_product_columns($columns) {
        $new_columns = array();

        foreach ($columns as $key => $column) {
            $new_columns[$key] = $column;

            // Add competition columns after the title
            if ($key === 'name') {
                $new_columns['is_competition'] = 'Competition';
                $new_columns['competition_status'] = 'Status';
                $new_columns['competition_tickets'] = 'Tickets';
                $new_columns['competition_end_date'] = 'End Date';
                $new_columns['competition_export'] = 'Export Tickets';
            }
        }

        return $new_columns;
    }

    /**
     * Display competition column content
     */
    public function competition_product_column_content($column, $post_id) {
        switch ($column) {
            case 'is_competition':
                $is_competition = get_post_meta($post_id, '_is_competition', true);
                echo $is_competition === 'yes' ? '✅ Yes' : '❌ No';
                break;

            case 'competition_status':
                if (get_post_meta($post_id, '_is_competition', true) === 'yes') {
                    $status = $this->get_competition_status($post_id);
                    switch ($status) {
                        case 'scheduled':
                            $start_date = get_post_meta($post_id, '_competition_start_date', true);
                            echo '<span style="color: #3b82f6; font-weight: bold;">📅 Scheduled</span><br>';
                            echo '<small style="color: #666;">Starts: ' . date('M j, Y H:i', strtotime($start_date)) . '</small>';
                            break;
                        case 'active':
                            echo '<span style="color: #10b981; font-weight: bold;">🟢 Active</span>';
                            break;
                        case 'awaiting_draw':
                            echo '<span style="color: #f59e0b; font-weight: bold;">⏳ Awaiting Draw</span>';
                            break;
                        case 'completed':
                            echo '<span style="color: #8b5cf6; font-weight: bold;">✅ Completed</span>';
                            break;
                        case 'ended':
                            echo '<span style="color: #ef4444; font-weight: bold;">🔴 Ended</span>';
                            break;
                        default:
                            echo '<span style="color: #6b7280;">—</span>';
                    }
                } else {
                    echo '—';
                }
                break;

            case 'competition_tickets':
                if (get_post_meta($post_id, '_is_competition', true) === 'yes') {
                    $sold = get_post_meta($post_id, '_competition_sold_tickets', true) ?: 0;
                    $max = get_post_meta($post_id, '_competition_max_tickets', true) ?: 0;
                    echo esc_html($sold . ' / ' . $max);
                } else {
                    echo '—';
                }
                break;

            case 'competition_end_date':
                if (get_post_meta($post_id, '_is_competition', true) === 'yes') {
                    $end_date = get_post_meta($post_id, '_competition_end_date', true);
                    if ($end_date) {
                        echo esc_html(date('Y-m-d H:i', strtotime($end_date)));
                    } else {
                        echo '—';
                    }
                } else {
                    echo '—';
                }
                break;

            case 'competition_export':
                if (get_post_meta($post_id, '_is_competition', true) === 'yes') {
                    global $wpdb;
                    $tickets_table = $wpdb->prefix . 'competition_tickets';
                    $ticket_count = $wpdb->get_var($wpdb->prepare(
                        "SELECT COUNT(*) FROM $tickets_table WHERE competition_id = %d",
                        $post_id
                    ));

                    if ($ticket_count > 0) {
                        $export_url = wp_nonce_url(
                            admin_url('admin.php?action=export_competition_tickets&competition_id=' . $post_id),
                            'export_tickets_' . $post_id
                        );
                        echo '<a href="' . esc_url($export_url) . '" class="button button-small">📥 Export (' . $ticket_count . ')</a>';
                    } else {
                        echo '<span style="color: #666;">No tickets</span>';
                    }
                } else {
                    echo '—';
                }
                break;
        }
    }

    /**
     * Add instant win meta boxes
     */
    public function add_instant_win_meta_boxes() {
        add_meta_box(
            'instant_win_details',
            'Instant Win Details',
            array($this, 'instant_win_meta_box'),
            'instant_win',
            'normal',
            'high'
        );
    }

    /**
     * Instant win meta box content
     */
    public function instant_win_meta_box($post) {
        wp_nonce_field('instant_win_meta_box', 'instant_win_meta_box_nonce');

        $prize_type = get_post_meta($post->ID, '_prize_type', true) ?: 'credit';
        $prize_value = get_post_meta($post->ID, '_prize_value', true);
        $prize_description = get_post_meta($post->ID, '_prize_description', true);
        $competition_id = get_post_meta($post->ID, '_competition_id', true);
        $ticket_numbers = get_post_meta($post->ID, '_winning_ticket_numbers', true);

        ?>
        <table class="form-table">
            <tr>
                <th><label for="prize_type">Prize Type</label></th>
                <td>
                    <select id="prize_type" name="prize_type">
                        <option value="credit" <?php selected($prize_type, 'credit'); ?>>Site Credit</option>
                        <option value="physical" <?php selected($prize_type, 'physical'); ?>>Physical Prize</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th><label for="prize_value">Prize Value (£)</label></th>
                <td>
                    <input type="number" id="prize_value" name="prize_value" value="<?php echo esc_attr($prize_value); ?>" step="0.01" min="0" />
                </td>
            </tr>
            <tr>
                <th><label for="prize_description">Prize Description</label></th>
                <td>
                    <textarea id="prize_description" name="prize_description" rows="3" cols="50"><?php echo esc_textarea($prize_description); ?></textarea>
                </td>
            </tr>
            <tr>
                <th><label for="competition_id">Competition Product ID</label></th>
                <td>
                    <input type="number" id="competition_id" name="competition_id" value="<?php echo esc_attr($competition_id); ?>" />
                    <p class="description">Enter the WooCommerce product ID of the competition this instant win belongs to</p>
                </td>
            </tr>
            <tr>
                <th><label for="winning_ticket_numbers">Winning Ticket Numbers</label></th>
                <td>
                    <textarea id="winning_ticket_numbers" name="winning_ticket_numbers" rows="3" cols="50" placeholder="1,5,10,25,50"><?php echo esc_textarea($ticket_numbers); ?></textarea>
                    <p class="description">Comma-separated list of ticket numbers that will win this prize</p>
                </td>
            </tr>
        </table>
        <?php
    }

    /**
     * Save instant win meta box data
     */
    public function save_instant_win_meta_boxes($post_id) {
        if (!isset($_POST['instant_win_meta_box_nonce']) || !wp_verify_nonce($_POST['instant_win_meta_box_nonce'], 'instant_win_meta_box')) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        if (get_post_type($post_id) !== 'instant_win') {
            return;
        }

        $fields = array('prize_type', 'prize_value', 'prize_description', 'competition_id', 'winning_ticket_numbers');

        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
    }

    /**
     * Add question meta boxes
     */
    public function add_question_meta_boxes() {
        add_meta_box(
            'question_details',
            'Question Details',
            array($this, 'question_meta_box'),
            'competition_question',
            'normal',
            'high'
        );
    }

    /**
     * Question meta box content
     */
    public function question_meta_box($post) {
        wp_nonce_field('question_meta_box', 'question_meta_box_nonce');

        $answer_1 = get_post_meta($post->ID, '_answer_1', true);
        $answer_2 = get_post_meta($post->ID, '_answer_2', true);
        $answer_3 = get_post_meta($post->ID, '_answer_3', true);
        $correct_answer = get_post_meta($post->ID, '_correct_answer', true);
        $category = get_post_meta($post->ID, '_question_category', true);

        ?>
        <table class="form-table">
            <tr>
                <th><label for="question_category">Category</label></th>
                <td>
                    <input type="text" id="question_category" name="question_category" value="<?php echo esc_attr($category); ?>" />
                    <p class="description">e.g., Film, TV, General Knowledge</p>
                </td>
            </tr>
            <tr>
                <th><label>Answer Options</label></th>
                <td>
                    <p><label>A: </label><input type="text" name="answer_1" value="<?php echo esc_attr($answer_1); ?>" style="width: 300px;" /></p>
                    <p><label>B: </label><input type="text" name="answer_2" value="<?php echo esc_attr($answer_2); ?>" style="width: 300px;" /></p>
                    <p><label>C: </label><input type="text" name="answer_3" value="<?php echo esc_attr($answer_3); ?>" style="width: 300px;" /></p>
                </td>
            </tr>
            <tr>
                <th><label for="correct_answer">Correct Answer</label></th>
                <td>
                    <select id="correct_answer" name="correct_answer">
                        <option value="1" <?php selected($correct_answer, '1'); ?>>A</option>
                        <option value="2" <?php selected($correct_answer, '2'); ?>>B</option>
                        <option value="3" <?php selected($correct_answer, '3'); ?>>C</option>
                    </select>
                </td>
            </tr>
        </table>
        <?php
    }

    /**
     * Save question meta box data
     */
    public function save_question_meta_boxes($post_id) {
        if (!isset($_POST['question_meta_box_nonce']) || !wp_verify_nonce($_POST['question_meta_box_nonce'], 'question_meta_box')) {
            return;
        }

        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        if (get_post_type($post_id) !== 'competition_question') {
            return;
        }

        $fields = array('answer_1', 'answer_2', 'answer_3', 'correct_answer', 'question_category');

        foreach ($fields as $field) {
            if (isset($_POST[$field])) {
                update_post_meta($post_id, '_' . $field, sanitize_text_field($_POST[$field]));
            }
        }
    }

    /**
     * Add competition admin menu
     */
    public function add_competition_admin_menu() {
        // Main menu page
        add_menu_page(
            'Competitions',
            'Competitions',
            'manage_options',
            'competition-manager',
            array($this, 'competition_admin_page'),
            'dashicons-tickets-alt',
            25
        );

        // Submenu items
        add_submenu_page(
            'competition-manager',
            'Competition Products',
            'Competition Products',
            'manage_options',
            'edit.php?post_type=product&competition_filter=yes'
        );

        add_submenu_page(
            'competition-manager',
            'Instant Wins',
            'Instant Wins',
            'manage_options',
            'edit.php?post_type=instant_win'
        );

        add_submenu_page(
            'competition-manager',
            'Questions',
            'Questions',
            'manage_options',
            'edit.php?post_type=competition_question'
        );

        add_submenu_page(
            'competition-manager',
            'Winners',
            'Winners',
            'manage_options',
            'edit.php?post_type=winner'
        );

        add_submenu_page(
            'competition-manager',
            'Add New Winner',
            'Add New Winner',
            'manage_options',
            'add-new-winner',
            array($this, 'add_new_winner_page')
        );

        add_submenu_page(
            'competition-manager',
            'Test Email',
            'Test Email',
            'manage_options',
            'test-winner-email',
            array($this, 'test_winner_email_page')
        );
    }

    /**
     * Competition admin page
     */
    public function competition_admin_page() {
        ?>
        <div class="wrap">
            <h1>Competition Manager</h1>
            <p>Manage your film collectables competitions, instant wins, questions, and winners.</p>

            <div class="competition-admin-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 20px; margin-top: 30px;">

                <div class="admin-card" style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2 style="margin-top: 0;">🎬 Competition Products</h2>
                    <p>Create and manage competition products. These are WooCommerce products with competition features enabled.</p>
                    <a href="<?php echo admin_url('edit.php?post_type=product&competition_filter=yes'); ?>" class="button button-primary">Manage Competition Products</a>
                    <a href="<?php echo admin_url('post-new.php?post_type=product'); ?>" class="button">Add New Competition</a>
                </div>

                <div class="admin-card" style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2 style="margin-top: 0;">⭐ Instant Wins</h2>
                    <p>Set up instant win prizes that can be awarded to specific ticket numbers during competitions.</p>
                    <a href="<?php echo admin_url('edit.php?post_type=instant_win'); ?>" class="button button-primary">Manage Instant Wins</a>
                    <a href="<?php echo admin_url('post-new.php?post_type=instant_win'); ?>" class="button">Add New Instant Win</a>
                </div>

                <div class="admin-card" style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2 style="margin-top: 0;">❓ Questions</h2>
                    <p>Create skill-based questions for competitions. Questions can be reused across multiple competitions.</p>
                    <a href="<?php echo admin_url('edit.php?post_type=competition_question'); ?>" class="button button-primary">Manage Questions</a>
                    <a href="<?php echo admin_url('post-new.php?post_type=competition_question'); ?>" class="button">Add New Question</a>
                </div>

                <div class="admin-card" style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2 style="margin-top: 0;">📥 Export Tickets</h2>
                    <p>Export ticket lists for competitions to use with external randomizers for winner selection.</p>
                    <a href="<?php echo admin_url('edit.php?post_type=product&competition_filter=yes'); ?>" class="button button-primary">View Competitions to Export</a>
                    <?php
                    $bulk_export_url = wp_nonce_url(
                        admin_url('admin.php?action=export_all_competition_tickets'),
                        'export_all_tickets'
                    );
                    ?>
                    <a href="<?php echo esc_url($bulk_export_url); ?>" class="button" style="margin-left: 10px;">📥 Export All Competitions</a>
                    <p style="margin-top: 15px; font-size: 0.9em; color: #666;">
                        <strong>How to use:</strong><br>
                        1. Click "Export All" for a combined CSV of all competitions<br>
                        2. Or go to Competition Products for individual exports<br>
                        3. Download CSV with all ticket numbers and user names<br>
                        4. Use external randomizer to pick winner
                    </p>
                </div>

                <div class="admin-card" style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2 style="margin-top: 0;">🏆 Winners</h2>
                    <p>Announce competition winners and manage winner information for public display.</p>
                    <a href="<?php echo admin_url('edit.php?post_type=winner'); ?>" class="button button-primary">Manage Winners</a>
                    <a href="<?php echo admin_url('admin.php?page=add-new-winner'); ?>" class="button">Add New Winner</a>
                    <a href="<?php echo admin_url('admin.php?page=test-winner-email'); ?>" class="button">Test Email System</a>
                </div>



                <div class="admin-card" style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px;">
                    <h2 style="margin-top: 0;">📊 Reports</h2>
                    <p>View competition statistics, ticket sales, and performance metrics.</p>
                    <a href="<?php echo admin_url('admin.php?page=competition-reports'); ?>" class="button">View Reports</a>
                    <span style="color: #666; font-size: 12px;">(Coming Soon)</span>
                </div>

            </div>

            <div style="margin-top: 40px; padding: 20px; background: #f9f9f9; border-radius: 8px;">
                <h3>Quick Start Guide</h3>
                <ol>
                    <li><strong>Create a Competition:</strong> Go to Products → Add New, create your product, then enable "Is Competition Product" in the Competition Settings meta box.</li>
                    <li><strong>Add Questions:</strong> Create skill-based questions in the Questions section, then assign them to competitions.</li>
                    <li><strong>Set Up Instant Wins:</strong> Create instant win prizes and assign them to specific ticket numbers.</li>
                    <li><strong>Announce Winners:</strong> Use the Winners section to create public winner announcements.</li>
                </ol>
            </div>
        </div>
        <?php
    }

    /**
     * Add new winner admin page
     */
    public function add_new_winner_page() {
        ?>
        <div class="wrap">
            <h1>Add New Winner</h1>
            <p>Manually add a winner by selecting a competition and entering the winning ticket number.</p>

            <!-- Debug Information -->
            <div style="background: #f0f8ff; padding: 15px; border: 1px solid #0073aa; border-radius: 8px; margin-bottom: 20px;">
                <h3>Debug Information</h3>
                <button type="button" id="debug-database" class="button">Check Database Status</button>
                <button type="button" id="flush-rewrite-rules" class="button" style="margin-left: 10px;">Fix Winners Page</button>
                <div id="debug-results" style="margin-top: 10px;"></div>
            </div>

            <div style="background: white; padding: 20px; border: 1px solid #ddd; border-radius: 8px; max-width: 600px;">
                <form id="add-winner-form">
                    <?php wp_nonce_field('add_manual_winner', 'add_winner_nonce'); ?>

                    <table class="form-table">
                        <tr>
                            <th scope="row">
                                <label for="competition_id">Competition</label>
                            </th>
                            <td>
                                <select id="competition_id" name="competition_id" required style="width: 100%;">
                                    <option value="">Select a competition...</option>
                                    <?php
                                    // Get all competitions that are awaiting draw or completed
                                    $competitions = new WP_Query(array(
                                        'post_type' => 'product',
                                        'posts_per_page' => -1,
                                        'meta_query' => array(
                                            array(
                                                'key' => '_is_competition',
                                                'value' => 'yes',
                                                'compare' => '='
                                            )
                                        ),
                                        'orderby' => 'title',
                                        'order' => 'ASC'
                                    ));

                                    if ($competitions->have_posts()) {
                                        while ($competitions->have_posts()) {
                                            $competitions->the_post();
                                            $competition_id = get_the_ID();
                                            $status = get_competition_status_simple($competition_id);
                                            $existing_winner = get_post_meta($competition_id, 'winner_user_id', true);

                                            // Only show competitions that are awaiting draw and don't have a winner
                                            if ($status === 'awaiting_draw' && !$existing_winner) {
                                                echo '<option value="' . esc_attr($competition_id) . '">' . esc_html(get_the_title()) . ' (ID: ' . $competition_id . ')</option>';
                                            }
                                        }
                                        wp_reset_postdata();
                                    }
                                    ?>
                                </select>
                                <p class="description">Only competitions awaiting draw without existing winners are shown.</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">
                                <label for="ticket_number">Winning Ticket Number</label>
                            </th>
                            <td>
                                <input type="text" id="ticket_number" name="ticket_number" required style="width: 100%;" placeholder="Enter the winning ticket number">
                                <p class="description">Enter the exact ticket number that won the competition.</p>
                            </td>
                        </tr>
                    </table>

                    <div id="ticket-details" style="display: none; margin-top: 20px; padding: 15px; background: #f0f8ff; border: 1px solid #0073aa; border-radius: 4px;">
                        <h3>Ticket Details</h3>
                        <div id="user-details"></div>
                    </div>

                    <p class="submit">
                        <button type="button" id="check-ticket" class="button">Check Ticket</button>
                        <button type="submit" id="add-winner" class="button button-primary" disabled>Add Winner</button>
                    </p>
                </form>

                <div id="result-message" style="margin-top: 20px;"></div>
            </div>
        </div>

        <script type="text/javascript">
        jQuery(document).ready(function($) {
            // Debug database functionality
            $('#debug-database').on('click', function() {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'debug_competition_database',
                        nonce: $('#add_winner_nonce').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#debug-results').html('<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px;">' + response.data + '</div>');
                        } else {
                            $('#debug-results').html('<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px;">Error: ' + response.data + '</div>');
                        }
                    },
                    error: function() {
                        $('#debug-results').html('<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px;">An error occurred while checking the database.</div>');
                    }
                });
            });

            // Flush rewrite rules functionality
            $('#flush-rewrite-rules').on('click', function() {
                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'flush_winner_rewrite_rules'
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#debug-results').html('<div style="background: #d4edda; border: 1px solid #c3e6cb; color: #155724; padding: 10px; border-radius: 4px;">' + response.data + '</div>');
                        } else {
                            $('#debug-results').html('<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px;">Error: ' + response.data + '</div>');
                        }
                    },
                    error: function() {
                        $('#debug-results').html('<div style="background: #f8d7da; border: 1px solid #f5c6cb; color: #721c24; padding: 10px; border-radius: 4px;">An error occurred while flushing rewrite rules.</div>');
                    }
                });
            });

            $('#check-ticket').on('click', function() {
                var competitionId = $('#competition_id').val();
                var ticketNumber = $('#ticket_number').val();

                if (!competitionId || !ticketNumber) {
                    alert('Please select a competition and enter a ticket number.');
                    return;
                }

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'get_ticket_user_details',
                        competition_id: competitionId,
                        ticket_number: ticketNumber,
                        nonce: $('#add_winner_nonce').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#user-details').html(
                                '<p><strong>User:</strong> ' + response.data.user_name + '</p>' +
                                '<p><strong>Email:</strong> ' + response.data.user_email + '</p>' +
                                '<p><strong>Ticket ID:</strong> ' + response.data.ticket_id + '</p>' +
                                '<p><strong>Created:</strong> ' + response.data.created_at + '</p>'
                            );
                            $('#ticket-details').show();
                            $('#add-winner').prop('disabled', false);
                        } else {
                            alert('Error: ' + response.data);
                            $('#ticket-details').hide();
                            $('#add-winner').prop('disabled', true);
                        }
                    },
                    error: function() {
                        alert('An error occurred while checking the ticket.');
                    }
                });
            });

            $('#add-winner-form').on('submit', function(e) {
                e.preventDefault();

                var competitionId = $('#competition_id').val();
                var ticketNumber = $('#ticket_number').val();

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'add_manual_winner',
                        competition_id: competitionId,
                        ticket_number: ticketNumber,
                        nonce: $('#add_winner_nonce').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $('#result-message').html('<div class="notice notice-success"><p>' + response.data.message + '</p></div>');
                            $('#add-winner-form')[0].reset();
                            $('#ticket-details').hide();
                            $('#add-winner').prop('disabled', true);
                        } else {
                            $('#result-message').html('<div class="notice notice-error"><p>Error: ' + response.data + '</p></div>');
                        }
                    },
                    error: function() {
                        $('#result-message').html('<div class="notice notice-error"><p>An error occurred while adding the winner.</p></div>');
                    }
                });
            });
        });
        </script>
        <?php
    }

    public function register_rest_routes() {
        // Custom REST API endpoints
        register_rest_route('competition-manager/v1', '/select-winner/(?P<id>\d+)', array(
            'methods' => 'POST',
            'callback' => array($this, 'select_winner'),
            'permission_callback' => array($this, 'check_admin_permission'),
            'args' => array(
                'id' => array(
                    'validate_callback' => function($param, $request, $key) {
                        return is_numeric($param);
                    }
                ),
            ),
        ));
        
        register_rest_route('competition-manager/v1', '/instant-win', array(
            'methods' => 'POST',
            'callback' => array($this, 'process_instant_win'),
            'permission_callback' => array($this, 'check_user_permission'),
        ));
        
        register_rest_route('competition-manager/v1', '/competition-stats/(?P<id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_competition_stats'),
            'permission_callback' => '__return_true',
            'args' => array(
                'id' => array(
                    'validate_callback' => function($param, $request, $key) {
                        return is_numeric($param);
                    }
                ),
            ),
        ));
        
        register_rest_route('competition-manager/v1', '/user-tickets/(?P<user_id>\d+)', array(
            'methods' => 'GET',
            'callback' => array($this, 'get_user_tickets'),
            'permission_callback' => array($this, 'check_user_permission'),
            'args' => array(
                'user_id' => array(
                    'validate_callback' => function($param, $request, $key) {
                        return is_numeric($param);
                    }
                ),
            ),
        ));
    }
    
    public function select_winner($request) {
        $competition_id = $request['id'];
        
        // Get competition
        $competition = get_post($competition_id);
        if (!$competition || $competition->post_type !== 'competition') {
            return new WP_Error('invalid_competition', 'Competition not found', array('status' => 404));
        }
        
        // Get all tickets for this competition from WooCommerce orders
        $tickets = $this->get_competition_tickets($competition_id);
        
        if (empty($tickets)) {
            return new WP_Error('no_tickets', 'No tickets found for this competition', array('status' => 400));
        }
        
        // Select random winner
        $winner_ticket = $tickets[array_rand($tickets)];
        
        // Update competition with winner information
        update_post_meta($competition_id, 'winner_user_id', $winner_ticket['user_id']);
        update_post_meta($competition_id, 'winner_ticket_id', $winner_ticket['ticket_id']);
        update_post_meta($competition_id, 'winner_username', $winner_ticket['username']);
        update_post_meta($competition_id, 'winner_ticket_number', $winner_ticket['ticket_number']);
        update_post_meta($competition_id, 'status', 'winner_selected');
        
        // Create winner record
        $winner_post = wp_insert_post(array(
            'post_title' => $competition->post_title . ' Winner',
            'post_content' => 'Winner selected for ' . $competition->post_title,
            'post_status' => 'publish',
            'post_type' => 'winner',
            'meta_input' => array(
                'competition_id' => $competition_id,
                'user_id' => $winner_ticket['user_id'],
                'ticket_id' => $winner_ticket['ticket_id'],
                'username' => $winner_ticket['username'],
                'ticket_number' => $winner_ticket['ticket_number'],
                'selected_date' => current_time('mysql'),
            )
        ));
        
        return array(
            'success' => true,
            'winner' => array(
                'user_id' => $winner_ticket['user_id'],
                'username' => $winner_ticket['username'],
                'ticket_number' => $winner_ticket['ticket_number'],
                'winner_post_id' => $winner_post,
            )
        );
    }
    
    public function process_instant_win($request) {
        $competition_id = $request['competition_id'];
        $user_id = $request['user_id'];
        
        // Get instant win prizes for this competition
        $prizes = get_post_meta($competition_id, 'instant_win_prizes', true);
        
        if (empty($prizes)) {
            return array('success' => false, 'message' => 'No instant win prizes available');
        }
        
        // Calculate win probability and select prize
        $win_result = $this->calculate_instant_win($prizes);
        
        if ($win_result['won']) {
            // Record the win
            $this->record_instant_win($competition_id, $user_id, $win_result['prize']);
            
            return array(
                'success' => true,
                'won' => true,
                'prize' => $win_result['prize']
            );
        }
        
        return array(
            'success' => true,
            'won' => false,
            'message' => 'Better luck next time!'
        );
    }
    
    public function get_competition_stats($request) {
        $competition_id = $request['id'];
        
        // Get competition tickets count from WooCommerce
        $tickets_sold = $this->get_competition_tickets_count($competition_id);
        $max_tickets = get_post_meta($competition_id, 'max_tickets', true);
        
        return array(
            'competition_id' => $competition_id,
            'tickets_sold' => $tickets_sold,
            'max_tickets' => $max_tickets,
            'tickets_remaining' => max(0, $max_tickets - $tickets_sold),
            'percentage_sold' => $max_tickets > 0 ? round(($tickets_sold / $max_tickets) * 100, 2) : 0,
        );
    }
    
    public function get_user_tickets($request) {
        $user_id = $request['user_id'];
        
        // Get user's tickets from WooCommerce orders
        $tickets = $this->get_user_competition_tickets($user_id);
        
        return array(
            'user_id' => $user_id,
            'tickets' => $tickets,
            'total_tickets' => count($tickets),
        );
    }
    
    // Helper methods
    private function get_competition_tickets($competition_id) {
        // This would integrate with WooCommerce to get actual ticket purchases
        // For now, return mock data structure
        return array();
    }
    
    private function get_competition_tickets_count($competition_id) {
        // Get count from WooCommerce orders
        return 0;
    }
    
    private function get_user_competition_tickets($user_id) {
        // Get user's tickets from WooCommerce
        return array();
    }
    
    private function calculate_instant_win($prizes) {
        // Implement instant win logic
        return array('won' => false, 'prize' => null);
    }
    
    private function record_instant_win($competition_id, $user_id, $prize) {
        // Record instant win in database
    }
    
    // Permission callbacks
    public function check_admin_permission() {
        return current_user_can('manage_options');
    }
    
    public function check_user_permission() {
        return is_user_logged_in();
    }
    
    // Admin interface
    public function admin_scripts($hook) {
        if ('post.php' === $hook || 'post-new.php' === $hook) {
            wp_enqueue_script('competition-manager-admin', COMPETITION_MANAGER_PLUGIN_URL . 'assets/admin.js', array('jquery'), COMPETITION_MANAGER_VERSION, true);
            wp_enqueue_style('competition-manager-admin', COMPETITION_MANAGER_PLUGIN_URL . 'assets/admin.css', array(), COMPETITION_MANAGER_VERSION);
        }
    }
    
    public function competition_columns($columns) {
        $columns['status'] = 'Status';
        $columns['tickets_sold'] = 'Tickets Sold';
        $columns['instant_wins'] = 'Instant Wins';
        $columns['end_date'] = 'End Date';
        return $columns;
    }
    
    public function competition_column_content($column, $post_id) {
        switch ($column) {
            case 'status':
                echo get_post_meta($post_id, 'status', true);
                break;
            case 'tickets_sold':
                echo get_post_meta($post_id, 'sold_tickets', true);
                break;
            case 'instant_wins':
                $instant_wins_enabled = get_post_meta($post_id, 'instant_wins_enabled', true);
                $instant_win_prizes = get_post_meta($post_id, 'instant_win_prizes', true) ?: array();
                if ($instant_wins_enabled && !empty($instant_win_prizes)) {
                    echo '<span style="color: #10b981; font-weight: bold;">✓ Enabled (' . count($instant_win_prizes) . ' prizes)</span>';
                } else {
                    echo '<span style="color: #6b7280;">—</span>';
                }
                break;
            case 'end_date':
                $end_date = get_post_meta($post_id, 'end_date', true);
                if ($end_date) {
                    echo date('Y-m-d H:i', strtotime($end_date));
                }
                break;
        }
    }

    public function add_competition_meta_boxes() {
        add_meta_box(
            'competition_details',
            'Competition Details',
            array($this, 'competition_details_meta_box'),
            'competition',
            'normal',
            'high'
        );

        add_meta_box(
            'competition_instant_wins',
            'Instant Wins (Optional)',
            array($this, 'competition_instant_wins_meta_box'),
            'competition',
            'side',
            'default'
        );

        add_meta_box(
            'competition_question',
            'Competition Question',
            array($this, 'competition_question_meta_box'),
            'competition',
            'side',
            'default'
        );
    }

    public function competition_details_meta_box($post) {
        wp_nonce_field('competition_meta_box', 'competition_meta_box_nonce');

        $price = get_post_meta($post->ID, 'price', true);
        $max_tickets = get_post_meta($post->ID, 'max_tickets', true);
        $sold_tickets = get_post_meta($post->ID, 'sold_tickets', true);
        $status = get_post_meta($post->ID, 'status', true);
        $end_date = get_post_meta($post->ID, 'end_date', true);
        $featured = get_post_meta($post->ID, 'featured', true);

        ?>
        <table class="form-table">
            <tr>
                <th><label for="competition_price">Entry Price (£)</label></th>
                <td><input type="number" step="0.01" id="competition_price" name="competition_price" value="<?php echo esc_attr($price); ?>" /></td>
            </tr>
            <tr>
                <th><label for="competition_max_tickets">Max Tickets</label></th>
                <td><input type="number" id="competition_max_tickets" name="competition_max_tickets" value="<?php echo esc_attr($max_tickets); ?>" /></td>
            </tr>
            <tr>
                <th><label for="competition_sold_tickets">Sold Tickets</label></th>
                <td><input type="number" id="competition_sold_tickets" name="competition_sold_tickets" value="<?php echo esc_attr($sold_tickets); ?>" readonly /></td>
            </tr>
            <tr>
                <th><label for="competition_status">Status</label></th>
                <td>
                    <select id="competition_status" name="competition_status">
                        <option value="active" <?php selected($status, 'active'); ?>>Active</option>
                        <option value="inactive" <?php selected($status, 'inactive'); ?>>Inactive</option>
                        <option value="ended" <?php selected($status, 'ended'); ?>>Ended</option>
                        <option value="awaiting_draw" <?php selected($status, 'awaiting_draw'); ?>>Awaiting Draw</option>
                        <option value="completed" <?php selected($status, 'completed'); ?>>Completed</option>
                    </select>
                </td>
            </tr>
            <tr>
                <th><label for="competition_end_date">End Date</label></th>
                <td><input type="datetime-local" id="competition_end_date" name="competition_end_date" value="<?php echo esc_attr($end_date ? date('Y-m-d\TH:i', strtotime($end_date)) : ''); ?>" /></td>
            </tr>
            <tr>
                <th><label for="competition_featured">Featured on Homepage</label></th>
                <td>
                    <input type="checkbox" id="competition_featured" name="competition_featured" value="1" <?php checked($featured, '1'); ?> />
                    <label for="competition_featured">Display this competition in the homepage slider</label>
                </td>
            </tr>
        </table>
        <?php
    }



    public function competition_instant_wins_meta_box($post) {
        $instant_wins_enabled = get_post_meta($post->ID, 'instant_wins_enabled', true);
        $instant_win_prizes = get_post_meta($post->ID, 'instant_win_prizes', true) ?: array();
        $max_tickets = get_post_meta($post->ID, 'max_tickets', true) ?: 100;

        ?>
        <div style="margin-bottom: 15px;">
            <label>
                <input type="checkbox" name="instant_wins_enabled" value="1" <?php checked($instant_wins_enabled, '1'); ?> />
                <strong>Enable Instant Wins for this competition</strong>
            </label>
        </div>

        <div id="instant-wins-config" style="<?php echo $instant_wins_enabled ? '' : 'display: none;'; ?>">
            <div style="background: #e8f5e8; padding: 10px; border-radius: 4px; margin-bottom: 15px; border-left: 4px solid #10b981;">
                <strong>How Instant Wins Work:</strong><br>
                <small>Pre-assign specific ticket numbers to instant win prizes. When someone buys those exact ticket numbers, they win instantly. Winning ticket numbers are shown transparently on the competition page.</small>
            </div>

            <div style="margin-bottom: 15px;">
                <label><strong>Instant Win Prizes & Ticket Numbers:</strong></label><br>
                <small>Max tickets for this competition: <strong><?php echo esc_html($max_tickets); ?></strong></small>
                <div id="instant-win-prizes">
                    <?php if (!empty($instant_win_prizes)) : ?>
                        <?php foreach ($instant_win_prizes as $index => $prize) : ?>
                            <div class="instant-win-prize" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 4px; background: #f9f9f9;">
                                <div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">
                                    <input type="text" name="instant_win_prizes[<?php echo $index; ?>][name]"
                                           placeholder="Prize Name (e.g., £10 Amazon Voucher)" value="<?php echo esc_attr($prize['name'] ?? ''); ?>"
                                           style="width: 100%;" />
                                    <input type="number" name="instant_win_prizes[<?php echo $index; ?>][value]"
                                           placeholder="Prize Value (£)" value="<?php echo esc_attr($prize['value'] ?? ''); ?>"
                                           step="0.01" style="width: 100%;" />
                                </div>
                                <input type="text" name="instant_win_prizes[<?php echo $index; ?>][description]"
                                       placeholder="Prize Description (optional)" value="<?php echo esc_attr($prize['description'] ?? ''); ?>"
                                       style="width: 100%; margin-bottom: 10px;" />
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Prize Type:</label>
                                    <select name="instant_win_prizes[<?php echo $index; ?>][type]" style="width: 100%; margin-bottom: 10px;">
                                        <option value="site_credit" <?php selected($prize['type'] ?? '', 'site_credit'); ?>>Site Credit (goes to user wallet)</option>
                                        <option value="physical" <?php selected($prize['type'] ?? '', 'physical'); ?>>Physical Prize (shipped to user)</option>
                                    </select>
                                </div>
                                <div style="margin-bottom: 10px;">
                                    <label style="display: block; font-weight: bold; margin-bottom: 5px;">Winning Ticket Numbers:</label>
                                    <input type="text" name="instant_win_prizes[<?php echo $index; ?>][ticket_numbers]"
                                           placeholder="Auto-assigned when competition is published"
                                           value="<?php echo esc_attr(implode(',', $prize['ticket_numbers'] ?? array())); ?>"
                                           style="width: 100%; background: #f0f0f0;" readonly />
                                    <small style="color: #666;">Ticket numbers are automatically assigned randomly when the competition is published</small>
                                </div>
                                <button type="button" class="remove-prize" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Remove Prize</button>
                            </div>
                        <?php endforeach; ?>
                    <?php endif; ?>
                </div>
                <button type="button" id="add-instant-win-prize" style="background: #007cba; color: white; border: none; padding: 8px 15px; border-radius: 3px; cursor: pointer; margin-top: 10px;">Add Prize</button>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            var maxTickets = <?php echo intval($max_tickets); ?>;

            // Toggle instant wins config
            $('input[name="instant_wins_enabled"]').change(function() {
                if ($(this).is(':checked')) {
                    $('#instant-wins-config').show();
                } else {
                    $('#instant-wins-config').hide();
                }
            });

            // Add new prize
            $('#add-instant-win-prize').click(function() {
                var index = $('.instant-win-prize').length;
                var prizeHtml = '<div class="instant-win-prize" style="border: 1px solid #ddd; padding: 15px; margin-bottom: 10px; border-radius: 4px; background: #f9f9f9;">' +
                    '<div style="display: grid; grid-template-columns: 1fr 1fr; gap: 10px; margin-bottom: 10px;">' +
                    '<input type="text" name="instant_win_prizes[' + index + '][name]" placeholder="Prize Name (e.g., £10 Amazon Voucher)" style="width: 100%;" />' +
                    '<input type="number" name="instant_win_prizes[' + index + '][value]" placeholder="Prize Value (£)" step="0.01" style="width: 100%;" />' +
                    '</div>' +
                    '<input type="text" name="instant_win_prizes[' + index + '][description]" placeholder="Prize Description (optional)" style="width: 100%; margin-bottom: 10px;" />' +
                    '<div style="margin-bottom: 10px;">' +
                    '<label style="display: block; font-weight: bold; margin-bottom: 5px;">Prize Type:</label>' +
                    '<select name="instant_win_prizes[' + index + '][type]" style="width: 100%; margin-bottom: 10px;">' +
                    '<option value="site_credit">Site Credit (goes to user wallet)</option>' +
                    '<option value="physical">Physical Prize (shipped to user)</option>' +
                    '</select>' +
                    '</div>' +
                    '<div style="margin-bottom: 10px;">' +
                    '<label style="display: block; font-weight: bold; margin-bottom: 5px;">Winning Ticket Numbers:</label>' +
                    '<input type="text" name="instant_win_prizes[' + index + '][ticket_numbers]" placeholder="Auto-assigned when competition is published" style="width: 100%; background: #f0f0f0;" readonly />' +
                    '<small style="color: #666;">Ticket numbers are automatically assigned randomly when the competition is published</small>' +
                    '</div>' +
                    '<button type="button" class="remove-prize" style="background: #dc3545; color: white; border: none; padding: 5px 10px; border-radius: 3px; cursor: pointer;">Remove Prize</button>' +
                    '</div>';
                $('#instant-win-prizes').append(prizeHtml);
            });

            // Remove prize
            $(document).on('click', '.remove-prize', function() {
                $(this).closest('.instant-win-prize').remove();
            });
        });
        </script>
        <?php
    }

    public function competition_question_meta_box($post) {
        $question_set = get_post_meta($post->ID, 'question_set', true);
        $predefined_questions = get_option('competition_predefined_questions', array());
        ?>
        <div style="margin-bottom: 15px;">
            <label for="competition_question_set"><strong>Select Question:</strong></label><br>
            <select id="competition_question_set" name="competition_question_set" style="width: 100%;">
                <option value="">Random Question (Auto-assigned)</option>
                <?php foreach ($predefined_questions as $id => $question): ?>
                    <option value="<?php echo esc_attr($id); ?>" <?php selected($question_set, $id); ?>>
                        <?php echo esc_html(substr($question['question'], 0, 50) . (strlen($question['question']) > 50 ? '...' : '')); ?>
                    </option>
                <?php endforeach; ?>
            </select>
            <small style="color: #666; display: block; margin-top: 5px;">
                Leave blank to auto-assign a random question.
                <a href="<?php echo admin_url('edit.php?post_type=competition&page=competition-questions'); ?>" target="_blank">Manage Questions</a>
            </small>
        </div>

        <?php if (!empty($question_set) && isset($predefined_questions[$question_set])): ?>
            <div style="background: #f0f9ff; padding: 10px; border-radius: 4px; border-left: 4px solid #0ea5e9;">
                <strong>Selected Question Preview:</strong><br>
                <div style="margin: 10px 0;">
                    <strong>Q:</strong> <?php echo esc_html($predefined_questions[$question_set]['question']); ?>
                </div>
                <div style="margin: 5px 0;">
                    <strong>A:</strong> <?php echo esc_html($predefined_questions[$question_set]['answer_a']); ?>
                </div>
                <div style="margin: 5px 0;">
                    <strong>B:</strong> <?php echo esc_html($predefined_questions[$question_set]['answer_b']); ?>
                </div>
                <div style="margin: 5px 0;">
                    <strong>C:</strong> <?php echo esc_html($predefined_questions[$question_set]['answer_c']); ?>
                </div>
                <div style="margin: 5px 0; color: #10b981; font-weight: bold;">
                    Correct Answer: <?php
                    $correct_labels = array(1 => 'A', 2 => 'B', 3 => 'C');
                    echo esc_html($correct_labels[$predefined_questions[$question_set]['correct_answer']] ?? 'Unknown');
                    ?>
                </div>
            </div>
        <?php endif; ?>
        <?php
    }

    public function save_competition_meta_boxes($post_id) {
        // Check if nonce is valid
        if (!isset($_POST['competition_meta_box_nonce']) || !wp_verify_nonce($_POST['competition_meta_box_nonce'], 'competition_meta_box')) {
            return;
        }

        // Check if user has permission to edit
        if (!current_user_can('edit_post', $post_id)) {
            return;
        }

        // Check if not an autosave
        if (defined('DOING_AUTOSAVE') && DOING_AUTOSAVE) {
            return;
        }

        // Check post type
        if (get_post_type($post_id) !== 'competition') {
            return;
        }

        // Save competition details
        $fields = array(
            'competition_price' => 'price',
            'competition_max_tickets' => 'max_tickets',
            'competition_sold_tickets' => 'sold_tickets',
            'competition_status' => 'status',
            'competition_end_date' => 'end_date'
        );

        // Handle featured checkbox separately
        $featured = isset($_POST['competition_featured']) ? '1' : '0';
        update_post_meta($post_id, 'featured', $featured);

        // Handle instant wins
        $instant_wins_enabled = isset($_POST['instant_wins_enabled']) ? '1' : '0';
        update_post_meta($post_id, 'instant_wins_enabled', $instant_wins_enabled);

        if (isset($_POST['instant_win_prizes']) && is_array($_POST['instant_win_prizes'])) {
            $prizes = array();
            $max_tickets = intval($_POST['competition_max_tickets'] ?? 100);
            $used_ticket_numbers = array();

            foreach ($_POST['instant_win_prizes'] as $prize) {
                if (!empty($prize['name']) && !empty($prize['value'])) {
                    // Get existing ticket numbers or assign new ones
                    $ticket_numbers = array();
                    if (!empty($prize['ticket_numbers'])) {
                        // Parse existing ticket numbers
                        $ticket_numbers = array_map('trim', explode(',', $prize['ticket_numbers']));
                        $ticket_numbers = array_filter($ticket_numbers, 'is_numeric');
                        $ticket_numbers = array_map('intval', $ticket_numbers);
                        $ticket_numbers = array_unique($ticket_numbers);
                    }

                    // If no ticket numbers assigned yet, auto-assign one random number
                    if (empty($ticket_numbers)) {
                        $attempts = 0;
                        while (empty($ticket_numbers) && $attempts < 100) {
                            $random_number = rand(1, $max_tickets);
                            if (!in_array($random_number, $used_ticket_numbers)) {
                                $ticket_numbers = array($random_number);
                                $used_ticket_numbers[] = $random_number;
                            }
                            $attempts++;
                        }
                    } else {
                        $used_ticket_numbers = array_merge($used_ticket_numbers, $ticket_numbers);
                    }

                    if (!empty($ticket_numbers)) {
                        sort($ticket_numbers);
                        $prizes[] = array(
                            'name' => sanitize_text_field($prize['name']),
                            'description' => sanitize_text_field($prize['description'] ?? ''),
                            'value' => floatval($prize['value']),
                            'type' => sanitize_text_field($prize['type'] ?? 'site_credit'),
                            'ticket_numbers' => $ticket_numbers
                        );
                    }
                }
            }
            update_post_meta($post_id, 'instant_win_prizes', $prizes);
        } else {
            delete_post_meta($post_id, 'instant_win_prizes');
        }

        foreach ($fields as $field => $meta_key) {
            if (isset($_POST[$field])) {
                $value = sanitize_text_field($_POST[$field]);
                if ($field === 'competition_end_date' && $value) {
                    $value = date('Y-m-d H:i:s', strtotime($value));
                }
                update_post_meta($post_id, $meta_key, $value);
            }
        }

        // Initialize sold_tickets to 0 if not set
        if (!get_post_meta($post_id, 'sold_tickets', true)) {
            update_post_meta($post_id, 'sold_tickets', 0);
        }

        // Handle question set
        if (isset($_POST['competition_question_set'])) {
            $question_set = sanitize_text_field($_POST['competition_question_set']);
            if (!empty($question_set)) {
                update_post_meta($post_id, 'question_set', $question_set);
            } else {
                // If question set is empty, assign a random one
                $predefined_questions = get_option('competition_predefined_questions', array());
                if (!empty($predefined_questions)) {
                    $random_question_id = array_rand($predefined_questions);
                    update_post_meta($post_id, 'question_set', $random_question_id);
                } else {
                    delete_post_meta($post_id, 'question_set');
                }
            }
        }

        // Clean up obsolete meta fields
        delete_post_meta($post_id, 'instant_win_chance');
        delete_post_meta($post_id, 'ticket_price'); // Use 'price' instead
        delete_post_meta($post_id, 'question'); // Old question system
        delete_post_meta($post_id, 'answer_a'); // Old question system
        delete_post_meta($post_id, 'answer_b'); // Old question system
        delete_post_meta($post_id, 'answer_c'); // Old question system
        delete_post_meta($post_id, 'correct_answer'); // Old question system

        // Create or update WooCommerce product for this competition
        $this->create_competition_product($post_id);
    }

    /**
     * Create a WooCommerce product for the competition
     */
    private function create_competition_product($competition_id) {
        if (!class_exists('WooCommerce')) {
            return false;
        }

        $competition = get_post($competition_id);
        if (!$competition) {
            return false;
        }

        // Check if product already exists
        $existing_product_id = get_post_meta($competition_id, 'product_id', true);
        if ($existing_product_id && wc_get_product($existing_product_id)) {
            return $existing_product_id;
        }

        // Get competition details
        $ticket_price = get_post_meta($competition_id, 'ticket_price', true) ?: '0.25';

        // Create WooCommerce product
        $product = new WC_Product_Simple();
        $product->set_name($competition->post_title . ' - Competition Ticket');
        $product->set_slug('competition-ticket-' . $competition_id);
        $product->set_description('Competition ticket for: ' . $competition->post_title);
        $product->set_short_description('Enter this exciting competition for a chance to win!');
        $product->set_status('private'); // Make completely private
        $product->set_catalog_visibility('hidden'); // Hide from shop
        $product->set_price($ticket_price);
        $product->set_regular_price($ticket_price);
        $product->set_manage_stock(false); // We'll manage stock through competition system
        $product->set_stock_status('instock');
        $product->set_virtual(true); // Digital product
        $product->set_downloadable(false);

        // Save the product
        $product_id = $product->save();

        if ($product_id) {
            // Link the product to the competition
            update_post_meta($competition_id, 'product_id', $product_id);
            update_post_meta($product_id, '_competition_id', $competition_id);

            return $product_id;
        }

        return false;
    }

    /**
     * Clean up obsolete custom fields from all competitions
     */
    private function cleanup_obsolete_custom_fields() {
        global $wpdb;

        $competitions = get_posts(array(
            'post_type' => 'competition',
            'posts_per_page' => -1,
            'post_status' => 'any'
        ));

        $obsolete_fields = array(
            'instant_win_chance',
            'ticket_price',
            'question_set',
            'question',
            'answer_a',
            'answer_b',
            'answer_c',
            'correct_answer'
        );

        // Delete meta values from competitions
        foreach ($competitions as $competition) {
            foreach ($obsolete_fields as $field) {
                delete_post_meta($competition->ID, $field);
            }
        }

        // Also remove the meta keys entirely from the database
        // This will remove them from the custom fields dropdown
        foreach ($obsolete_fields as $field) {
            $wpdb->query($wpdb->prepare(
                "DELETE FROM {$wpdb->postmeta} WHERE meta_key = %s",
                $field
            ));
        }

        return count($competitions);
    }

    /**
     * AJAX handler to clean up custom fields
     */
    public function ajax_cleanup_custom_fields() {
        // Check user permissions
        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized');
        }

        $count = $this->cleanup_obsolete_custom_fields();

        wp_send_json_success(array(
            'message' => "Cleaned up obsolete custom fields from {$count} competitions.",
            'count' => $count
        ));
    }



    // Activation/Deactivation
    public function activate() {
        $this->register_post_types();
        flush_rewrite_rules();

        // Create necessary database tables
        $this->create_tables();

        // Create products for existing competitions
        $this->create_products_for_existing_competitions();

        // Clean up obsolete custom fields from all competitions
        $this->cleanup_obsolete_custom_fields();

        // Initialize default questions if none exist
        $this->initialize_default_questions();

        // Update to easy questions
        $this->update_to_easy_questions();
    }

    private function initialize_default_questions() {
        $existing_questions = get_option('competition_predefined_questions', array());

        if (empty($existing_questions)) {
            $default_questions = array(
                array(
                    'question' => 'In the movie Jaws, what type of animal attacks the beach?',
                    'answer_a' => 'Shark',
                    'answer_b' => 'Whale',
                    'answer_c' => 'Octopus',
                    'correct_answer' => 1
                ),
                array(
                    'question' => 'What color is Superman\'s cape?',
                    'answer_a' => 'Blue',
                    'answer_b' => 'Red',
                    'answer_c' => 'Green',
                    'correct_answer' => 2
                ),
                array(
                    'question' => 'In Star Wars, what color is Luke Skywalker\'s first lightsaber?',
                    'answer_a' => 'Red',
                    'answer_b' => 'Green',
                    'answer_c' => 'Blue',
                    'correct_answer' => 3
                ),
                array(
                    'question' => 'What does E.T. want to do in the movie E.T.?',
                    'answer_a' => 'Phone home',
                    'answer_b' => 'Eat candy',
                    'answer_c' => 'Ride a bike',
                    'correct_answer' => 1
                ),
                array(
                    'question' => 'In The Lion King, what type of animal is Simba?',
                    'answer_a' => 'Tiger',
                    'answer_b' => 'Lion',
                    'answer_c' => 'Leopard',
                    'correct_answer' => 2
                ),
                array(
                    'question' => 'What vehicle does Batman drive?',
                    'answer_a' => 'Batmobile',
                    'answer_b' => 'Batplane',
                    'answer_c' => 'Batboat',
                    'correct_answer' => 1
                ),
                array(
                    'question' => 'In Toy Story, what is the name of the cowboy toy?',
                    'answer_a' => 'Buzz',
                    'answer_b' => 'Woody',
                    'answer_c' => 'Rex',
                    'correct_answer' => 2
                ),
                array(
                    'question' => 'What does Spider-Man shoot from his wrists?',
                    'answer_a' => 'Webs',
                    'answer_b' => 'Lasers',
                    'answer_c' => 'Fire',
                    'correct_answer' => 1
                )
            );

            update_option('competition_predefined_questions', $default_questions);
        }
    }

    /**
     * Update questions to easier ones (can be called manually)
     */
    public function update_to_easy_questions() {
        $easy_questions = array(
            array(
                'question' => 'In the movie Jaws, what type of animal attacks the beach?',
                'answer_a' => 'Shark',
                'answer_b' => 'Whale',
                'answer_c' => 'Octopus',
                'correct_answer' => 1
            ),
            array(
                'question' => 'What color is Superman\'s cape?',
                'answer_a' => 'Blue',
                'answer_b' => 'Red',
                'answer_c' => 'Green',
                'correct_answer' => 2
            ),
            array(
                'question' => 'In Star Wars, what color is Luke Skywalker\'s first lightsaber?',
                'answer_a' => 'Red',
                'answer_b' => 'Green',
                'answer_c' => 'Blue',
                'correct_answer' => 3
            ),
            array(
                'question' => 'What does E.T. want to do in the movie E.T.?',
                'answer_a' => 'Phone home',
                'answer_b' => 'Eat candy',
                'answer_c' => 'Ride a bike',
                'correct_answer' => 1
            ),
            array(
                'question' => 'In The Lion King, what type of animal is Simba?',
                'answer_a' => 'Tiger',
                'answer_b' => 'Lion',
                'answer_c' => 'Leopard',
                'correct_answer' => 2
            ),
            array(
                'question' => 'What vehicle does Batman drive?',
                'answer_a' => 'Batmobile',
                'answer_b' => 'Batplane',
                'answer_c' => 'Batboat',
                'correct_answer' => 1
            ),
            array(
                'question' => 'In Toy Story, what is the name of the cowboy toy?',
                'answer_a' => 'Buzz',
                'answer_b' => 'Woody',
                'answer_c' => 'Rex',
                'correct_answer' => 2
            ),
            array(
                'question' => 'What does Spider-Man shoot from his wrists?',
                'answer_a' => 'Webs',
                'answer_b' => 'Lasers',
                'answer_c' => 'Fire',
                'correct_answer' => 1
            )
        );

        update_option('competition_predefined_questions', $easy_questions);
        return true;
    }

    /**
     * Handle ticket export requests
     */
    public function handle_ticket_export() {
        if (!isset($_GET['action']) || $_GET['action'] !== 'export_competition_tickets') {
            return;
        }

        if (!isset($_GET['competition_id']) || !current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }

        $competition_id = intval($_GET['competition_id']);

        // Verify nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'export_tickets_' . $competition_id)) {
            wp_die('Security check failed');
        }

        $this->export_competition_tickets($competition_id);
    }

    /**
     * Export competition tickets to CSV
     */
    private function export_competition_tickets($competition_id) {
        global $wpdb;

        // Get competition details
        $competition_title = get_the_title($competition_id);
        $competition_title = sanitize_file_name($competition_title);

        // Get all tickets for this competition
        $tickets_table = $wpdb->prefix . 'competition_tickets';
        $tickets = $wpdb->get_results($wpdb->prepare(
            "SELECT t.*, u.display_name, u.user_email, u.user_login,
                    um_first.meta_value as first_name,
                    um_last.meta_value as last_name
             FROM $tickets_table t
             LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID
             LEFT JOIN {$wpdb->usermeta} um_first ON (u.ID = um_first.user_id AND um_first.meta_key = 'first_name')
             LEFT JOIN {$wpdb->usermeta} um_last ON (u.ID = um_last.user_id AND um_last.meta_key = 'last_name')
             WHERE t.competition_id = %d
             ORDER BY t.ticket_number ASC",
            $competition_id
        ));

        if (empty($tickets)) {
            wp_die('No tickets found for this competition');
        }

        // Set headers for CSV download
        $filename = 'competition-tickets-' . $competition_title . '-' . date('Y-m-d-H-i-s') . '.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Open output stream
        $output = fopen('php://output', 'w');

        // Add BOM for Excel compatibility
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Write CSV header
        fputcsv($output, array(
            'Ticket Number',
            'User ID',
            'Username',
            'First Name',
            'Last Name',
            'Display Name',
            'Email',
            'Order ID',
            'Status',
            'Purchase Date',
            'Competition'
        ));

        // Write ticket data
        foreach ($tickets as $ticket) {
            $full_name = trim(($ticket->first_name ?: '') . ' ' . ($ticket->last_name ?: ''));
            if (empty($full_name)) {
                $full_name = $ticket->display_name ?: $ticket->user_login;
            }

            fputcsv($output, array(
                $ticket->ticket_number,
                $ticket->user_id,
                $ticket->user_login,
                $ticket->first_name ?: '',
                $ticket->last_name ?: '',
                $ticket->display_name ?: '',
                $ticket->user_email,
                $ticket->order_id,
                $ticket->status,
                $ticket->created_at,
                $competition_title
            ));
        }

        fclose($output);
        exit;
    }

    /**
     * Handle bulk ticket export requests
     */
    public function handle_bulk_ticket_export() {
        if (!isset($_GET['action']) || $_GET['action'] !== 'export_all_competition_tickets') {
            return;
        }

        if (!current_user_can('manage_options')) {
            wp_die('Unauthorized access');
        }

        // Verify nonce
        if (!wp_verify_nonce($_GET['_wpnonce'], 'export_all_tickets')) {
            wp_die('Security check failed');
        }

        $this->export_all_competition_tickets();
    }

    /**
     * Export all competition tickets to CSV
     */
    private function export_all_competition_tickets() {
        global $wpdb;

        // Get all competition products
        $competition_products = get_posts(array(
            'post_type' => 'product',
            'meta_query' => array(
                array(
                    'key' => '_is_competition',
                    'value' => 'yes'
                )
            ),
            'posts_per_page' => -1,
            'post_status' => 'publish'
        ));

        if (empty($competition_products)) {
            wp_die('No competition products found');
        }

        // Get all tickets for all competitions
        $competition_ids = wp_list_pluck($competition_products, 'ID');
        $tickets_table = $wpdb->prefix . 'competition_tickets';

        $placeholders = implode(',', array_fill(0, count($competition_ids), '%d'));
        $tickets = $wpdb->get_results($wpdb->prepare(
            "SELECT t.*, u.display_name, u.user_email, u.user_login,
                    um_first.meta_value as first_name,
                    um_last.meta_value as last_name,
                    p.post_title as competition_title
             FROM $tickets_table t
             LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID
             LEFT JOIN {$wpdb->usermeta} um_first ON (u.ID = um_first.user_id AND um_first.meta_key = 'first_name')
             LEFT JOIN {$wpdb->usermeta} um_last ON (u.ID = um_last.user_id AND um_last.meta_key = 'last_name')
             LEFT JOIN {$wpdb->posts} p ON t.competition_id = p.ID
             WHERE t.competition_id IN ($placeholders)
             ORDER BY t.competition_id ASC, t.ticket_number ASC",
            ...$competition_ids
        ));

        if (empty($tickets)) {
            wp_die('No tickets found for any competitions');
        }

        // Set headers for CSV download
        $filename = 'all-competition-tickets-' . date('Y-m-d-H-i-s') . '.csv';
        header('Content-Type: text/csv');
        header('Content-Disposition: attachment; filename="' . $filename . '"');
        header('Pragma: no-cache');
        header('Expires: 0');

        // Open output stream
        $output = fopen('php://output', 'w');

        // Add BOM for Excel compatibility
        fprintf($output, chr(0xEF).chr(0xBB).chr(0xBF));

        // Write CSV header
        fputcsv($output, array(
            'Competition',
            'Competition ID',
            'Ticket Number',
            'User ID',
            'Username',
            'First Name',
            'Last Name',
            'Display Name',
            'Email',
            'Order ID',
            'Status',
            'Purchase Date'
        ));

        // Write ticket data
        foreach ($tickets as $ticket) {
            $full_name = trim(($ticket->first_name ?: '') . ' ' . ($ticket->last_name ?: ''));
            if (empty($full_name)) {
                $full_name = $ticket->display_name ?: $ticket->user_login;
            }

            fputcsv($output, array(
                $ticket->competition_title ?: 'Competition #' . $ticket->competition_id,
                $ticket->competition_id,
                $ticket->ticket_number,
                $ticket->user_id,
                $ticket->user_login,
                $ticket->first_name ?: '',
                $ticket->last_name ?: '',
                $ticket->display_name ?: '',
                $ticket->user_email,
                $ticket->order_id,
                $ticket->status,
                $ticket->created_at
            ));
        }

        fclose($output);
        exit;
    }

    public function maybe_initialize_questions() {
        // Only run once per session to avoid repeated checks
        if (!get_transient('competition_questions_checked')) {
            $this->initialize_default_questions();
            set_transient('competition_questions_checked', true, HOUR_IN_SECONDS);
        }
    }

    /**
     * Create WooCommerce products for existing competitions that don't have them
     */
    private function create_products_for_existing_competitions() {
        if (!class_exists('WooCommerce')) {
            return;
        }

        $competitions = get_posts(array(
            'post_type' => 'competition',
            'post_status' => 'publish',
            'numberposts' => -1,
            'meta_query' => array(
                array(
                    'key' => 'product_id',
                    'compare' => 'NOT EXISTS'
                )
            )
        ));

        foreach ($competitions as $competition) {
            $this->create_competition_product($competition->ID);
        }
    }
    
    public function deactivate() {
        flush_rewrite_rules();
    }
    
    private function create_tables() {
        global $wpdb;

        $charset_collate = $wpdb->get_charset_collate();

        // Create tickets table
        $tickets_table = $wpdb->prefix . 'competition_tickets';
        $sql_tickets = "CREATE TABLE $tickets_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            competition_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            order_id bigint(20) DEFAULT NULL,
            ticket_number varchar(50) NOT NULL,
            answer tinyint(1) DEFAULT NULL,
            status varchar(20) DEFAULT 'active',
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            updated_at datetime DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY ticket_number (ticket_number),
            KEY competition_id (competition_id),
            KEY user_id (user_id),
            KEY order_id (order_id)
        ) $charset_collate;";

        // Create instant wins table
        $instant_wins_table = $wpdb->prefix . 'competition_instant_wins';
        $sql_instant = "CREATE TABLE $instant_wins_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            competition_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            ticket_number mediumint(9) NOT NULL,
            prize_name varchar(255) NOT NULL,
            prize_value decimal(10,2) DEFAULT 0.00,
            prize_type varchar(20) DEFAULT 'site_credit',
            status varchar(20) DEFAULT 'won',
            claimed_at datetime DEFAULT NULL,
            created_at datetime DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY competition_id (competition_id),
            KEY user_id (user_id),
            KEY ticket_number (ticket_number)
        ) $charset_collate;";

        // Create winners table
        $winners_table = $wpdb->prefix . 'competition_winners';
        $sql_winners = "CREATE TABLE $winners_table (
            id mediumint(9) NOT NULL AUTO_INCREMENT,
            competition_id bigint(20) NOT NULL,
            user_id bigint(20) NOT NULL,
            ticket_id mediumint(9) NOT NULL,
            winning_ticket_number varchar(50) NOT NULL,
            selected_at datetime DEFAULT CURRENT_TIMESTAMP,
            notified_at datetime DEFAULT NULL,
            photo_url varchar(500) DEFAULT NULL,
            status varchar(20) DEFAULT 'selected',
            PRIMARY KEY (id),
            UNIQUE KEY competition_id (competition_id),
            KEY user_id (user_id),
            KEY ticket_id (ticket_id)
        ) $charset_collate;";

        require_once(ABSPATH . 'wp-admin/includes/upgrade.php');
        dbDelta($sql_tickets);
        dbDelta($sql_instant);
        dbDelta($sql_winners);
    }

    /**
     * AJAX handler for adding competition tickets to cart
     */
    public function ajax_add_to_cart() {
        // Ensure clean output
        while (ob_get_level()) {
            ob_end_clean();
        }
        ob_start();

        // Check nonce for security (optional but recommended)
        // if (!wp_verify_nonce($_POST['nonce'], 'add_to_cart_nonce')) {
        //     wp_send_json_error('Security check failed');
        //     return;
        // }

        try {
            // Verify we have WooCommerce
            if (!class_exists('WooCommerce')) {
                wp_send_json_error('WooCommerce not available');
                return;
            }

            // Get and validate data with fallback for older PHP versions
            $product_id = isset($_POST['product_id']) ? intval($_POST['product_id']) : 0;
            $quantity = isset($_POST['quantity']) ? intval($_POST['quantity']) : 1;
            $answer = isset($_POST['answer']) ? intval($_POST['answer']) : 0;

            // Debug logging
            error_log('Competition AJAX Debug - Product ID: ' . $product_id . ', Quantity: ' . $quantity . ', Answer: ' . $answer);

            if (!$product_id || !$answer) {
                wp_send_json_error('Missing required data - Product: ' . $product_id . ', Answer: ' . $answer);
                return;
            }

            // Verify product exists and is a competition
            $product = wc_get_product($product_id);
            if (!$product) {
                wp_send_json_error('Product not found');
                return;
            }

            // Check if this is a competition product
            $is_competition = get_post_meta($product_id, '_is_competition', true);
            if ($is_competition !== 'yes') {
                wp_send_json_error('This is not a competition product');
                return;
            }

            // Check if competition is active (not scheduled or ended)
            if (!$this->is_competition_active($product_id)) {
                wp_send_json_error('Competition is not currently active');
                return;
            }

            // Validate answer
            $correct_answer = get_post_meta($product_id, '_competition_correct_answer', true);

            error_log('Answer validation - Submitted: ' . $answer . ', Correct: ' . $correct_answer);

            if ($correct_answer && intval($answer) !== intval($correct_answer)) {
                wp_send_json_error('Incorrect answer to competition question. Please try again.');
                return;
            } else if (!$correct_answer) {
                // If no correct answer is set, allow any answer (fallback)
                error_log('No correct answer set for competition - allowing submission');
            }

            // Check stock availability
            $max_tickets = get_post_meta($product_id, '_competition_max_tickets', true) ?: 100;
            $sold_tickets = get_post_meta($product_id, '_competition_sold_tickets', true) ?: 0;
            $remaining_tickets = $max_tickets - $sold_tickets;

            if ($quantity > $remaining_tickets) {
                wp_send_json_error('Not enough tickets available. Only ' . $remaining_tickets . ' tickets remaining.');
                return;
            }

            // Add custom data to cart item
            $cart_item_data = array(
                'competition_id' => $product_id, // Use product ID as competition ID
                'competition_answer' => $answer,
                'competition_title' => get_the_title($product_id)
            );

            // Add to cart
            error_log('Attempting to add to cart - Product: ' . $product_id . ', Quantity: ' . $quantity);

            // Clear any existing notices
            wc_clear_notices();

            $cart_item_key = WC()->cart->add_to_cart($product_id, $quantity, 0, array(), $cart_item_data);

            error_log('Cart add result: ' . ($cart_item_key ? 'Success - ' . $cart_item_key : 'Failed'));

            if ($cart_item_key) {
                // Don't update sold tickets count here - only update when order is completed
                // The sold_tickets count should only increase after payment is confirmed

                // Simple success response
                wp_send_json_success(array(
                    'message' => 'Tickets added to cart successfully',
                    'cart_item_key' => $cart_item_key
                ));
            } else {
                // Get WooCommerce notices for more detailed error
                $notices = wc_get_notices('error');
                $error_message = 'Failed to add tickets to cart';
                if (!empty($notices)) {
                    $error_message .= ': ' . implode(', ', array_column($notices, 'notice'));
                }
                wp_send_json_error($error_message);
            }

        } catch (Exception $e) {
            error_log('Competition AJAX Exception: ' . $e->getMessage());
            wp_send_json_error('Error: ' . $e->getMessage());
        }

        // Ensure we exit after sending JSON
        wp_die();
    }

    /**
     * AJAX handler to create products for existing competitions
     */
    public function ajax_create_products() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        $this->create_products_for_existing_competitions();
        wp_send_json_success('Products created for existing competitions');
    }

    /**
     * AJAX handler to hide all competition products
     */
    public function ajax_hide_products() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
            return;
        }

        // Get all competition products
        $competition_products = get_posts(array(
            'post_type' => 'product',
            'meta_key' => '_competition_id',
            'meta_compare' => 'EXISTS',
            'numberposts' => -1
        ));

        $updated_count = 0;

        foreach ($competition_products as $product_post) {
            $product = wc_get_product($product_post->ID);
            if ($product) {
                $product->set_status('private');
                $product->set_catalog_visibility('hidden');
                $product->save();
                $updated_count++;
            }
        }

        wp_send_json_success("Updated {$updated_count} competition products to private status");
    }









    /**
     * Hide competition products from shop and category pages
     */
    public function hide_competition_products_from_shop($query) {
        if (!is_admin() && $query->is_main_query()) {
            // Get all competition product IDs
            $competition_products = get_posts(array(
                'post_type' => 'product',
                'meta_key' => '_competition_id',
                'meta_compare' => 'EXISTS',
                'fields' => 'ids',
                'numberposts' => -1
            ));

            if (!empty($competition_products)) {
                $query->set('post__not_in', $competition_products);
            }
        }
    }

    /**
     * Hide competition products from WooCommerce shortcodes
     */
    public function hide_competition_products_from_shortcodes($query_args) {
        // Get all competition product IDs
        $competition_products = get_posts(array(
            'post_type' => 'product',
            'meta_key' => '_competition_id',
            'meta_compare' => 'EXISTS',
            'fields' => 'ids',
            'numberposts' => -1
        ));

        if (!empty($competition_products)) {
            $existing_excludes = isset($query_args['post__not_in']) ? $query_args['post__not_in'] : array();
            $query_args['post__not_in'] = array_merge($existing_excludes, $competition_products);
        }

        return $query_args;
    }

    /**
     * Prevent direct access to competition product pages
     */
    public function prevent_direct_product_access() {
        if (is_product()) {
            global $post;
            $competition_id = get_post_meta($post->ID, '_competition_id', true);

            if ($competition_id) {
                // Redirect to the competition page instead
                $competition_url = get_permalink($competition_id);
                if ($competition_url) {
                    wp_redirect($competition_url);
                    exit;
                } else {
                    // If competition doesn't exist, show 404
                    global $wp_query;
                    $wp_query->set_404();
                    status_header(404);
                }
            }
        }
    }

    /**
     * Hide competition products from all product queries (including widgets)
     */
    public function hide_competition_products_everywhere($query) {
        // Only affect product queries on frontend
        if (is_admin() || !$query->is_main_query()) {
            return;
        }

        // Only affect product post type queries
        if ($query->get('post_type') !== 'product') {
            return;
        }

        // Get all competition product IDs
        $competition_products = get_posts(array(
            'post_type' => 'product',
            'meta_key' => '_competition_id',
            'meta_compare' => 'EXISTS',
            'fields' => 'ids',
            'numberposts' => -1
        ));

        if (!empty($competition_products)) {
            $existing_excludes = $query->get('post__not_in') ?: array();
            $query->set('post__not_in', array_merge($existing_excludes, $competition_products));
        }
    }

    /**
     * Enqueue countdown timer scripts
     */
    public function enqueue_countdown_scripts() {
        // Enqueue on competition pages, competitions listing, home page, and front page
        if (is_singular('competition') ||
            is_page('competitions') ||
            is_home() ||
            is_front_page() ||
            (is_page() && get_page_template_slug() === 'page-competitions.php')) {
            wp_enqueue_script('competition-countdown', plugin_dir_url(__FILE__) . 'countdown.js', array('jquery'), '1.0.0', true);
        }
    }

    /**
     * Generate tickets when order is completed
     */
    public function generate_tickets_on_order_completion($order_id) {
        $this->generate_tickets_for_order($order_id);
    }

    /**
     * Generate tickets when payment is completed
     */
    public function generate_tickets_on_payment_complete($order_id) {
        $this->generate_tickets_for_order($order_id);
    }

    /**
     * Generate tickets for a completed order
     */
    private function generate_tickets_for_order($order_id) {
        global $wpdb;

        $order = wc_get_order($order_id);
        if (!$order) {
            return;
        }

        // Check if tickets already generated for this order
        $tickets_table = $wpdb->prefix . 'competition_tickets';
        $existing_tickets = $wpdb->get_var($wpdb->prepare(
            "SELECT COUNT(*) FROM $tickets_table WHERE order_id = %d",
            $order_id
        ));

        if ($existing_tickets > 0) {
            error_log("Tickets already generated for order $order_id (found $existing_tickets existing tickets)");
            return; // Tickets already generated
        }

        // Add a processing flag to prevent race conditions
        $processing_key = "processing_order_$order_id";
        if (get_transient($processing_key)) {
            error_log("Order $order_id is already being processed");
            return;
        }
        set_transient($processing_key, true, 60); // 60 second lock

        $user_id = $order->get_user_id();
        if (!$user_id) {
            error_log("No user ID found for order $order_id");
            return;
        }

        foreach ($order->get_items() as $item_id => $item) {
            $product_id = $item->get_product_id();
            $quantity = $item->get_quantity();

            error_log("Processing order item: Product ID = $product_id, Quantity = $quantity");

            // Check if this is a competition product
            $is_competition = get_post_meta($product_id, '_is_competition', true);
            error_log("Product $product_id is_competition = '$is_competition'");

            if ($is_competition !== 'yes') {
                error_log("Skipping product $product_id - not a competition");
                continue; // Not a competition product
            }

            // Use the product ID as competition ID for WooCommerce products
            $competition_id = $product_id;
            error_log("Using competition ID = $competition_id for product $product_id");

            // Get competition details
            $max_tickets = get_post_meta($product_id, '_competition_max_tickets', true) ?: 100;
            $sold_tickets = get_post_meta($product_id, '_competition_sold_tickets', true) ?: 0;

            // Generate random ticket numbers
            $generated_tickets = array();

            // Start database transaction to prevent race conditions
            $wpdb->query('START TRANSACTION');

            try {
                // Get already sold ticket numbers for this competition (with lock)
                $existing_tickets = $wpdb->get_col($wpdb->prepare(
                    "SELECT ticket_number FROM $tickets_table WHERE competition_id = %d FOR UPDATE",
                    $competition_id
                ));

                // Create array of available ticket numbers
                $available_tickets = array();
                for ($i = 1; $i <= $max_tickets; $i++) {
                    if (!in_array($i, $existing_tickets)) {
                        $available_tickets[] = $i;
                    }
                }

                // Check if we have enough available tickets
                if (count($available_tickets) < $quantity) {
                    error_log("Not enough available tickets for competition $competition_id. Requested: $quantity, Available: " . count($available_tickets));
                    $quantity = count($available_tickets); // Reduce quantity to available tickets
                }

                // Randomly select ticket numbers
                shuffle($available_tickets);
                $selected_tickets = array_slice($available_tickets, 0, $quantity);

            foreach ($selected_tickets as $ticket_number) {
                // Insert ticket into database
                $result = $wpdb->insert(
                    $tickets_table,
                    array(
                        'competition_id' => $competition_id,
                        'user_id' => $user_id,
                        'order_id' => $order_id,
                        'ticket_number' => $ticket_number,
                        'status' => 'active',
                        'created_at' => current_time('mysql')
                    ),
                    array('%d', '%d', '%d', '%s', '%s', '%s')
                );

                if ($result) {
                    $generated_tickets[] = $ticket_number;
                    error_log("Generated random ticket #$ticket_number for user $user_id, competition $competition_id");
                } else {
                    error_log("Failed to generate ticket for user $user_id, competition $competition_id: " . $wpdb->last_error);
                }
            }

                // Update sold tickets count
                if (!empty($generated_tickets)) {
                    $new_sold_count = $sold_tickets + count($generated_tickets);
                    update_post_meta($competition_id, '_competition_sold_tickets', $new_sold_count);
                    error_log("Updated sold tickets count for competition $competition_id: $new_sold_count");

                    // Update WooCommerce stock
                    $available_tickets = $max_tickets - $new_sold_count;
                    update_post_meta($competition_id, '_stock', $available_tickets);

                    // Update stock status
                    $stock_status = $available_tickets > 0 ? 'instock' : 'outofstock';
                    update_post_meta($competition_id, '_stock_status', $stock_status);

                    // Clear WooCommerce product cache
                    if (function_exists('wc_delete_product_transients')) {
                        wc_delete_product_transients($competition_id);
                    }
                    wp_cache_delete($competition_id, 'posts');
                    wp_cache_delete($competition_id, 'post_meta');

                    // Check for instant wins
                    $this->check_instant_wins($competition_id, $user_id, $generated_tickets);

                    // Add order note
                    $order->add_order_note(sprintf(
                        'Competition tickets generated: %s (Competition: %s)',
                        implode(', ', array_map(function($num) { return "#$num"; }, $generated_tickets)),
                        get_the_title($competition_id)
                    ));
                }

                // Commit the transaction
                $wpdb->query('COMMIT');
                error_log("Successfully generated " . count($generated_tickets) . " tickets for competition $competition_id");

            } catch (Exception $e) {
                // Rollback on error
                $wpdb->query('ROLLBACK');
                error_log("Error generating tickets for competition $competition_id: " . $e->getMessage());
            }
        }

        // Clean up processing lock
        $processing_key = "processing_order_$order_id";
        delete_transient($processing_key);
    }

    /**
     * Check if any generated tickets are instant wins
     */
    private function check_instant_wins($competition_id, $user_id, $ticket_numbers) {
        global $wpdb;

        error_log("Checking instant wins for competition $competition_id, user $user_id, tickets: " . implode(',', $ticket_numbers));

        $instant_wins_enabled = get_post_meta($competition_id, '_instant_wins_enabled', true);
        if (!$instant_wins_enabled) {
            error_log("Instant wins not enabled for competition $competition_id");
            return;
        }

        $instant_win_prizes = get_post_meta($competition_id, '_instant_win_prizes', true) ?: array();
        if (empty($instant_win_prizes)) {
            error_log("No instant win prizes configured for competition $competition_id");
            return;
        }

        error_log("Found " . count($instant_win_prizes) . " instant win prizes for competition $competition_id");

        $instant_wins_table = $wpdb->prefix . 'competition_instant_wins';

        foreach ($instant_win_prizes as $prize) {
            $winning_numbers = $prize['ticket_numbers'] ?? array();
            $won_numbers = array_intersect($ticket_numbers, $winning_numbers);

            foreach ($won_numbers as $winning_number) {
                // Check if this instant win already claimed
                $existing = $wpdb->get_var($wpdb->prepare(
                    "SELECT id FROM $instant_wins_table WHERE competition_id = %d AND ticket_number = %d",
                    $competition_id, $winning_number
                ));

                if (!$existing) {
                    // Record instant win
                    $result = $wpdb->insert(
                        $instant_wins_table,
                        array(
                            'competition_id' => $competition_id,
                            'user_id' => $user_id,
                            'ticket_number' => $winning_number,
                            'prize_name' => $prize['name'],
                            'prize_value' => $prize['value'],
                            'prize_type' => $prize['type'] ?? 'site_credit',
                            'status' => 'won',
                            'created_at' => current_time('mysql')
                        ),
                        array('%d', '%d', '%d', '%s', '%f', '%s', '%s', '%s')
                    );

                    if ($result) {
                        $instant_win_id = $wpdb->insert_id;
                        error_log("Instant win recorded for user $user_id: {$prize['name']} (Ticket #{$winning_number}) - ID: $instant_win_id");

                        // Add wallet credit if it's a site credit prize
                        if (($prize['type'] ?? 'site_credit') === 'site_credit' && function_exists('add_to_user_wallet')) {
                            // Check if wallet credit has already been added for this instant win
                            $credit_key = "instant_win_credit_{$instant_win_id}";
                            if (!get_transient($credit_key)) {
                                // Get wallet balance before adding credit
                                $balance_before = get_user_wallet_balance($user_id);
                                error_log("WALLET DEBUG: User $user_id balance before adding £{$prize['value']}: £$balance_before");

                                $credit_added = add_to_user_wallet($user_id, $prize['value'], "Instant win: {$prize['name']} (Ticket #{$winning_number})");

                                // Get wallet balance after adding credit
                                $balance_after = get_user_wallet_balance($user_id);
                                $actual_increase = $balance_after - $balance_before;
                                error_log("WALLET DEBUG: User $user_id balance after adding £{$prize['value']}: £$balance_after (actual increase: £$actual_increase)");

                                if ($credit_added) {
                                    // Set a transient to prevent duplicate credit additions
                                    set_transient($credit_key, true, DAY_IN_SECONDS);
                                    error_log("Added £{$prize['value']} wallet credit to user $user_id for instant win ID $instant_win_id");
                                } else {
                                    error_log("Failed to add wallet credit to user $user_id for instant win");
                                }
                            } else {
                                error_log("Wallet credit already added for instant win ID $instant_win_id - skipping duplicate");
                            }
                        }
                    } else {
                        error_log("Failed to record instant win for user $user_id, ticket $winning_number");
                    }

                    // Wallet credit is handled above with duplicate prevention - no additional processing needed
                }
            }
        }
    }



    /**
     * Display tickets on order summary page
     */
    public function display_order_tickets($order) {
        global $wpdb;

        $order_id = $order->get_id();
        $tickets_table = $wpdb->prefix . 'competition_tickets';

        // Debug logging
        error_log("TICKET DISPLAY DEBUG: display_order_tickets called for order #$order_id");

        // Get tickets for this order
        $tickets = $wpdb->get_results($wpdb->prepare("
            SELECT t.*, p.post_title as competition_title
            FROM $tickets_table t
            LEFT JOIN {$wpdb->prefix}posts p ON t.competition_id = p.ID
            WHERE t.order_id = %d
            ORDER BY t.competition_id, t.ticket_number
        ", $order_id));

        if (!empty($tickets)) {
            error_log("TICKET DISPLAY DEBUG: Found " . count($tickets) . " tickets for order #$order_id, displaying them");
            echo '<div class="order-tickets-section">';
            echo '<h2>' . __('Your Competition Tickets', 'competition-manager') . '</h2>';

            // Group tickets by competition
            $grouped_tickets = array();
            foreach ($tickets as $ticket) {
                $grouped_tickets[$ticket->competition_id][] = $ticket;
            }

            foreach ($grouped_tickets as $competition_id => $competition_tickets) {
                $competition_title = $competition_tickets[0]->competition_title;
                echo '<div class="competition-tickets">';
                echo '<h3>' . esc_html($competition_title) . '</h3>';
                echo '<div class="ticket-numbers">';
                echo '<p><strong>Your Ticket Numbers:</strong> ';
                $ticket_numbers = array_map(function($t) { return '#' . $t->ticket_number; }, $competition_tickets);
                echo implode(', ', $ticket_numbers);
                echo '</p>';
                echo '</div>';
                echo '</div>';
            }

            echo '</div>';
        } else {
            error_log("TICKET DISPLAY DEBUG: No tickets found for order #$order_id");
        }
    }

    /**
     * Check if a competition is currently active (started and not ended)
     */
    public function is_competition_active($product_id) {
        $is_competition = get_post_meta($product_id, '_is_competition', true);
        if ($is_competition !== 'yes') {
            return false;
        }

        $current_time = current_time('timestamp');

        // Check start date
        $start_date = get_post_meta($product_id, '_competition_start_date', true);
        if ($start_date && strtotime($start_date) > $current_time) {
            return false; // Competition hasn't started yet
        }

        // Check end date
        $end_date = get_post_meta($product_id, '_competition_end_date', true);
        if ($end_date && strtotime($end_date) < $current_time) {
            return false; // Competition has ended
        }

        return true; // Competition is active
    }

    /**
     * Get competition status
     */
    public function get_competition_status($product_id) {
        $is_competition = get_post_meta($product_id, '_is_competition', true);
        if ($is_competition !== 'yes') {
            return 'not_competition';
        }

        // Check if winner has been selected
        $winner_selected = get_post_meta($product_id, 'winner_user_id', true);
        if ($winner_selected) {
            return 'completed'; // Winner has been selected
        }

        $current_time = current_time('timestamp');

        // Check start date
        $start_date = get_post_meta($product_id, '_competition_start_date', true);
        if ($start_date && strtotime($start_date) > $current_time) {
            return 'scheduled'; // Competition is scheduled for future
        }

        // Check if competition is sold out
        $max_tickets = get_post_meta($product_id, '_competition_max_tickets', true);
        $sold_tickets = get_post_meta($product_id, '_competition_sold_tickets', true) ?: 0;

        if ($max_tickets && $sold_tickets >= $max_tickets) {
            return 'awaiting_draw'; // Competition is sold out
        }

        // Check end date
        $end_date = get_post_meta($product_id, '_competition_end_date', true);
        if ($end_date && strtotime($end_date) < $current_time) {
            return 'awaiting_draw'; // Competition has ended, awaiting draw
        }

        return 'active'; // Competition is currently active
    }

    /**
     * AJAX handler to get ticket user details
     */
    public function get_ticket_user_details() {
        check_ajax_referer('add_manual_winner', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $competition_id = intval($_POST['competition_id']);
        $ticket_number = sanitize_text_field($_POST['ticket_number']);

        if (!$competition_id || !$ticket_number) {
            wp_send_json_error('Competition ID and ticket number are required');
        }

        global $wpdb;
        $tickets_table = $wpdb->prefix . 'competition_tickets';

        // Get ticket details - handle both string and integer ticket numbers
        $ticket = $wpdb->get_row($wpdb->prepare(
            "SELECT t.*, u.display_name, u.user_email
             FROM $tickets_table t
             LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID
             WHERE t.competition_id = %d AND (t.ticket_number = %s OR t.ticket_number = %d) AND t.status = 'active'",
            $competition_id,
            $ticket_number,
            intval($ticket_number)
        ));

        if (!$ticket) {
            // Debug: Get all tickets for this competition to help troubleshoot
            $all_tickets = $wpdb->get_col($wpdb->prepare(
                "SELECT ticket_number FROM $tickets_table WHERE competition_id = %d AND status = 'active' ORDER BY ticket_number",
                $competition_id
            ));

            $debug_info = '';
            if (!empty($all_tickets)) {
                $debug_info = ' Available tickets: ' . implode(', ', $all_tickets);
            } else {
                $debug_info = ' No tickets found for this competition.';
            }

            wp_send_json_error('Ticket not found for this competition.' . $debug_info);
        }

        wp_send_json_success(array(
            'ticket_id' => $ticket->id,
            'user_name' => $ticket->display_name,
            'user_email' => $ticket->user_email,
            'user_id' => $ticket->user_id,
            'created_at' => $ticket->created_at
        ));
    }

    /**
     * Test winner email page
     */
    public function test_winner_email_page() {
        ?>
        <div class="wrap">
            <h1>Test Winner Email</h1>
            <p>Test the winner notification email system to ensure it's working correctly.</p>

            <div style="background: #f0f8ff; padding: 15px; border: 1px solid #0073aa; border-radius: 8px; margin-bottom: 20px;">
                <h3>Email System Test</h3>
                <p>This will send a test winner notification email to verify the system is working.</p>

                <form id="test-email-form">
                    <table class="form-table">
                        <tr>
                            <th scope="row">Test Email Address</th>
                            <td>
                                <input type="email" id="test_email" name="test_email" value="<?php echo esc_attr(get_option('admin_email')); ?>" class="regular-text" required>
                                <p class="description">Email address to send the test to (defaults to admin email)</p>
                            </td>
                        </tr>
                        <tr>
                            <th scope="row">Competition</th>
                            <td>
                                <select id="test_competition" name="test_competition" required>
                                    <option value="">Select a competition...</option>
                                    <?php
                                    $competitions = get_posts(array(
                                        'post_type' => 'product',
                                        'posts_per_page' => -1,
                                        'meta_query' => array(
                                            array(
                                                'key' => '_is_competition',
                                                'value' => 'yes',
                                                'compare' => '='
                                            )
                                        )
                                    ));
                                    foreach ($competitions as $comp) {
                                        echo '<option value="' . esc_attr($comp->ID) . '">' . esc_html($comp->post_title) . '</option>';
                                    }
                                    ?>
                                </select>
                            </td>
                        </tr>
                    </table>

                    <p class="submit">
                        <button type="submit" class="button button-primary">Send Test Email</button>
                    </p>
                </form>

                <div id="test-email-results" style="margin-top: 15px;"></div>
            </div>
        </div>

        <script>
        jQuery(document).ready(function($) {
            $('#test-email-form').on('submit', function(e) {
                e.preventDefault();

                const $results = $('#test-email-results');
                const $button = $(this).find('button[type="submit"]');

                $button.prop('disabled', true).text('Sending...');
                $results.html('<p>Sending test email...</p>');

                $.ajax({
                    url: ajaxurl,
                    type: 'POST',
                    data: {
                        action: 'test_winner_email',
                        nonce: '<?php echo wp_create_nonce('test_winner_email'); ?>',
                        test_email: $('#test_email').val(),
                        test_competition: $('#test_competition').val()
                    },
                    success: function(response) {
                        if (response.success) {
                            $results.html('<div style="background: #d4edda; color: #155724; padding: 10px; border-radius: 4px;"><strong>Success:</strong> ' + response.data.message + '</div>');
                        } else {
                            $results.html('<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px;"><strong>Error:</strong> ' + response.data + '</div>');
                        }
                    },
                    error: function() {
                        $results.html('<div style="background: #f8d7da; color: #721c24; padding: 10px; border-radius: 4px;"><strong>Error:</strong> Failed to send test email</div>');
                    },
                    complete: function() {
                        $button.prop('disabled', false).text('Send Test Email');
                    }
                });
            });
        });
        </script>
        <?php
    }

    /**
     * AJAX handler to add manual winner
     */
    public function add_manual_winner() {
        check_ajax_referer('add_manual_winner', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $competition_id = intval($_POST['competition_id']);
        $ticket_number = sanitize_text_field($_POST['ticket_number']);

        if (!$competition_id || !$ticket_number) {
            wp_send_json_error('Competition ID and ticket number are required');
        }

        // Check if winner already exists
        $existing_winner = get_post_meta($competition_id, 'winner_user_id', true);
        if ($existing_winner) {
            wp_send_json_error('Winner already selected for this competition');
        }

        global $wpdb;
        $tickets_table = $wpdb->prefix . 'competition_tickets';
        $winners_table = $wpdb->prefix . 'competition_winners';

        // Get ticket details - handle both string and integer ticket numbers
        $ticket = $wpdb->get_row($wpdb->prepare(
            "SELECT t.*, u.display_name, u.user_email
             FROM $tickets_table t
             LEFT JOIN {$wpdb->users} u ON t.user_id = u.ID
             WHERE t.competition_id = %d AND (t.ticket_number = %s OR t.ticket_number = %d) AND t.status = 'active'",
            $competition_id,
            $ticket_number,
            intval($ticket_number)
        ));

        if (!$ticket) {
            // Debug: Get all tickets for this competition to help troubleshoot
            $all_tickets = $wpdb->get_col($wpdb->prepare(
                "SELECT ticket_number FROM $tickets_table WHERE competition_id = %d AND status = 'active' ORDER BY ticket_number",
                $competition_id
            ));

            $debug_info = '';
            if (!empty($all_tickets)) {
                $debug_info = ' Available tickets: ' . implode(', ', $all_tickets);
            } else {
                $debug_info = ' No tickets found for this competition.';
            }

            wp_send_json_error('Ticket not found for this competition.' . $debug_info);
        }

        // Record the winner in the winners table
        $result = $wpdb->insert(
            $winners_table,
            array(
                'competition_id' => $competition_id,
                'user_id' => $ticket->user_id,
                'ticket_id' => $ticket->id,
                'winning_ticket_number' => $ticket_number,
                'selected_at' => current_time('mysql'),
                'status' => 'selected'
            ),
            array('%d', '%d', '%d', '%s', '%s', '%s')
        );

        if (!$result) {
            wp_send_json_error('Failed to record winner in database');
        }

        $winner_db_id = $wpdb->insert_id;

        // Update competition meta
        update_post_meta($competition_id, 'status', 'completed');
        update_post_meta($competition_id, 'winner_user_id', $ticket->user_id);
        update_post_meta($competition_id, 'winner_username', $ticket->display_name);
        update_post_meta($competition_id, 'winner_ticket_id', $ticket->id);
        update_post_meta($competition_id, 'winner_ticket_number', $ticket_number);

        // Create winner post for public display
        $competition = get_post($competition_id);
        $winner_post_id = wp_insert_post(array(
            'post_title' => $ticket->display_name . ' - ' . $competition->post_title,
            'post_content' => 'Winner of ' . $competition->post_title . ' with ticket number ' . $ticket_number,
            'post_status' => 'publish',
            'post_type' => 'winner',
            'meta_input' => array(
                'competition_id' => $competition_id,
                'winner_user_id' => $ticket->user_id,
                'winning_ticket_number' => $ticket_number,
                'winner_db_id' => $winner_db_id
            )
        ));

        // Send winner notification email
        $email_sent = false;
        $email_method = 'none';

        // Try WooCommerce email system first
        if (function_exists('send_winner_notification_email_wc')) {
            error_log('Sending winner notification via WooCommerce email system for manual winner');
            $email_sent = send_winner_notification_email_wc($ticket->user_id, $competition_id, $ticket_number);
            $email_method = 'woocommerce';
        } elseif (class_exists('Competition_WooCommerce_Email_Integration')) {
            error_log('Sending winner notification via Competition_WooCommerce_Email_Integration for manual winner');
            $email_sent = Competition_WooCommerce_Email_Integration::send_winner_notification($ticket->user_id, $competition_id, $ticket_number);
            $email_method = 'integration';
        } else {
            // Fallback to basic email
            error_log('Sending winner notification via basic email for manual winner');
            $subject = 'Congratulations! You won the competition!';
            $message = sprintf(
                "Hi %s,\n\nCongratulations! You've won the competition '%s' with ticket number %s!\n\nWe'll be in touch soon with details on how to claim your prize.\n\nBest regards,\nThe Competition Team",
                $ticket->display_name,
                $competition->post_title,
                $ticket_number
            );
            $email_sent = wp_mail($ticket->user_email, $subject, $message);
            $email_method = 'basic';
        }

        // Log email result
        if ($email_sent) {
            error_log('Winner notification email sent successfully via ' . $email_method . ' method');
        } else {
            error_log('Failed to send winner notification email via ' . $email_method . ' method');
        }

        wp_send_json_success(array(
            'message' => 'Winner added successfully! ' . $ticket->display_name . ' has been set as the winner for ticket number ' . $ticket_number . ($email_sent ? ' Winner notification email sent.' : ' Warning: Email notification failed to send.'),
            'winner_id' => $winner_db_id,
            'winner_post_id' => $winner_post_id,
            'winner_name' => $ticket->display_name,
            'email_sent' => $email_sent,
            'email_method' => $email_method
        ));
    }

    /**
     * AJAX handler for testing winner email
     */
    public function test_winner_email() {
        check_ajax_referer('test_winner_email', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        $test_email = sanitize_email($_POST['test_email']);
        $competition_id = intval($_POST['test_competition']);

        if (!$test_email || !$competition_id) {
            wp_send_json_error('Email address and competition are required');
        }

        // Create a test user ID (use admin user)
        $admin_user = get_user_by('email', get_option('admin_email'));
        $test_user_id = $admin_user ? $admin_user->ID : 1;
        $test_ticket_number = 'TEST-001';

        // Get competition details
        $competition = get_post($competition_id);
        if (!$competition) {
            wp_send_json_error('Competition not found');
        }

        $email_sent = false;
        $email_method = 'none';
        $error_message = '';

        // Try WooCommerce email system first
        if (function_exists('send_winner_notification_email_wc')) {
            error_log('Testing winner notification via WooCommerce email system');
            try {
                $email_sent = send_winner_notification_email_wc($test_user_id, $competition_id, $test_ticket_number);
                $email_method = 'woocommerce';
            } catch (Exception $e) {
                $error_message = $e->getMessage();
                error_log('WooCommerce email test failed: ' . $error_message);
            }
        } elseif (class_exists('Competition_WooCommerce_Email_Integration')) {
            error_log('Testing winner notification via Competition_WooCommerce_Email_Integration');
            try {
                $email_sent = Competition_WooCommerce_Email_Integration::send_winner_notification($test_user_id, $competition_id, $test_ticket_number);
                $email_method = 'integration';
            } catch (Exception $e) {
                $error_message = $e->getMessage();
                error_log('Integration email test failed: ' . $error_message);
            }
        } else {
            // Fallback to basic email
            error_log('Testing winner notification via basic email');
            $subject = 'TEST: Competition Winner Notification';
            $message = sprintf(
                "This is a TEST email.\n\nHi %s,\n\nCongratulations! You've won the competition '%s' with ticket number %s!\n\nThis was a test of the winner notification system.\n\nBest regards,\nThe Competition Team",
                $admin_user->display_name,
                $competition->post_title,
                $test_ticket_number
            );

            try {
                $email_sent = wp_mail($test_email, $subject, $message);
                $email_method = 'basic';
            } catch (Exception $e) {
                $error_message = $e->getMessage();
                error_log('Basic email test failed: ' . $error_message);
            }
        }

        if ($email_sent) {
            wp_send_json_success(array(
                'message' => 'Test email sent successfully via ' . $email_method . ' method to ' . $test_email,
                'email_method' => $email_method,
                'competition_title' => $competition->post_title
            ));
        } else {
            wp_send_json_error('Failed to send test email via ' . $email_method . ' method. ' . $error_message);
        }
    }

    /**
     * AJAX handler for debugging database status
     */
    public function debug_competition_database() {
        check_ajax_referer('add_manual_winner', 'nonce');

        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        global $wpdb;
        $tickets_table = $wpdb->prefix . 'competition_tickets';

        $debug_info = '<h4>Database Status:</h4>';

        // Check if table exists
        $table_exists = $wpdb->get_var("SHOW TABLES LIKE '$tickets_table'");
        $debug_info .= '<p><strong>Tickets Table:</strong> ' . ($table_exists ? '✅ EXISTS' : '❌ MISSING') . '</p>';

        if ($table_exists) {
            // Total tickets count
            $total_tickets = $wpdb->get_var("SELECT COUNT(*) FROM $tickets_table");
            $debug_info .= '<p><strong>Total Tickets:</strong> ' . $total_tickets . '</p>';

            // Tickets by competition
            $competitions = $wpdb->get_results("
                SELECT competition_id,
                       COUNT(*) as ticket_count,
                       GROUP_CONCAT(ticket_number ORDER BY CAST(ticket_number AS UNSIGNED)) as ticket_numbers
                FROM $tickets_table
                GROUP BY competition_id
                ORDER BY competition_id
            ");

            if (!empty($competitions)) {
                $debug_info .= '<h4>Tickets by Competition:</h4>';
                $debug_info .= '<table border="1" style="border-collapse: collapse; width: 100%; font-size: 12px;">';
                $debug_info .= '<tr><th>Competition ID</th><th>Ticket Count</th><th>Ticket Numbers</th></tr>';

                foreach ($competitions as $comp) {
                    $debug_info .= '<tr>';
                    $debug_info .= '<td>' . esc_html($comp->competition_id) . '</td>';
                    $debug_info .= '<td>' . esc_html($comp->ticket_count) . '</td>';
                    $debug_info .= '<td>' . esc_html($comp->ticket_numbers) . '</td>';
                    $debug_info .= '</tr>';
                }
                $debug_info .= '</table>';
            } else {
                $debug_info .= '<p><em>No tickets found in database.</em></p>';
            }

            // Recent orders with competition products
            $recent_orders = $wpdb->get_results("
                SELECT o.ID as order_id, o.post_status, o.post_date,
                       oi.order_item_name, pm.meta_value as product_id
                FROM {$wpdb->posts} o
                LEFT JOIN {$wpdb->prefix}woocommerce_order_items oi ON o.ID = oi.order_id
                LEFT JOIN {$wpdb->prefix}woocommerce_order_itemmeta pm ON oi.order_item_id = pm.order_item_id
                LEFT JOIN {$wpdb->postmeta} comp_meta ON pm.meta_value = comp_meta.post_id
                WHERE o.post_type = 'shop_order'
                AND pm.meta_key = '_product_id'
                AND comp_meta.meta_key = '_is_competition'
                AND comp_meta.meta_value = 'yes'
                ORDER BY o.post_date DESC
                LIMIT 5
            ");

            if (!empty($recent_orders)) {
                $debug_info .= '<h4>Recent Competition Orders:</h4>';
                $debug_info .= '<table border="1" style="border-collapse: collapse; width: 100%; font-size: 12px;">';
                $debug_info .= '<tr><th>Order ID</th><th>Status</th><th>Date</th><th>Product</th></tr>';

                foreach ($recent_orders as $order) {
                    $debug_info .= '<tr>';
                    $debug_info .= '<td>' . esc_html($order->order_id) . '</td>';
                    $debug_info .= '<td>' . esc_html($order->post_status) . '</td>';
                    $debug_info .= '<td>' . esc_html($order->post_date) . '</td>';
                    $debug_info .= '<td>' . esc_html($order->order_item_name) . '</td>';
                    $debug_info .= '</tr>';
                }
                $debug_info .= '</table>';
            } else {
                $debug_info .= '<p><em>No recent competition orders found.</em></p>';
            }

            // Check specific competition status (ID 124)
            $debug_info .= '<h4>Competition ID 124 Status Check:</h4>';
            $comp_124_max = get_post_meta(124, '_competition_max_tickets', true);
            $comp_124_sold = get_post_meta(124, '_competition_sold_tickets', true) ?: 0;
            $comp_124_status = get_competition_status_simple(124);
            $comp_124_end_date = get_post_meta(124, '_competition_end_date', true);

            $debug_info .= '<p><strong>Max Tickets:</strong> ' . $comp_124_max . '</p>';
            $debug_info .= '<p><strong>Sold Tickets:</strong> ' . $comp_124_sold . '</p>';
            $debug_info .= '<p><strong>Current Status:</strong> ' . $comp_124_status . '</p>';
            $debug_info .= '<p><strong>End Date:</strong> ' . ($comp_124_end_date ?: 'Not set') . '</p>';
            $debug_info .= '<p><strong>Is Sold Out:</strong> ' . (($comp_124_max && $comp_124_sold >= $comp_124_max) ? 'YES' : 'NO') . '</p>';
        }

        wp_send_json_success($debug_info);
    }

    /**
     * Flush rewrite rules on plugin activation
     */
    public function flush_rewrite_rules_on_activation() {
        // Register post types first
        $this->register_post_types();

        // Flush rewrite rules to ensure custom post type URLs work
        flush_rewrite_rules();
    }

    /**
     * Clear email cache on plugin activation
     */
    public function clear_email_cache_on_activation() {
        // Clear email cache
        delete_transient('woocommerce_emails');
        delete_transient('wc_emails');

        // Clear any WooCommerce caches
        if (function_exists('wc_delete_shop_order_transients')) {
            wc_delete_shop_order_transients();
        }

        // Trigger action for email integration
        do_action('competition_manager_activated');

        error_log('Competition Manager email cache cleared on activation');
    }

    /**
     * AJAX handler to flush rewrite rules manually
     */
    public function flush_winner_rewrite_rules() {
        if (!current_user_can('manage_options')) {
            wp_send_json_error('Insufficient permissions');
        }

        // Register post types first
        $this->register_post_types();

        // Flush rewrite rules
        flush_rewrite_rules();

        wp_send_json_success('Rewrite rules flushed successfully. The /winners/ page should now work.');
    }
}

// Initialize the plugin
new CompetitionManager();

// Initialize additional managers if classes exist
// Removed old Instant_Win_Manager - functionality moved to main CompetitionManager class
if (class_exists('Winner_Manager')) {
    new Winner_Manager();
}
if (class_exists('Competition_Ajax_Handlers')) {
    new Competition_Ajax_Handlers();
}
// Competition_WooCommerce_Integration disabled - main CompetitionManager handles all functionality
