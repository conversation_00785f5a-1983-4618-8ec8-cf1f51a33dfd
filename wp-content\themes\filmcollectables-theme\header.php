<!DOCTYPE html>
<html <?php language_attributes(); ?>>
<head>
    <meta charset="<?php bloginfo('charset'); ?>">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title><?php wp_title('|', true, 'right'); ?><?php bloginfo('name'); ?></title>

    <!-- Meta Pixel Code -->
    <script>
    !function(f,b,e,v,n,t,s)
    {if(f.fbq)return;n=f.fbq=function(){n.callMethod?
    n.callMethod.apply(n,arguments):n.queue.push(arguments)};
    if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
    n.queue=[];t=b.createElement(e);t.async=!0;
    t.src=v;s=b.getElementsByTagName(e)[0];
    s.parentNode.insertBefore(t,s)}(window, document,'script',
    'https://connect.facebook.net/en_US/fbevents.js');
    fbq('init', '2530248027326868');
    fbq('track', 'PageView');
    </script>
    <noscript><img height="1" width="1" style="display:none"
    src="https://www.facebook.com/tr?id=2530248027326868&ev=PageView&noscript=1"
    /></noscript>
    <!-- End Meta Pixel Code -->

    <?php wp_head(); ?>
</head>

<body <?php body_class(); ?>>

<!-- Header -->
<header class="site-header">
    <div class="header-content">
        <!-- Left Section: Logo -->
        <div class="header-left">
            <a href="<?php echo esc_url(home_url('/')); ?>" class="logo">
                <?php
                // Check if custom logo is set
                if (has_custom_logo()) {
                    the_custom_logo();
                } else {
                    // Fallback to site name
                    bloginfo('name');
                }
                ?>
            </a>
        </div>

        <!-- Center Section: Navigation (Desktop) -->
        <nav class="main-nav desktop-nav">
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_class'     => 'nav-menu',
                'container'      => false,
                'fallback_cb'    => 'filmcollectables_fallback_menu',
            ));
            ?>
        </nav>

        <!-- Right Section: User Actions -->
        <div class="header-right">
            <!-- User Account -->
            <?php if (is_user_logged_in()) : ?>
                <a href="<?php echo esc_url(get_permalink(get_option('woocommerce_myaccount_page_id'))); ?>" class="header-link">
                    My Account
                </a>

                <!-- Wallet -->
                <?php
                $user_id = get_current_user_id();
                $wallet_balance = get_user_wallet_balance($user_id);

                // Temporary debug - remove after fixing
                if (current_user_can('administrator')) {
                    debug_wallet_balance($user_id);
                }
                ?>
                <a href="<?php echo esc_url(home_url('/wallet/')); ?>" class="header-link">
                    Wallet (£<?php echo number_format($wallet_balance, 2); ?>)
                </a>
            <?php else : ?>
                <a href="<?php echo esc_url(home_url('/login/')); ?>" class="header-link">Login</a>
                <a href="<?php echo esc_url(home_url('/register/')); ?>" class="header-link">Register</a>
            <?php endif; ?>

            <!-- Cart Icon -->
            <?php if (class_exists('WooCommerce')) : ?>
                <a href="<?php echo esc_url(wc_get_cart_url()); ?>" class="cart-icon" id="cart-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="9" cy="21" r="1"></circle>
                        <circle cx="20" cy="21" r="1"></circle>
                        <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"></path>
                    </svg>
                    <span class="cart-count"><?php echo WC()->cart->get_cart_contents_count(); ?></span>
                    <span class="cart-total"><?php echo WC()->cart->get_cart_subtotal(); ?></span>
                </a>
            <?php endif; ?>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobile-menu-toggle" aria-label="Toggle mobile menu">
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
                <span class="hamburger-line"></span>
            </button>
        </div>
    </div>

    <!-- Mobile Navigation Overlay -->
    <div class="mobile-nav-overlay" id="mobile-nav-overlay"></div>

    <!-- Mobile Navigation Menu -->
    <nav class="mobile-nav" id="mobile-nav">
        <div class="mobile-nav-header">
            <h3>Menu</h3>
            <button class="mobile-nav-close" id="mobile-nav-close" aria-label="Close mobile menu">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
            </button>
        </div>

        <div class="mobile-nav-content">
            <!-- Mobile Navigation Menu -->
            <?php
            wp_nav_menu(array(
                'theme_location' => 'primary',
                'menu_class'     => 'mobile-nav-menu',
                'container'      => false,
                'fallback_cb'    => 'filmcollectables_fallback_menu',
            ));
            ?>

            <!-- Mobile User Actions -->
            <div class="mobile-nav-actions">
                <?php if (is_user_logged_in()) : ?>
                    <a href="<?php echo esc_url(get_permalink(get_option('woocommerce_myaccount_page_id'))); ?>" class="mobile-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                        </svg>
                        My Account
                    </a>

                    <a href="<?php echo esc_url(home_url('/wallet/')); ?>" class="mobile-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                            <line x1="1" y1="10" x2="23" y2="10"></line>
                        </svg>
                        Wallet (£<?php echo number_format($wallet_balance, 2); ?>)
                    </a>
                <?php else : ?>
                    <a href="<?php echo esc_url(home_url('/login/')); ?>" class="mobile-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                            <polyline points="10,17 15,12 10,7"></polyline>
                            <line x1="15" y1="12" x2="3" y2="12"></line>
                        </svg>
                        Login
                    </a>
                    <a href="<?php echo esc_url(home_url('/register/')); ?>" class="mobile-nav-link">
                        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                            <circle cx="8.5" cy="7" r="4"></circle>
                            <line x1="20" y1="8" x2="20" y2="14"></line>
                            <line x1="23" y1="11" x2="17" y2="11"></line>
                        </svg>
                        Register
                    </a>
                <?php endif; ?>
            </div>
        </div>
    </nav>
</header>


