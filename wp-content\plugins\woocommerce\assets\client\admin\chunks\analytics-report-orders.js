"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[7202],{68224:(e,t,r)=>{r.d(t,{A:()=>y});var o=r(27723),s=r(86087),a=r(29491),n=r(47143),i=r(96476),l=r(98846),c=r(43577),u=r(40314),m=r(77374),d=r(83306),p=r(94111),_=r(39793);class h extends s.Component{formatVal(e,t){const{formatAmount:r,getCurrencyConfig:o}=this.context;return"currency"===t?r(e):(0,c.formatValue)(o(),t,e)}getValues(e,t){const{emptySearchResults:r,summaryData:o}=this.props,{totals:s}=o,a=s.primary?s.primary[e]:0,n=s.secondary?s.secondary[e]:0,i=r?0:a,l=r?0:n;return{delta:(0,c.calculateDelta)(i,l),prevValue:this.formatVal(l,t),value:this.formatVal(i,t)}}render(){const{charts:e,query:t,selectedChart:r,summaryData:s,endpoint:a,report:n,defaultDateRange:c}=this.props,{isError:u,isRequesting:p}=s;if(u)return(0,_.jsx)(l.AnalyticsError,{});if(p)return(0,_.jsx)(l.SummaryListPlaceholder,{numberOfItems:e.length});const{compare:h}=(0,m.getDateParamsFromQuery)(t,c);return(0,_.jsx)(l.SummaryList,{children:({onToggle:t})=>e.map((e=>{const{key:s,order:c,orderby:u,label:m,type:p,isReverseTrend:y,labelTooltipText:b}=e,g={chart:s};u&&(g.orderby=u),c&&(g.order=c);const w=(0,i.getNewPath)(g),f=r.key===s,{delta:v,prevValue:C,value:k}=this.getValues(s,p);return(0,_.jsx)(l.SummaryNumber,{delta:v,href:w,label:m,reverseTrend:y,prevLabel:"previous_period"===h?(0,o.__)("Previous period:","woocommerce"):(0,o.__)("Previous year:","woocommerce"),prevValue:C,selected:f,value:k,labelTooltipText:b,onLinkClickCallback:()=>{t&&t(),(0,d.recordEvent)("analytics_chart_tab_click",{report:n||a,key:s})}},s)}))})}}h.defaultProps={summaryData:{totals:{primary:{},secondary:{}},isError:!1}},h.contextType=p.CurrencyContext;const y=(0,a.compose)((0,n.withSelect)(((e,t)=>{const{charts:r,endpoint:o,limitProperties:s,query:a,filters:n,advancedFilters:i}=t,l=s||[o],c=l.some((e=>a[e]&&a[e].length));if(a.search&&!c)return{emptySearchResults:!0};const m=r&&r.map((e=>e.key)),{woocommerce_default_date_range:d}=e(u.settingsStore).getSetting("wc_admin","wcAdminSettings");return{summaryData:(0,u.getSummaryNumbers)({endpoint:o,query:a,select:e,limitBy:l,filters:n,advancedFilters:i,defaultDateRange:d,fields:m}),defaultDateRange:d}})))(h)},30457:(e,t,r)=>{r.r(t),r.d(t,{default:()=>C});var o=r(86087),s=r(27723),a=r(13560),n=r(95272),i=r(66087),l=r(98846),c=r(43577),u=r(96476),m=r(77374),d=r(94111),p=r(97605),_=r(56109),h=r(39793);class y extends o.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,s.__)("Date","woocommerce"),key:"date",required:!0,defaultSort:!0,isLeftAligned:!0,isSortable:!0},{label:(0,s.__)("Order #","woocommerce"),screenReaderLabel:(0,s.__)("Order Number","woocommerce"),key:"order_number",required:!0},{label:(0,s.__)("Status","woocommerce"),key:"status",required:!1,isSortable:!1},{label:(0,s.__)("Customer","woocommerce"),key:"customer_id",required:!1,isSortable:!1},{label:(0,s.__)("Customer type","woocommerce"),key:"customer_type",required:!1,isSortable:!1},{label:(0,s.__)("Product(s)","woocommerce"),screenReaderLabel:(0,s.__)("Products","woocommerce"),key:"products",required:!1,isSortable:!1},{label:(0,s.__)("Items sold","woocommerce"),key:"num_items_sold",required:!1,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Coupon(s)","woocommerce"),screenReaderLabel:(0,s.__)("Coupons","woocommerce"),key:"coupons",required:!1,isSortable:!1},{label:(0,s.__)("Net sales","woocommerce"),screenReaderLabel:(0,s.__)("Net sales","woocommerce"),key:"net_total",required:!0,isSortable:!0,isNumeric:!0},{label:(0,s.__)("Attribution","woocommerce"),screenReaderLabel:(0,s.__)("Attribution","woocommerce"),key:"attribution",required:!1,isSortable:!1}]}getCustomerName(e){const{first_name:t,last_name:r}=e||{};return t||r?[t,r].join(" "):""}getRowsContent(e){const{query:t}=this.props,r=(0,u.getPersistedQuery)(t),o=(0,_.Qk)("dateFormat",m.defaultTableDateFormat),{render:a,getCurrencyConfig:n}=this.context;return(0,i.map)(e,(e=>{const{currency:t,date:i,net_total:m,num_items_sold:d,order_id:p,order_number:y,parent_id:b,status:g,customer_type:w}=e,f=e.extended_info||{},{coupons:v,customer:C,products:k}=f,x=k.sort(((e,t)=>t.quantity-e.quantity)).map((e=>({label:e.name,quantity:e.quantity,href:(0,u.getNewPath)(r,"/analytics/products",{filter:"single_product",products:e.id})}))),S=v.map((e=>({label:e.code,href:(0,u.getNewPath)(r,"/analytics/coupons",{filter:"single_coupon",coupons:e.id})})));return[{display:(0,h.jsx)(l.Date,{date:i,visibleFormat:o}),value:i},{display:(0,h.jsx)(l.Link,{href:"post.php?post="+(b||p)+"&action=edit"+(b?"#order_refunds":""),type:"wp-admin",children:y}),value:y},{display:(0,h.jsx)(l.OrderStatus,{className:"woocommerce-orders-table__status",order:{status:g},labelPositionToLeft:!0,orderStatusMap:(0,_.Qk)("orderStatuses",{})}),value:g},{display:this.getCustomerName(C),value:this.getCustomerName(C)},{display:(q=w,q.charAt(0).toUpperCase()+q.slice(1)),value:w},{display:this.renderList(x.length?[x[0]]:[],x.map((e=>({label:(0,s.sprintf)((0,s.__)("%1$s× %2$s","woocommerce"),e.quantity,e.label),href:e.href})))),value:x.map((({quantity:e,label:t})=>(0,s.sprintf)((0,s.__)("%1$s× %2$s","woocommerce"),e,t))).join(", ")},{display:(0,c.formatValue)(n(),"number",d),value:d},{display:this.renderList(S.length?[S[0]]:[],S),value:S.map((e=>e.label)).join(", ")},{display:a(m,t),value:m},{display:f.attribution.origin,value:f.attribution.origin}];var q}))}getSummary(e){const{orders_count:t=0,total_customers:r=0,products:o=0,num_items_sold:a=0,coupons_count:n=0,net_revenue:i=0}=e,{formatAmount:l,getCurrencyConfig:u}=this.context,m=u();return[{label:(0,s._n)("Order","Orders",t,"woocommerce"),value:(0,c.formatValue)(m,"number",t)},{label:(0,s._n)(" Customer"," Customers",r,"woocommerce"),value:(0,c.formatValue)(m,"number",r)},{label:(0,s._n)("Product","Products",o,"woocommerce"),value:(0,c.formatValue)(m,"number",o)},{label:(0,s._n)("Item sold","Items sold",a,"woocommerce"),value:(0,c.formatValue)(m,"number",a)},{label:(0,s._n)("Coupon","Coupons",n,"woocommerce"),value:(0,c.formatValue)(m,"number",n)},{label:(0,s.__)("net sales","woocommerce"),value:l(i)}]}renderLinks(e=[]){return e.map(((e,t)=>(0,h.jsx)(l.Link,{href:e.href,type:"wc-admin",children:e.label},t)))}renderList(e,t){return(0,h.jsxs)(o.Fragment,{children:[this.renderLinks(e),t.length>1&&(0,h.jsx)(l.ViewMoreList,{items:this.renderLinks(t)})]})}render(){const{query:e,filters:t,advancedFilters:r}=this.props;return(0,h.jsx)(p.A,{endpoint:"orders",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,getSummary:this.getSummary,summaryFields:["orders_count","total_customers","products","num_items_sold","coupons_count","net_revenue"],query:e,tableQuery:{extended_info:!0},title:(0,s.__)("Orders","woocommerce"),columnPrefsKey:"orders_report_columns",filters:t,advancedFilters:r})}}y.contextType=d.CurrencyContext;const b=y;var g=r(55737),w=r(68224),f=r(88711),v=r(84179);class C extends o.Component{render(){const{path:e,query:t}=this.props;return(0,h.jsxs)(o.Fragment,{children:[(0,h.jsx)(f.A,{query:t,path:e,filters:a.uW,advancedFilters:a.Qc,report:"orders"}),(0,h.jsx)(w.A,{charts:a.eg,endpoint:"orders",query:t,selectedChart:(0,n.A)(t.chart,a.eg),filters:a.uW,advancedFilters:a.Qc}),(0,h.jsx)(g.A,{charts:a.eg,endpoint:"orders",path:e,query:t,selectedChart:(0,n.A)(t.chart,a.eg),filters:a.uW,advancedFilters:a.Qc}),(0,h.jsx)(b,{query:t,filters:a.uW,advancedFilters:a.Qc}),(0,h.jsx)(v.F,{optionName:"woocommerce_orders_report_date_tour_shown",headingText:(0,s.__)("Orders are now reported based on the payment dates ✅","woocommerce")})]})}}},84179:(e,t,r)=>{r.d(t,{F:()=>m});var o=r(98846),s=r(27723),a=r(40314),n=r(86087),i=r(47143),l=r(15703),c=r(39793);const u="woocommerce_date_type",m=({optionName:e,headingText:t})=>{const[r,m]=(0,n.useState)(!1),{updateOptions:d}=(0,i.useDispatch)(a.optionsStore),{shouldShowTour:p,isResolving:_}=(0,i.useSelect)((t=>{const{getOption:r,hasFinishedResolution:o}=t(a.optionsStore);return{shouldShowTour:"yes"!==r(e)&&!1===r(u),isResolving:!o("getOption",[e])||!o("getOption",[u])}}),[e]);if(r||!p||_)return null;const h={steps:[{referenceElements:{desktop:".woocommerce-filters-filter > .components-dropdown"},focusElement:{desktop:".woocommerce-filters-filter > .components-dropdown"},meta:{name:"product-feedback-",heading:t,descriptions:{desktop:(0,n.createInterpolateElement)((0,s.__)("We now collect orders in this table based on when the payment went through, rather than when they were placed. You can change this in <link>settings</link>.","woocommerce"),{link:(0,n.createElement)("a",{href:(0,l.getAdminLink)("admin.php?page=wc-admin&path=/analytics/settings"),"aria-label":(0,s.__)("Analytics date settings","woocommerce")})})},primaryButton:{text:(0,s.__)("Got it","woocommerce")}},options:{classNames:{desktop:"woocommerce-revenue-report-date-tour"}}}],closeHandler:()=>{d({[e]:"yes"}),m(!0)}};return(0,c.jsx)(o.TourKit,{config:h})}},95272:(e,t,r)=>{r.d(t,{A:()=>s});var o=r(66087);function s(e,t=[]){return(0,o.find)(t,{key:e})||t[0]}}}]);