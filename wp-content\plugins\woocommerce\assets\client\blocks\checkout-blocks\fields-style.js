"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[8330],{61259:(c,e,o)=>{o.r(e),o.d(e,{default:()=>h});var s=o(4921),l=o(27723),t=o(92902),r=o(87052),a=o(86087),k=o(94199),n=o(10790);const h=({children:c,className:e})=>{const{dispatchCheckoutEvent:o}=(0,r.y)(),{showFormStepNumbers:h}=(0,k.O)();return(0,a.useEffect)((()=>{o("render-checkout-form")}),[]),(0,n.jsx)(t.A,{className:(0,s.A)("wc-block-checkout__main",e),children:(0,n.jsx)("form",{"aria-label":(0,l.__)("Checkout","woocommerce"),className:(0,s.A)("wc-block-components-form wc-block-checkout__form",{"wc-block-checkout__form--with-step-numbers":h}),children:c})})}}}]);