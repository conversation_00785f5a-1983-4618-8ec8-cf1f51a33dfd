{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-catalog-visibility-field", "description": "A checkbox to manage the catalog visibility of the product.", "title": "Product catalog visibility", "category": "widgets", "keywords": ["products", "catalog"], "textdomain": "default", "attributes": {"label": {"type": "string", "role": "content"}, "visibility": {"type": "string", "enum": ["visible", "catalog", "search", "hidden"], "default": "visible"}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": false, "inserter": false, "lock": false, "__experimentalToolbar": false}}