"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[9837],{34247:(s,c,e)=>{e.r(c),e.d(c,{default:()=>o});var a=e(81228),l=e(70910),n=e(2328),r=e(71337),t=e(10790);const o=({children:s,className:c=""})=>{const{cartTotals:e}=(0,n.V)(),o=(0,l.getCurrencyFromPriceResponse)(e);return(0,t.jsxs)("div",{className:c,children:[s,(0,t.jsx)("div",{className:"wc-block-components-totals-wrapper",children:(0,t.jsx)(a.Ay,{currency:o,values:e,isEstimate:!0})}),(0,t.jsx)(r.X,{})]})}}}]);