"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[2791],{88711:(e,t,r)=>{r.d(t,{A:()=>g});var a=r(86087),o=r(29491),s=r(66087),n=r(47143),i=r(27752),c=r(98846),l=r(40314),d=r(77374),u=r(83306),m=r(94111),p=r(56109),h=r(39793);class y extends a.Component{constructor(){super(),this.onDateSelect=this.onDateSelect.bind(this),this.onFilterSelect=this.onFilterSelect.bind(this),this.onAdvancedFilterAction=this.onAdvancedFilterAction.bind(this)}onDateSelect(e){const{report:t,addCesSurveyForAnalytics:r}=this.props;r(),(0,u.recordEvent)("datepicker_update",{report:t,...(0,s.omitBy)(e,s.isUndefined)})}onFilterSelect(e){const{report:t,addCesSurveyForAnalytics:r}=this.props,a=e.filter||e["filter-variations"];["single_product","single_category","single_coupon","single_variation"].includes(a)&&r();const o={report:t,filter:e.filter||"all"};"single_product"===e.filter&&(o.filter_variation=e["filter-variations"]||"all"),(0,u.recordEvent)("analytics_filter",o)}onAdvancedFilterAction(e,t){const{report:r,addCesSurveyForAnalytics:a}=this.props;switch(e){case"add":(0,u.recordEvent)("analytics_filters_add",{report:r,filter:t.key});break;case"remove":(0,u.recordEvent)("analytics_filters_remove",{report:r,filter:t.key});break;case"filter":const e=Object.keys(t).reduce(((e,r)=>(e[(0,s.snakeCase)(r)]=t[r],e)),{});a(),(0,u.recordEvent)("analytics_filters_filter",{report:r,...e});break;case"clear_all":(0,u.recordEvent)("analytics_filters_clear_all",{report:r});break;case"match":(0,u.recordEvent)("analytics_filters_all_any",{report:r,value:t.match})}}render(){const{advancedFilters:e,filters:t,path:r,query:a,showDatePicker:o,defaultDateRange:s}=this.props,{period:n,compare:i,before:l,after:u}=(0,d.getDateParamsFromQuery)(a,s),{primary:m,secondary:y}=(0,d.getCurrentDates)(a,s),g={period:n,compare:i,before:l,after:u,primaryDate:m,secondaryDate:y},_=this.context;return(0,h.jsx)(c.ReportFilters,{query:a,siteLocale:p.ne.siteLocale,currency:_.getCurrencyConfig(),path:r,filters:t,advancedFilters:e,showDatePicker:o,onDateSelect:this.onDateSelect,onFilterSelect:this.onFilterSelect,onAdvancedFilterAction:this.onAdvancedFilterAction,dateQuery:g,isoDateFormat:d.isoDateFormat})}}y.contextType=m.CurrencyContext;const g=(0,o.compose)((0,n.withSelect)((e=>{const{woocommerce_default_date_range:t}=e(l.settingsStore).getSetting("wc_admin","wcAdminSettings");return{defaultDateRange:t}})),(0,n.withDispatch)((e=>{const{addCesSurveyForAnalytics:t}=e(i.STORE_KEY);return{addCesSurveyForAnalytics:t}})))(y)},97605:(e,t,r)=>{r.d(t,{A:()=>C});var a=r(56427),o=r(52619),s=r(86087),n=r(29491),i=r(28107),c=r(47143),l=r(66087),d=r(27723),u=r(27752),m=r(98846),p=r(96476),h=r(91554),y=r(40314),g=r(83306),_=r(39793);const f=()=>(0,_.jsx)("svg",{role:"img","aria-hidden":"true",focusable:"false",version:"1.1",xmlns:"http://www.w3.org/2000/svg",x:"0px",y:"0px",viewBox:"0 0 24 24",children:(0,_.jsx)("path",{d:"M18,9c-0.009,0-0.017,0.002-0.025,0.003C17.72,5.646,14.922,3,11.5,3C7.91,3,5,5.91,5,9.5c0,0.524,0.069,1.031,0.186,1.519 C5.123,11.016,5.064,11,5,11c-2.209,0-4,1.791-4,4c0,1.202,0.541,2.267,1.38,3h18.593C22.196,17.089,23,15.643,23,14 C23,11.239,20.761,9,18,9z M12,16l-4-5h3V8h2v3h3L12,16z"})}),b=[],w={},C=(0,n.compose)((0,c.withSelect)(((e,t)=>{const{endpoint:r,getSummary:a,isRequesting:o,itemIdField:s,query:n,tableData:i,tableQuery:c,filters:d,advancedFilters:u,summaryFields:m,extendedItemsStoreName:p}=t,h=p?e(p):null,g=n.search&&!(n[r]&&n[r].length);if(o||g)return w;const _=e(y.reportsStore),{woocommerce_default_date_range:f}=e(y.settingsStore).getSetting("wc_admin","wcAdminSettings"),C="categories"===r?"products":r,S=a?(0,y.getReportChartData)({endpoint:C,selector:_,dataType:"primary",query:n,filters:d,advancedFilters:u,defaultDateRange:f,fields:m}):w,v=i||(0,y.getReportTableData)({endpoint:r,query:n,selector:_,tableQuery:c,filters:d,advancedFilters:u,defaultDateRange:f}),x=h?function(e,t,r){const{extendItemsMethodNames:a,itemIdField:o}=t,s=r.items.data;if(!(Array.isArray(s)&&s.length&&a&&o))return r;const{[a.getError]:n,[a.isRequesting]:i,[a.load]:c}=e,d={include:s.map((e=>e[o])).join(","),per_page:s.length},u=c(d),m=!!i&&i(d),p=!!n&&n(d),h=s.map((e=>{const t=(0,l.first)(u.filter((t=>e.id===t.id)));return{...e,...t}})),y=r.isRequesting||m,g=r.isError||p;return{...r,isRequesting:y,isError:g,items:{...r.items,data:h}}}(h,t,v):v;return{primaryData:S,ids:s&&x.items.data?x.items.data.map((e=>e[s])):b,tableData:x,query:n}})),(0,c.withDispatch)((e=>{const{startExport:t}=e(y.EXPORT_STORE_NAME),{createNotice:r}=e("core/notices"),{addCesSurveyForCustomerSearch:a}=e(u.STORE_KEY);return{createNotice:r,startExport:t,addCesSurveyForCustomerSearch:a}})))((e=>{const{getHeadersContent:t,getRowsContent:r,getSummary:n,isRequesting:c,primaryData:u={},tableData:b={items:{data:[],totalResults:0},query:{}},endpoint:w,itemIdField:C,tableQuery:S={},compareBy:v,compareParam:x="filter",searchBy:F,labels:E={},...k}=e,{query:D,columnPrefsKey:A}=e,{items:R,query:q}=b,j=D[x]?(0,p.getIdsFromQuery)(D[v]):[],[T,Q]=(0,s.useState)(j),N=(0,s.useRef)(null),{updateUserPreferences:P,...B}=(0,y.useUserPreferences)();if(b.isError||u.isError)return(0,_.jsx)(m.AnalyticsError,{});let I=[];A&&(I=B&&B[A]?B[A]:I);const O=(e,a,s)=>{const i=n?n(a,s):null;return(0,o.applyFilters)("woocommerce_admin_report_table",{endpoint:w,headers:t(),rows:r(e),totals:a,summary:i,items:R})},L=t=>{const{ids:r}=e;Q(t?r:[])},U=(t,r)=>{const{ids:a}=e;if(r)Q((0,l.uniq)([a[t],...T]));else{const e=T.indexOf(a[t]);Q([...T.slice(0,e),...T.slice(e+1)])}},V=t=>{const{ids:r=[]}=e,o=-1!==T.indexOf(r[t]);return{display:(0,_.jsx)(a.CheckboxControl,{onChange:(0,l.partial)(U,t),checked:o}),value:!1}},M=c||b.isRequesting||u.isRequesting,Y=(0,l.get)(u,["data","totals"],{}),z=R.totalResults||0,K=z>0,J=(0,p.getSearchWords)(D).map((e=>({key:e,label:e}))),{data:H}=R,W=O(H,Y,z);let{headers:X,rows:G}=W;const{summary:Z}=W;v&&(G=G.map(((e,t)=>[V(t),...e])),X=[(()=>{const{ids:t=[]}=e,r=t.length>0,o=r&&t.length===T.length;return{cellClassName:"is-checkbox-column",key:"compare",label:(0,_.jsx)(a.CheckboxControl,{onChange:L,"aria-label":(0,d.__)("Select All","woocommerce"),checked:o,disabled:!r}),required:!0}})(),...X]);const $=((e,t)=>t?e.map((e=>({...e,visible:e.required||!t.includes(e.key)}))):e.map((e=>({...e,visible:e.required||!e.hiddenByDefault}))))(X,I);return(0,_.jsxs)(s.Fragment,{children:[(0,_.jsx)("div",{className:"woocommerce-report-table__scroll-point",ref:N,"aria-hidden":!0}),(0,_.jsx)(m.TableCard,{className:"woocommerce-report-table",hasSearch:!!F,actions:[v&&(0,_.jsx)(m.CompareButton,{className:"woocommerce-table__compare",count:T.length,helpText:E.helpText||(0,d.__)("Check at least two items below to compare","woocommerce"),onClick:()=>{v&&(0,p.onQueryChange)("compare")(v,x,T.join(","))},disabled:!K,children:E.compareButton||(0,d.__)("Compare","woocommerce")},"compare"),F&&(0,_.jsx)(m.Search,{allowFreeTextSearch:!0,inlineTags:!0,onChange:t=>{const{baseSearchQuery:r={},addCesSurveyForCustomerSearch:a}=e,o=t.map((e=>e.label.replace(",","%2C")));o.length?((0,p.updateQueryString)({filter:void 0,[x]:void 0,[F]:void 0,...r,search:(0,l.uniq)(o).join(",")}),a()):(0,p.updateQueryString)({search:void 0}),(0,g.recordEvent)("analytics_table_filter",{report:w})},placeholder:E.placeholder||(0,d.__)("Search by item name","woocommerce"),selected:J,showClearButton:!0,type:F,disabled:!K},"search"),K&&(0,_.jsxs)(a.Button,{className:"woocommerce-table__download-button",disabled:M,onClick:()=>{const{createNotice:t,startExport:r,title:a}=e,o=Object.assign({},D),{data:s,totalResults:n}=R;let i="browser";if(delete o.extended_info,o.search&&delete o[F],s&&s.length===n){const{headers:e,rows:t}=O(s,n);(0,h.downloadCSVFile)((0,h.generateCSVFileName)(a,o),(0,h.generateCSVDataFromTable)(e,t))}else i="email",r(w,q).then((()=>t("success",(0,d.sprintf)((0,d.__)("Your %s Report will be emailed to you.","woocommerce"),a)))).catch((e=>t("error",e.message||(0,d.sprintf)((0,d.__)("There was a problem exporting your %s Report. Please try again.","woocommerce"),a))));(0,g.recordEvent)("analytics_table_download",{report:w,rows:n,download_type:i})},children:[(0,_.jsx)(f,{}),(0,_.jsx)("span",{className:"woocommerce-table__download-button__label",children:E.downloadButton||(0,d.__)("Download","woocommerce")})]},"download")],headers:$,isLoading:M,onQueryChange:p.onQueryChange,onColumnsChange:(e,t)=>{const r=X.map((e=>e.key)).filter((t=>!e.includes(t)));if(A&&P({[A]:r}),t){const r={report:w,column:t,status:e.includes(t)?"on":"off"};(0,g.recordEvent)("analytics_table_header_toggle",r)}},onSort:(e,t)=>{(0,p.onQueryChange)("sort")(e,t);const r={report:w,column:e,direction:t};(0,g.recordEvent)("analytics_table_sort",r)},onPageChange:(e,t)=>{N.current.scrollIntoView();const r=N.current.nextSibling.querySelector(".woocommerce-table__table"),a=i.focus.focusable.find(r);a.length&&a[0].focus(),t&&("goto"===t?(0,g.recordEvent)("analytics_table_go_to_page",{report:w,page:e}):(0,g.recordEvent)("analytics_table_page_click",{report:w,direction:t}))},rows:G,rowsPerPage:parseInt(q.per_page,10)||y.QUERY_DEFAULTS.pageSize,summary:Z,totalRows:z,...k})]})}))}}]);