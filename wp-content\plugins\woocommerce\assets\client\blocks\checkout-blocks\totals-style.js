"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[8268],{87170:(e,t,n)=>{n.r(t),n.d(t,{default:()=>a});var o=n(4921),r=n(20389),c=n(14656),i=n(86087),s=n(25656),h=n(10790);const a=({children:e,className:t})=>{const[n,a,u]=function(){const[e,t]=(0,i.useState)({height:0,width:0}),[n,o]=(0,i.useState)({height:0,width:0}),r=(0,i.useRef)(null);return(0,i.useEffect)((()=>{if(!r.current)return;const e=r.current,n=new ResizeObserver((n=>{n.forEach((n=>{if(n.target===e){let o="0";o=e.computedStyleMap?e.computedStyleMap().get("top")?.toString()||o:getComputedStyle(e).top||o;const{height:r,width:c}=n.contentRect;t({height:r+parseInt(o,10),width:c})}}))})),c=new IntersectionObserver((e=>{e.forEach((e=>{const{height:n,width:r}=e.boundingClientRect;t({height:n,width:r}),e.target.ownerDocument.defaultView&&o({height:e.target.ownerDocument.defaultView?.innerHeight,width:e.target.ownerDocument.defaultView?.innerWidth})}))}),{root:null,rootMargin:"0px",threshold:1});return n.observe(e),c.observe(e),()=>{e&&(n.unobserve(e),c.unobserve(e))}}),[]),[r,e,n]}(),l=a.height<u.height,{isLarge:g}=(0,s.G)();return(0,h.jsxs)(r.A,{ref:n,className:(0,o.A)("wc-block-checkout__sidebar",t,{"is-sticky":l,"is-large":g}),children:[(0,h.jsx)(c.StoreNoticesContainer,{context:"woocommerce/checkout-totals-block"}),e]})}}}]);