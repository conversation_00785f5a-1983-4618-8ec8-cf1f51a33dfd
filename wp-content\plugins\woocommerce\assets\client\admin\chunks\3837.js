"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3837],{55737:(e,t,r)=>{r.d(t,{A:()=>b});var a=r(27723),s=r(86087),n=r(29491),o=r(38443),i=r(47143),l=r(66087),c=r(98846),u=r(40314),m=r(77374),d=r(94111),p=r(96476);function y(e,t,r={}){if(!e||0===e.length)return null;const a=e.slice(0),s=a.pop();if(s.showFilters(t,r)){const e=(0,p.flattenFilters)(s.filters),r=t[s.param]||s.defaultValue||"all";return(0,l.find)(e,{value:r})}return y(a,t,r)}function g(e){return t=>(0,o.format)(e,t)}function h(e){if(e?.data?.intervals?.length>1){const t=e.data.intervals[0].date_start,r=e.data.intervals[e.data.intervals.length-1].date_end;if((0,m.containsLeapYear)(t,r))return!0}return!1}var f=r(39793);class v extends s.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,l.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:t}=this.props;return e.data.intervals.map((function(e){const r={};return e.subtotals.segments.forEach((function(e){if(e.segment_label){const a=r[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;r[e.segment_id]={label:a,value:e.subtotals[t.key]||0}}})),{date:(0,o.format)("Y-m-d\\TH:i:s",e.date_start),...r}}))}getTimeChartData(){const{query:e,primaryData:t,secondaryData:r,selectedChart:a,defaultDateRange:s}=this.props,n=(0,m.getIntervalForQuery)(e,s),{primary:i,secondary:l}=(0,m.getCurrentDates)(e,s);return function(e,t,r,a,s,n,i){const l=h(e),c=h(t),u=[...e.data.intervals],d=[...t.data.intervals],p=[];for(let e=0;e<u.length;e++){const t=u[e],y=(0,o.format)("Y-m-d\\TH:i:s",t.date_start),g=`${r.label} (${r.range})`,h=t.date_start,f=t.subtotals[n]||0,v=d[e],b=`${a.label} (${a.range})`;let C=(0,m.getPreviousDate)(t.date_start,r.after,a.after,s,i).format("YYYY-MM-DD HH:mm:ss"),D=v&&v.subtotals[n]||0;if("day"===i&&l&&!c&&d?.[e]){const r=new Date(t.date_start),a=new Date(d[e].date_start);(0,m.isLeapYear)(r.getFullYear())&&1===r.getMonth()&&29===r.getDate()&&2===a.getMonth()&&1===a.getDate()&&(C="-",D=0,d.splice(e,0,d[e]))}p.push({date:y,primary:{label:g,labelDate:h,value:f},secondary:{label:b,labelDate:C,value:D}})}return p}(t,r,i,l,e.compare,a.key,n)}getTimeChartTotals(){const{primaryData:e,secondaryData:t,selectedChart:r}=this.props;return{primary:(0,l.get)(e,["data","totals",r.key],null),secondary:(0,l.get)(t,["data","totals",r.key],null)}}renderChart(e,t,r,s){const{emptySearchResults:n,filterParam:o,interactiveLegend:i,itemsLabel:l,legendPosition:d,path:p,query:y,selectedChart:h,showHeaderControls:v,primaryData:b,defaultDateRange:C}=this.props,D=(0,m.getIntervalForQuery)(y,C),_=(0,m.getAllowedIntervalsForQuery)(y,C),A=(0,m.getDateFormatsForInterval)(D,b.data.intervals.length,{type:"php"}),R=n?(0,a.__)("No data for the current search","woocommerce"):(0,a.__)("No data for the selected date range","woocommerce"),{formatAmount:T,getCurrencyConfig:k}=this.context;return(0,f.jsx)(c.Chart,{allowedIntervals:_,data:r,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:R,filterParam:o,interactiveLegend:i,interval:D,isRequesting:t,itemsLabel:l,legendPosition:d,legendTotals:s,mode:e,path:p,query:y,screenReaderFormat:g(A.screenReaderFormat),showHeaderControls:v,title:h.label,tooltipLabelFormat:g(A.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&h.label||null,tooltipValueFormat:(0,u.getTooltipValueFormat)(h.type,T),chartType:(0,m.getChartTypeForQuery)(y),valueType:h.type,xFormat:g(A.xFormat),x2Format:g(A.x2Format),currency:k()})}renderItemComparison(){const{isRequesting:e,primaryData:t}=this.props;if(t.isError)return(0,f.jsx)(c.AnalyticsError,{});const r=e||t.isRequesting,a=this.getItemChartData();return this.renderChart("item-comparison",r,a)}renderTimeComparison(){const{isRequesting:e,primaryData:t,secondaryData:r}=this.props;if(!t||t.isError||r.isError)return(0,f.jsx)(c.AnalyticsError,{});const a=e||t.isRequesting||r.isRequesting,s=this.getTimeChartData(),n=this.getTimeChartTotals();return this.renderChart("time-comparison",a,s,n)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}v.contextType=d.CurrencyContext,v.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const b=(0,n.compose)((0,i.withSelect)(((e,t)=>{const{charts:r,endpoint:a,filters:s,isRequesting:n,limitProperties:o,query:i,advancedFilters:c}=t,m=o||[a],d=y(s,i),p=(0,l.get)(d,["settings","param"]),g=t.mode||function(e,t){if(e&&t){const r=(0,l.get)(e,["settings","param"]);if(!r||Object.keys(t).includes(r))return(0,l.get)(e,["chartMode"])}return null}(d,i)||"time-comparison",{woocommerce_default_date_range:h}=e(u.settingsStore).getSetting("wc_admin","wcAdminSettings"),f={mode:g,filterParam:p,defaultDateRange:h};if(n)return f;const v=m.some((e=>i[e]&&i[e].length));if(i.search&&!v)return{...f,emptySearchResults:!0};const b=e(u.reportsStore),C=r&&r.map((e=>e.key)),D=(0,u.getReportChartData)({endpoint:a,dataType:"primary",query:i,selector:b,limitBy:m,filters:s,advancedFilters:c,defaultDateRange:h,fields:C});if("item-comparison"===g)return{...f,primaryData:D};const _=(0,u.getReportChartData)({endpoint:a,dataType:"secondary",query:i,selector:b,limitBy:m,filters:s,advancedFilters:c,defaultDateRange:h,fields:C});return{...f,primaryData:D,secondaryData:_}})))(v)},68224:(e,t,r)=>{r.d(t,{A:()=>h});var a=r(27723),s=r(86087),n=r(29491),o=r(47143),i=r(96476),l=r(98846),c=r(43577),u=r(40314),m=r(77374),d=r(83306),p=r(94111),y=r(39793);class g extends s.Component{formatVal(e,t){const{formatAmount:r,getCurrencyConfig:a}=this.context;return"currency"===t?r(e):(0,c.formatValue)(a(),t,e)}getValues(e,t){const{emptySearchResults:r,summaryData:a}=this.props,{totals:s}=a,n=s.primary?s.primary[e]:0,o=s.secondary?s.secondary[e]:0,i=r?0:n,l=r?0:o;return{delta:(0,c.calculateDelta)(i,l),prevValue:this.formatVal(l,t),value:this.formatVal(i,t)}}render(){const{charts:e,query:t,selectedChart:r,summaryData:s,endpoint:n,report:o,defaultDateRange:c}=this.props,{isError:u,isRequesting:p}=s;if(u)return(0,y.jsx)(l.AnalyticsError,{});if(p)return(0,y.jsx)(l.SummaryListPlaceholder,{numberOfItems:e.length});const{compare:g}=(0,m.getDateParamsFromQuery)(t,c);return(0,y.jsx)(l.SummaryList,{children:({onToggle:t})=>e.map((e=>{const{key:s,order:c,orderby:u,label:m,type:p,isReverseTrend:h,labelTooltipText:f}=e,v={chart:s};u&&(v.orderby=u),c&&(v.order=c);const b=(0,i.getNewPath)(v),C=r.key===s,{delta:D,prevValue:_,value:A}=this.getValues(s,p);return(0,y.jsx)(l.SummaryNumber,{delta:D,href:b,label:m,reverseTrend:h,prevLabel:"previous_period"===g?(0,a.__)("Previous period:","woocommerce"):(0,a.__)("Previous year:","woocommerce"),prevValue:_,selected:C,value:A,labelTooltipText:f,onLinkClickCallback:()=>{t&&t(),(0,d.recordEvent)("analytics_chart_tab_click",{report:o||n,key:s})}},s)}))})}}g.defaultProps={summaryData:{totals:{primary:{},secondary:{}},isError:!1}},g.contextType=p.CurrencyContext;const h=(0,n.compose)((0,o.withSelect)(((e,t)=>{const{charts:r,endpoint:a,limitProperties:s,query:n,filters:o,advancedFilters:i}=t,l=s||[a],c=l.some((e=>n[e]&&n[e].length));if(n.search&&!c)return{emptySearchResults:!0};const m=r&&r.map((e=>e.key)),{woocommerce_default_date_range:d}=e(u.settingsStore).getSetting("wc_admin","wcAdminSettings");return{summaryData:(0,u.getSummaryNumbers)({endpoint:a,query:n,select:e,limitBy:l,filters:o,advancedFilters:i,defaultDateRange:d,fields:m}),defaultDateRange:d}})))(g)},43128:(e,t,r)=>{function a(e,t,r){return!!t&&e&&t<=r==="instock"}r.d(t,{n:()=>a})},32639:(e,t,r)=>{r.d(t,{H:()=>s});var a=r(27723);function s(e){return[e.country,e.state,e.name||(0,a.__)("TAX","woocommerce"),e.priority].map((e=>e.toString().toUpperCase().trim())).filter(Boolean).join("-")}},33958:(e,t,r)=>{r.d(t,{Dn:()=>d,U4:()=>y,aG:()=>p,b8:()=>b,jx:()=>f,p0:()=>h,wd:()=>g,xP:()=>v});var a=r(27723),s=r(93832),n=r(1455),o=r.n(n),i=r(66087),l=r(96476),c=r(40314),u=r(32639),m=r(56109);function d(e,t=i.identity){return function(r="",a){const n="function"==typeof e?e(a):e,i=(0,l.getIdsFromQuery)(r);if(i.length<1)return Promise.resolve([]);const c={include:i.join(","),per_page:i.length};return o()({path:(0,s.addQueryArgs)(n,c)}).then((e=>e.map(t)))}}d(c.NAMESPACE+"/products/attributes",(e=>({key:e.id,label:e.name})));const p=d(c.NAMESPACE+"/products/categories",(e=>({key:e.id,label:e.name}))),y=d(c.NAMESPACE+"/coupons",(e=>({key:e.id,label:e.code}))),g=d(c.NAMESPACE+"/customers",(e=>({key:e.id,label:e.name}))),h=d(c.NAMESPACE+"/products",(e=>({key:e.id,label:e.name}))),f=d(c.NAMESPACE+"/taxes",(e=>({key:e.id,label:(0,u.H)(e)})));function v({attributes:e,name:t}){const r=(0,m.Qk)("variationTitleAttributesSeparator"," - ");if(t&&t.indexOf(r)>-1)return t;const s=(e||[]).map((({name:e,option:t})=>(t||(e=e.charAt(0).toUpperCase()+e.slice(1),t=(0,a.sprintf)((0,a.__)("Any %s","woocommerce"),e)),t))).join(", ");return s?t+r+s:t}const b=d((({products:e})=>e?c.NAMESPACE+`/products/${e}/variations`:c.NAMESPACE+"/variations"),(e=>({key:e.id,label:v(e)})))},95272:(e,t,r)=>{r.d(t,{A:()=>s});var a=r(66087);function s(e,t=[]){return(0,a.find)(t,{key:e})||t[0]}}}]);