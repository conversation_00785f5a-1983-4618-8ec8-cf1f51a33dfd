<?php
/**
 * Competition Winner Notification Email Template - Modern Version
 * 
 * This template can be overridden by copying it to yourtheme/woocommerce/emails/winner-notification.php.
 * 
 * @see https://woocommerce.com/document/template-structure/
 * @package Competition_Manager/Templates/Emails
 * @version 3.0.0
 */

if (!defined('ABSPATH')) {
    exit;
}

/* 
 * @hooked WC_Emails::email_header() Output the email header 
 */
do_action('woocommerce_email_header', $email_heading, $email); 
?>

<style>
    @import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');
    
    .email-container {
        font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        max-width: 600px;
        margin: 0 auto;
        background: #ffffff;
        line-height: 1.6;
    }
    
    .winner-badge {
        background: linear-gradient(135deg, #6366f1 0%, #8b5cf6 100%);
        padding: 12px 24px;
        border-radius: 6px;
        color: #ffffff;
        font-weight: 600;
        font-size: 13px;
        text-transform: uppercase;
        letter-spacing: 0.8px;
        display: inline-block;
        margin-bottom: 24px;
        box-shadow: 0 4px 20px rgba(99, 102, 241, 0.25);
    }
    
    .cta-button {
        background: #000000;
        color: #ffffff !important;
        padding: 16px 32px;
        text-decoration: none;
        border-radius: 8px;
        font-weight: 500;
        font-size: 15px;
        display: inline-block;
        transition: all 0.2s ease;
        border: 1px solid #000000;
        letter-spacing: 0.3px;
    }
    
    .cta-button:hover {
        background: #1f2937;
        transform: translateY(-1px);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    }
    
    .prize-section {
        background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
        padding: 40px 32px;
        border-radius: 12px;
        margin: 32px 0;
        border: 1px solid #e2e8f0;
        position: relative;
    }
    
    .prize-section::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 3px;
        background: linear-gradient(90deg, #6366f1, #8b5cf6, #ec4899);
        border-radius: 12px 12px 0 0;
    }
    
    .info-grid {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 24px;
        margin: 32px 0;
    }
    
    .info-card {
        background: #ffffff;
        padding: 24px;
        border-radius: 8px;
        border: 1px solid #e5e7eb;
        transition: all 0.2s ease;
    }
    
    .info-card:hover {
        border-color: #6366f1;
        box-shadow: 0 4px 12px rgba(99, 102, 241, 0.1);
    }
    
    .ticket-number {
        background: linear-gradient(135deg, #1f2937 0%, #374151 100%);
        color: #ffffff;
        padding: 8px 16px;
        border-radius: 6px;
        font-weight: 600;
        font-size: 15px;
        display: inline-block;
        font-family: 'SF Mono', Monaco, 'Cascadia Code', monospace;
        letter-spacing: 1px;
    }
    
    .next-steps {
        background: #ffffff;
        border: 1px solid #e5e7eb;
        padding: 32px;
        border-radius: 12px;
        margin: 32px 0;
        position: relative;
    }
    
    .next-steps::before {
        content: '';
        position: absolute;
        top: 0;
        left: 24px;
        right: 24px;
        height: 2px;
        background: linear-gradient(90deg, #10b981, #059669);
        border-radius: 0 0 2px 2px;
    }
    
    .contact-section {
        background: #f9fafb;
        border: 1px solid #e5e7eb;
        padding: 28px;
        border-radius: 8px;
        margin: 32px 0;
    }
    
    .section-title {
        font-size: 18px;
        font-weight: 600;
        color: #111827;
        margin: 0 0 16px 0;
        letter-spacing: -0.025em;
    }
    
    .text-muted {
        color: #6b7280;
        font-size: 14px;
    }
    
    .text-primary {
        color: #6366f1;
        font-weight: 500;
    }
    
    @media (max-width: 600px) {
        .info-grid {
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .prize-section,
        .next-steps {
            padding: 24px 20px;
        }
        
        .email-container {
            margin: 0 16px;
        }
    }
</style>

<div class="email-container">
    <!-- Hero Section -->
    <div style="background: linear-gradient(135deg, #111827 0%, #1f2937 100%); padding: 48px 32px; text-align: center; border-radius: 12px 12px 0 0;">
        <div class="winner-badge">
            Winner Announcement
        </div>
        
        <h1 style="color: #ffffff; font-size: 32px; font-weight: 700; margin: 0 0 12px 0; letter-spacing: -0.025em;">
            Congratulations
        </h1>
        
        <p style="color: rgba(255,255,255,0.8); font-size: 16px; margin: 0; font-weight: 400;">
            You've won our competition
        </p>
    </div>

    <!-- Main Content -->
    <div style="padding: 48px 32px; background: #ffffff;">
        <!-- Personal Greeting -->
        <div style="margin-bottom: 40px;">
            <h2 style="font-size: 24px; color: #111827; margin: 0 0 12px 0; font-weight: 600; letter-spacing: -0.025em;">
                Hello <span class="text-primary"><?php echo esc_html($winner_name); ?></span>
            </h2>
            <p style="font-size: 16px; color: #4b5563; margin: 0; line-height: 1.6;">
                We're excited to inform you that you've been selected as the winner of our latest competition.
            </p>
        </div>

        <!-- Prize Section -->
        <div class="prize-section">
            <h3 class="section-title" style="text-align: center; margin-bottom: 24px;">
                Your Prize
            </h3>
            <div style="text-align: center;">
                <p style="font-size: 20px; font-weight: 600; color: #111827; margin: 0;">
                    <?php echo esc_html($prize_details); ?>
                </p>
            </div>
        </div>

        <!-- Competition Details -->
        <div style="margin: 40px 0;">
            <h3 class="section-title">
                Competition Details
            </h3>
            
            <div class="info-grid">
                <div class="info-card">
                    <h4 style="color: #6b7280; font-size: 12px; font-weight: 500; margin: 0 0 8px 0; text-transform: uppercase; letter-spacing: 0.8px;">
                        Competition
                    </h4>
                    <p style="color: #111827; font-size: 16px; font-weight: 500; margin: 0;">
                        <?php echo esc_html($competition_title); ?>
                    </p>
                </div>
                
                <div class="info-card">
                    <h4 style="color: #6b7280; font-size: 12px; font-weight: 500; margin: 0 0 12px 0; text-transform: uppercase; letter-spacing: 0.8px;">
                        Winning Ticket
                    </h4>
                    <span class="ticket-number">
                        <?php echo esc_html($ticket_number); ?>
                    </span>
                </div>
            </div>
        </div>

        <!-- Next Steps -->
        <div class="next-steps">
            <h3 class="section-title">
                What's Next
            </h3>
            <div style="background: #f0fdf4; border: 1px solid #bbf7d0; padding: 20px; border-radius: 8px; margin: 20px 0;">
                <p style="color: #166534; font-size: 15px; margin: 0; line-height: 1.6;">
                    Our team will reach out within <strong>14 business days</strong> with detailed instructions on claiming your prize.
                </p>
            </div>
            <p class="text-muted" style="margin: 16px 0 0 0;">
                Please ensure your account contact information is current and accurate.
            </p>
        </div>

        <!-- Call to Action -->
        <div style="text-align: center; margin: 40px 0;">
            <a href="<?php echo esc_url(home_url('/my-account/')); ?>" class="cta-button">
                Access My Account
            </a>
        </div>

        <!-- Contact Support -->
        <div class="contact-section">
            <h4 class="section-title" style="font-size: 16px;">
                Need Assistance?
            </h4>
            <p style="color: #4b5563; font-size: 15px; margin: 0 0 16px 0;">
                If you have questions about your prize or need support, our team is ready to help.
            </p>
            <a href="<?php echo esc_url(home_url('/contact/')); ?>" 
               style="color: #6366f1; text-decoration: none; font-weight: 500; font-size: 15px;">
               Contact Support →
            </a>
        </div>

        <!-- Thank You -->
        <div style="text-align: center; margin: 48px 0 24px 0; padding: 32px; background: #fafafa; border-radius: 8px; border: 1px solid #e5e7eb;">
            <p style="color: #374151; font-size: 16px; margin: 0 0 8px 0;">
                Thank you for participating in our competitions.
            </p>
            <p style="color: #6366f1; font-size: 15px; font-weight: 500; margin: 0;">
                Film Collectables Team
            </p>
        </div>
    </div>

    <!-- Footer -->
    <div style="background: #f9fafb; color: #6b7280; padding: 32px; text-align: center; border-radius: 0 0 12px 12px; border-top: 1px solid #e5e7eb;">
        <p style="font-size: 13px; margin: 0 0 16px 0;">
            This notification was sent because you won a competition on <strong style="color: #374151;"><?php echo esc_html(get_bloginfo('name')); ?></strong>
        </p>
        <div style="border-top: 1px solid #e5e7eb; padding-top: 16px;">
            <p style="font-size: 12px; margin: 0; color: #9ca3af;">
                <strong style="color: #6b7280;">Film Collectables Competitions Limited</strong><br>
                Ground Floor, Gallery Building, 65-69 Dublin Rd, Belfast, BT2 7HG<br>
                Company Registration: NI730025
            </p>
        </div>
    </div>
</div>

<?php
/**
 * Show user-defined additional content - this is set in each email's settings.
 */
if ($additional_content) {
    echo '<div style="margin-top: 24px; padding: 24px; background: #f8fafc; border-radius: 8px; border: 1px solid #e2e8f0;">';
    echo wp_kses_post(wpautop(wptexturize($additional_content)));
    echo '</div>';
}

/* 
 * @hooked WC_Emails::email_footer() Output the email footer 
 */
do_action('woocommerce_email_footer', $email);
?>
