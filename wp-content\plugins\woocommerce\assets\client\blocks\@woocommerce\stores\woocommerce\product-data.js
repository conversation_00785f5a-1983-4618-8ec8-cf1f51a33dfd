import*as t from"@wordpress/interactivity";var e={d:(t,o)=>{for(var a in o)e.o(o,a)&&!e.o(t,a)&&Object.defineProperty(t,a,{enumerable:!0,get:o[a]})},o:(t,e)=>Object.prototype.hasOwnProperty.call(t,e)};const o=(r={getContext:()=>t.getContext,store:()=>t.store},c={},e.d(c,r),c),a=(0,o.store)("woocommerce/product-data",{state:{get productData(){const t=(0,o.getContext)("woocommerce/single-product");return t?.productData||a?.state?.templateState?.productData},get originalProductData(){const t=(0,o.getContext)("woocommerce/single-product");return t?.originalProductData||a?.state?.templateState?.originalProductData}},actions:{setProductData:(t,e)=>{const r=(0,o.getContext)("woocommerce/single-product");r?.productData?r.productData[t]=e:a?.state?.templateState?.productData&&(a.state.templateState.productData[t]=e)}}},{lock:!0});var r,c;