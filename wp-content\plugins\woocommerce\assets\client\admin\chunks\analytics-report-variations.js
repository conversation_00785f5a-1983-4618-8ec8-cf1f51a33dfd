"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[6071],{96703:(e,o,t)=>{t.r(o),t.d(o,{default:()=>f});var r=t(27723),a=t(86087),i=t(98846),c=t(52619),s=t(47143),l=t(27752),n=t(33958);const{addCesSurveyForAnalytics:m}=(0,s.dispatch)(l.STORE_KEY),d=(0,c.applyFilters)("woocommerce_admin_variations_report_charts",[{key:"items_sold",label:(0,r.__)("Items sold","woocommerce"),order:"desc",orderby:"items_sold",type:"number"},{key:"net_revenue",label:(0,r.__)("Net sales","woocommerce"),order:"desc",orderby:"net_revenue",type:"currency"},{key:"orders_count",label:(0,r.__)("Orders","woocommerce"),order:"desc",orderby:"orders_count",type:"number"}]),u=(0,c.applyFilters)("woocommerce_admin_variations_report_filters",[{label:(0,r.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter-variations",showFilters:()=>!0,filters:[{label:(0,r.__)("All variations","woocommerce"),chartMode:"item-comparison",value:"all"},{label:(0,r.__)("Single variation","woocommerce"),value:"select_variation",subFilters:[{component:"Search",value:"single_variation",path:["select_variation"],settings:{type:"variations",param:"variations",getLabels:n.b8,labels:{placeholder:(0,r.__)("Type to search for a variation","woocommerce"),button:(0,r.__)("Single variation","woocommerce")}}}]},{label:(0,r.__)("Comparison","woocommerce"),chartMode:"item-comparison",value:"compare-variations",settings:{type:"variations",param:"variations",getLabels:n.b8,labels:{helpText:(0,r.__)("Check at least two variations below to compare","woocommerce"),placeholder:(0,r.__)("Search for variations to compare","woocommerce"),title:(0,r.__)("Compare Variations","woocommerce"),update:(0,r.__)("Compare","woocommerce")},onClick:m}},{label:(0,r.__)("Advanced filters","woocommerce"),value:"advanced"}]}]),_=(0,c.applyFilters)("woocommerce_admin_variations_report_advanced_filters",{title:(0,r._x)("Variations match <select/> filters","A sentence describing filters for Variations. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce"),filters:{attribute:{allowMultiple:!0,labels:{add:(0,r.__)("Product attribute","woocommerce"),placeholder:(0,r.__)("Search product attributes","woocommerce"),remove:(0,r.__)("Remove product attribute filter","woocommerce"),rule:(0,r.__)("Select a product attribute filter match","woocommerce"),title:(0,r.__)("<title>Product attribute</title> <rule/> <filter/>","woocommerce"),filter:(0,r.__)("Select product attributes","woocommerce")},rules:[{value:"is",label:(0,r._x)("Is","product attribute","woocommerce")},{value:"is_not",label:(0,r._x)("Is Not","product attribute","woocommerce")}],input:{component:"ProductAttribute"}},category:{labels:{add:(0,r.__)("Product category","woocommerce"),placeholder:(0,r.__)("Search product categories","woocommerce"),remove:(0,r.__)("Remove product category filter","woocommerce"),rule:(0,r.__)("Select a product category filter match","woocommerce"),title:(0,r.__)("<title>Product category</title> <rule/> <filter/>","woocommerce"),filter:(0,r.__)("Select product categories","woocommerce")},rules:[{value:"includes",label:(0,r._x)("Includes","categories","woocommerce")},{value:"excludes",label:(0,r._x)("Excludes","categories","woocommerce")}],input:{component:"Search",type:"categories",getLabels:n.aG}},product:{labels:{add:(0,r.__)("Product","woocommerce"),placeholder:(0,r.__)("Search products","woocommerce"),remove:(0,r.__)("Remove product filter","woocommerce"),rule:(0,r.__)("Select a product filter match","woocommerce"),title:(0,r.__)("<title>Product</title> <rule/> <filter/>","woocommerce"),filter:(0,r.__)("Select products","woocommerce")},rules:[{value:"includes",label:(0,r._x)("Includes","products","woocommerce")},{value:"excludes",label:(0,r._x)("Excludes","products","woocommerce")}],input:{component:"Search",type:"variableProducts",getLabels:n.p0}}}});var p=t(95272),v=t(55737),b=t(68224),w=t(86771),y=t(88711),h=t(39793);const f=e=>{const{itemsLabel:o,mode:t}=(({query:e})=>{const o="compare-variations"===e["filter-variations"]&&e.variations&&e.variations.split(",").length>1;return{compareObject:"variations",itemsLabel:(0,r.__)("%d variations","woocommerce"),mode:o?"item-comparison":"time-comparison"}})(e),{path:c,query:s,isError:l,isRequesting:n}=e;if(l)return(0,h.jsx)(i.AnalyticsError,{});const m={...s};return"item-comparison"===t&&(m.segmentby="variation"),(0,h.jsxs)(a.Fragment,{children:[(0,h.jsx)(y.A,{query:s,path:c,filters:u,advancedFilters:_,report:"variations"}),(0,h.jsx)(b.A,{mode:t,charts:d,endpoint:"variations",query:m,selectedChart:(0,p.A)(s.chart,d),filters:u,advancedFilters:_}),(0,h.jsx)(v.A,{charts:d,mode:t,filters:u,advancedFilters:_,endpoint:"variations",isRequesting:n,itemsLabel:o,path:c,query:m,selectedChart:(0,p.A)(m.chart,d)}),(0,h.jsx)(w.A,{isRequesting:n,query:s,filters:u,advancedFilters:_})]})}},86771:(e,o,t)=>{t.d(o,{A:()=>f});var r=t(27723),a=t(52619),i=t(86087),c=t(66087),s=t(98846),l=t(96476),n=t(43577),m=t(15703),d=t(94111),u=t(97605),_=t(43128),p=t(33958),v=t(56109),b=t(39793);const w=(0,v.Qk)("manageStock","no"),y=(0,v.Qk)("stockStatuses",{});class h extends i.Component{constructor(){super(),this.getHeadersContent=this.getHeadersContent.bind(this),this.getRowsContent=this.getRowsContent.bind(this),this.getSummary=this.getSummary.bind(this)}getHeadersContent(){return[{label:(0,r.__)("Product / Variation title","woocommerce"),key:"name",required:!0,isLeftAligned:!0},{label:(0,r.__)("SKU","woocommerce"),key:"sku",hiddenByDefault:!0,isSortable:!0},{label:(0,r.__)("Items sold","woocommerce"),key:"items_sold",required:!0,defaultSort:!0,isSortable:!0,isNumeric:!0},{label:(0,r.__)("Net sales","woocommerce"),screenReaderLabel:(0,r.__)("Net sales","woocommerce"),key:"net_revenue",required:!0,isSortable:!0,isNumeric:!0},{label:(0,r.__)("Orders","woocommerce"),key:"orders_count",isSortable:!0,isNumeric:!0},"yes"===w?{label:(0,r.__)("Status","woocommerce"),key:"stock_status"}:null,"yes"===w?{label:(0,r.__)("Stock","woocommerce"),key:"stock",isNumeric:!0}:null].filter(Boolean)}getRowsContent(e=[]){const{query:o}=this.props,t=(0,l.getPersistedQuery)(o),{formatAmount:a,formatDecimal:i,getCurrencyConfig:d}=this.context;return(0,c.map)(e,(e=>{const{items_sold:o,net_revenue:c,orders_count:u,product_id:v,variation_id:h}=e,f=e.extended_info||{},{stock_status:g,stock_quantity:S,low_stock_amount:x,deleted:k,sku:C}=f,q=(A=e,(0,p.xP)(A.extended_info||{}));var A;const F=(0,l.getNewPath)(t,"/analytics/orders",{filter:"advanced",variation_includes:h}),P=(0,m.getAdminLink)(`post.php?post=${v}&action=edit`);return[{display:k?q+" "+(0,r.__)("(Deleted)","woocommerce"):(0,b.jsx)(s.Link,{href:P,type:"wp-admin",children:q}),value:q},{display:C,value:C},{display:(0,n.formatValue)(d(),"number",o),value:o},{display:a(c),value:i(c)},{display:(0,b.jsx)(s.Link,{href:F,type:"wc-admin",children:u}),value:u},"yes"===w?{display:(0,_.n)(g,S,x)?(0,b.jsx)(s.Link,{href:P,type:"wp-admin",children:(0,r._x)("Low","Indication of a low quantity","woocommerce")}):y[g],value:y[g]}:null,"yes"===w?{display:S,value:S}:null].filter(Boolean)}))}getSummary(e){const{query:o}=this.props,{variations_count:t=0,items_sold:i=0,net_revenue:c=0,orders_count:s=0}=e,{formatAmount:l,getCurrencyConfig:m}=this.context,d=m();return[{label:(0,a.applyFilters)("experimental_woocommerce_admin_variations_report_table_summary_variations_count_label",(0,r._n)("variation sold","variations sold",t,"woocommerce"),t,o),value:(0,n.formatValue)(d,"number",t)},{label:(0,r._n)("item sold","items sold",i,"woocommerce"),value:(0,n.formatValue)(d,"number",i)},{label:(0,r.__)("net sales","woocommerce"),value:l(c)},{label:(0,r._n)("orders","orders",s,"woocommerce"),value:(0,n.formatValue)(d,"number",s)}]}render(){const{advancedFilters:e,baseSearchQuery:o,filters:t,isRequesting:i,query:c}=this.props,s={helpText:(0,r.__)("Check at least two variations below to compare","woocommerce"),placeholder:(0,r.__)("Search by variation name or SKU","woocommerce")};return(0,b.jsx)(u.A,{baseSearchQuery:o,compareBy:"variations",compareParam:"filter-variations",endpoint:"variations",getHeadersContent:this.getHeadersContent,getRowsContent:this.getRowsContent,isRequesting:i,itemIdField:"variation_id",labels:s,query:c,getSummary:this.getSummary,summaryFields:["variations_count","items_sold","net_revenue","orders_count"],tableQuery:{orderby:c.orderby||"items_sold",order:c.order||"desc",extended_info:!0,product_includes:c.product_includes,variations:c.variations},title:(0,a.applyFilters)("experimental_woocommerce_admin_variations_report_table_title",(0,r.__)("Variations","woocommerce"),c),columnPrefsKey:"variations_report_columns",filters:t,advancedFilters:e})}}h.contextType=d.CurrencyContext;const f=h}}]);