"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3501],{59255:(e,t,o)=>{o.d(t,{W:()=>a});var s=o(56427),n=o(39793);const a=({size:e="medium"})=>(0,n.jsx)("div",{className:`woocommerce-field-placeholder woocommerce-field-placeholder--${e}`,children:(0,n.jsx)(s.Placeholder,{})})},32905:(e,t,o)=>{o.d(t,{w:()=>i});var s=o(56427),n=o(51609),a=o(39793);const i=({children:e})=>(0,a.jsx)(a.Fragment,{children:e});i.Layout=({children:e})=>((0,n.useEffect)((()=>{const e=document.getElementById("wpbody");e&&e.querySelector(".settings-layout")&&e.classList.add("has-settings-layout")}),[]),(0,a.jsx)("div",{className:"settings-layout",children:e})),i.Section=({title:e,description:t,children:o,id:s})=>(0,a.jsxs)("div",{className:"settings-section",id:s,children:[(0,a.jsxs)("div",{className:"settings-section__details",children:[(0,a.jsx)("h2",{children:e}),(0,a.jsx)("p",{children:t})]}),(0,a.jsx)("div",{className:"settings-section__controls",children:o})]}),i.Actions=({children:e})=>(0,a.jsx)(s.Card,{className:"settings-card__wrapper ",children:(0,a.jsx)(s.CardBody,{className:"form__actions",children:e})}),i.Form=({children:e,onSubmit:t})=>(0,a.jsx)("form",{onSubmit:t,className:"settings-form",children:e})},54794:(e,t,o)=>{o.r(t),o.d(t,{SettingsPaymentsCod:()=>u,default:()=>_});var s=o(56427),n=o(98846),a=o(27723),i=o(47143),r=o(40314),c=o(86087);function l(e){const t=document.createElement("textarea");return t.innerHTML=e,t.value}var d=o(32905),m=o(59255),h=o(39793);const u=()=>{const{createSuccessNotice:e,createErrorNotice:t}=(0,i.useDispatch)("core/notices"),{codSettings:o,isLoading:u}=(0,i.useSelect)((e=>({codSettings:e(r.paymentGatewaysStore).getPaymentGateway("cod"),isLoading:!e(r.paymentGatewaysStore).hasFinishedResolution("getPaymentGateway",["cod"])})),[]),{updatePaymentGateway:_,invalidateResolutionForStoreSelector:p}=(0,i.useDispatch)(r.paymentGatewaysStore),[b,g]=(0,c.useState)({}),[y,w]=(0,c.useState)(!1),[v,x]=(0,c.useState)(!1);return(0,c.useEffect)((()=>{o&&(g({enabled:o.enabled,title:o.settings.title.value,description:o.description,instructions:o.settings.instructions.value,enable_for_methods:Array.isArray(o.settings.enable_for_methods.value)?o.settings.enable_for_methods.value:[],enable_for_virtual:"yes"===o.settings.enable_for_virtual.value}),x(!1))}),[o]),(0,h.jsx)(d.w,{children:(0,h.jsx)(d.w.Layout,{children:(0,h.jsxs)(d.w.Form,{onSubmit:s=>{s.preventDefault(),(()=>{if(!o)return;w(!0);const s={title:String(b.title),instructions:String(b.instructions),enable_for_methods:Array.isArray(b.enable_for_methods)?b.enable_for_methods:[],enable_for_virtual:b.enable_for_virtual?"yes":"no"};_("cod",{enabled:Boolean(b.enabled),description:String(b.description),settings:s}).then((()=>{p("getPaymentGateway"),e((0,a.__)("Settings updated successfully","woocommerce")),w(!1),x(!1)})).catch((()=>{t((0,a.__)("Failed to update settings","woocommerce")),w(!1)}))})()},children:[(0,h.jsxs)(d.w.Section,{title:(0,a.__)("Enable and customise","woocommerce"),description:(0,a.__)("Choose how you want to present cash on delivery payments to your customers during checkout.","woocommerce"),children:[u?(0,h.jsx)(m.W,{size:"small"}):(0,h.jsx)(s.CheckboxControl,{label:(0,a.__)("Enable cash on delivery payments","woocommerce"),checked:Boolean(b.enabled),onChange:e=>{g({...b,enabled:e}),x(!0)}}),u?(0,h.jsx)(m.W,{size:"medium"}):(0,h.jsx)(s.TextControl,{label:(0,a.__)("Title","woocommerce"),help:(0,a.__)("Payment method name that the customer will see during checkout.","woocommerce"),placeholder:(0,a.__)("Cash on delivery payments","woocommerce"),value:String(b.title),onChange:e=>{g({...b,title:e}),x(!0)}}),u?(0,h.jsx)(m.W,{size:"large"}):(0,h.jsx)(s.TextareaControl,{label:(0,a.__)("Description","woocommerce"),help:(0,a.__)("Payment method description that the customer will see during checkout.","woocommerce"),value:String(b.description),onChange:e=>{g({...b,description:e}),x(!0)}}),u?(0,h.jsx)(m.W,{size:"large"}):(0,h.jsx)(s.TextareaControl,{label:(0,a.__)("Instructions","woocommerce"),help:(0,a.__)("Instructions that will be added to the thank you page and emails.","woocommerce"),value:String(b.instructions),onChange:e=>{g({...b,instructions:e}),x(!0)}}),u||!o?(0,h.jsx)(m.W,{size:"medium"}):(0,h.jsx)(n.TreeSelectControl,{label:(0,a.__)("Enable for shipping methods","woocommerce"),help:(0,a.__)("Select shipping methods for which this payment method is enabled.","woocommerce"),options:o.settings.enable_for_methods?.options?(f=o.settings.enable_for_methods.options,Object.entries(f).map((([e,t])=>({label:l(e),value:e,children:Object.entries(t).map((([e,t])=>({value:e,label:l(t)})))})))):[],value:Array.isArray(b.enable_for_methods)?b.enable_for_methods:[],onChange:e=>{g({...b,enable_for_methods:e}),x(!0)},selectAllLabel:!1}),u?(0,h.jsx)(m.W,{size:"small"}):(0,h.jsx)(s.CheckboxControl,{label:(0,a.__)("Accept for virtual orders","woocommerce"),help:(0,a.__)("Accept cash on delivery if the order is virtual","woocommerce"),checked:Boolean(b.enable_for_virtual),onChange:e=>{g({...b,enable_for_virtual:e}),x(!0)}})]}),(0,h.jsx)(d.w.Actions,{children:(0,h.jsx)(s.Button,{variant:"primary",type:"submit",isBusy:y,disabled:y||!v,children:(0,a.__)("Save changes","woocommerce")})})]})})});var f},_=u}}]);