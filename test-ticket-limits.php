<?php
/**
 * Test Ticket Limits
 * 
 * This script tests the updated ticket limits
 * Run this by visiting: yoursite.com/test-ticket-limits.php
 */

// Load WordPress
require_once('wp-config.php');

// Prevent direct access from non-admin users
if (!current_user_can('administrator')) {
    wp_die('Access denied. Administrator privileges required.');
}

echo "<h1>🎫 Ticket Limits Test</h1>";

// Get a sample competition
$competitions = get_posts(array(
    'post_type' => 'product',
    'meta_query' => array(
        array(
            'key' => '_competition_enabled',
            'value' => 'yes',
            'compare' => '='
        )
    ),
    'posts_per_page' => 1,
    'post_status' => 'publish'
));

if (empty($competitions)) {
    echo "<div style='color: red; padding: 10px; border: 1px solid red; margin: 10px 0;'>";
    echo "<strong>❌ No competitions found for testing</strong>";
    echo "</div>";
    exit;
}

$competition = $competitions[0];
$competition_id = $competition->ID;

echo "<h2>📊 Testing Competition: " . esc_html($competition->post_title) . "</h2>";

// Get competition data
$max_tickets = get_post_meta($competition_id, '_competition_max_tickets', true);
$sold_tickets = get_post_meta($competition_id, '_competition_sold_tickets', true) ?: 0;
$remaining_tickets = $max_tickets - $sold_tickets;

echo "<div style='background: #f8f9fa; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Competition Details:</h3>";
echo "<ul>";
echo "<li><strong>Competition ID:</strong> " . $competition_id . "</li>";
echo "<li><strong>Max Tickets:</strong> " . number_format($max_tickets) . "</li>";
echo "<li><strong>Sold Tickets:</strong> " . number_format($sold_tickets) . "</li>";
echo "<li><strong>Remaining Tickets:</strong> " . number_format($remaining_tickets) . "</li>";
echo "</ul>";
echo "</div>";

// Test the limits
echo "<h2>🔍 Ticket Limit Tests</h2>";

$test_quantities = [1, 5, 10, 50, 100, 250, 500, 999, 1000];

foreach ($test_quantities as $quantity) {
    $max_allowed = min(999, $remaining_tickets);
    $is_valid = $quantity <= $max_allowed;
    
    $status_color = $is_valid ? 'green' : 'red';
    $status_icon = $is_valid ? '✅' : '❌';
    
    echo "<div style='color: {$status_color}; padding: 5px; margin: 5px 0;'>";
    echo "<strong>{$status_icon} {$quantity} tickets:</strong> ";
    
    if ($quantity <= 999) {
        if ($quantity <= $remaining_tickets) {
            echo "ALLOWED (within system limit and available tickets)";
        } else {
            echo "BLOCKED (exceeds available tickets: " . number_format($remaining_tickets) . ")";
        }
    } else {
        echo "BLOCKED (exceeds system limit of 999)";
    }
    echo "</div>";
}

// Test slider limits
echo "<h2>🎚️ Slider Configuration Test</h2>";

$slider_max = min(999, $remaining_tickets);

echo "<div style='background: #e3f2fd; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Slider Settings:</h3>";
echo "<ul>";
echo "<li><strong>Minimum:</strong> 1</li>";
echo "<li><strong>Maximum:</strong> " . number_format($slider_max) . " (min of 999 and remaining tickets)</li>";
echo "<li><strong>Default Value:</strong> 5</li>";
echo "</ul>";

if ($slider_max >= 999) {
    echo "<div style='color: green; font-weight: bold; margin-top: 10px;'>";
    echo "✅ Slider can reach the full 999 ticket limit!";
    echo "</div>";
} else {
    echo "<div style='color: orange; font-weight: bold; margin-top: 10px;'>";
    echo "⚠️ Slider limited by available tickets (" . number_format($remaining_tickets) . ")";
    echo "</div>";
}
echo "</div>";

// Test purchase validation
echo "<h2>🛒 Purchase Validation Test</h2>";

echo "<div style='background: #f3e5f5; padding: 20px; border-radius: 8px; margin: 20px 0;'>";
echo "<h3>Purchase Limits:</h3>";
echo "<ul>";
echo "<li><strong>Maximum per purchase:</strong> 999 tickets</li>";
echo "<li><strong>Free competitions:</strong> 1 ticket only</li>";
echo "<li><strong>Multiple purchases:</strong> Allowed for paid competitions</li>";
echo "</ul>";

// Get product price
$product = wc_get_product($competition_id);
$price = $product ? $product->get_price() : 0;

echo "<p><strong>This competition price:</strong> £" . number_format($price, 2) . "</p>";

if ($price == 0) {
    echo "<div style='color: blue; font-weight: bold;'>";
    echo "ℹ️ This is a FREE competition - limited to 1 ticket per person";
    echo "</div>";
} else {
    echo "<div style='color: green; font-weight: bold;'>";
    echo "✅ This is a PAID competition - up to 999 tickets per purchase allowed";
    echo "</div>";
}
echo "</div>";

echo "<h2>🔗 Quick Links</h2>";
echo "<p><a href='/product/" . $competition->post_name . "/' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>View This Competition</a></p>";
echo "<p><a href='/competitions/' style='background: #0073aa; color: white; padding: 10px 20px; text-decoration: none; border-radius: 3px;'>View All Competitions</a></p>";

echo "<h2>📋 Summary</h2>";
echo "<div style='background: #d4edda; padding: 20px; border-radius: 8px; border: 1px solid #c3e6cb;'>";
echo "<h3 style='color: #155724;'>✅ Ticket Limits Successfully Updated!</h3>";
echo "<ul style='color: #155724;'>";
echo "<li>Maximum tickets per purchase: <strong>999</strong> (increased from 50)</li>";
echo "<li>Slider maximum: <strong>999</strong> (increased from 250)</li>";
echo "<li>Info bar text updated to show <strong>999</strong> maximum</li>";
echo "<li>All validation logic updated</li>";
echo "<li>Automatically adjusts if competition has fewer than 999 tickets available</li>";
echo "</ul>";
echo "</div>";
?>
