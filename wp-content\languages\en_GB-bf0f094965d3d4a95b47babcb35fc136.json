{"translation-revision-date": "2025-04-06 12:37:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Word count type. Do not translate!\u0004words": ["words"], "Keeps the text cursor within blocks while navigating with arrow keys, preventing it from moving to other blocks and enhancing accessibility for keyboard users.": ["Keeps the text cursor within blocks while navigating with arrow keys, preventing it from moving to other blocks and enhancing accessibility for keyboard users."], "panel button label\u0004Settings": ["Settings"], "noun, panel\u0004Document": ["Document"], "Changes will be applied to all selected %s.": ["Changes will be applied to all selected %s."], "%i %s": ["%i %s"], "Set as posts page": ["Set as posts page"], "Set posts page": ["Set posts page"], "Set \"%1$s\" as the posts page? %2$s": ["Set \"%1$s\" as the posts page? %2$s"], "This page will show the latest posts.": ["This page will show the latest posts."], "This will replace the current posts page: \"%s\"": ["This will replace the current posts page: \"%s\""], "An error occurred while setting the posts page.": ["An error occurred while setting the posts page."], "Posts page updated.": ["Posts page updated."], "Set as homepage": ["Set as homepage"], "Set homepage": ["Set homepage"], "Set \"%1$s\" as the site homepage? %2$s": ["Set \"%1$s\" as the site homepage? %2$s"], "This will replace the current homepage: \"%s\"": ["This will replace the current homepage: \"%s\""], "This will replace the current homepage which is set to display latest posts.": ["This will replace the current homepage which is set to display latest posts."], "Homepage updated.": ["Homepage updated."], "Enter or exit zoom out.": ["Enter or exit zoom out."], "Comment deleted successfully.": ["Comment deleted successfully."], "Something went wrong. Please try publishing the post, or you may have already submitted your comment earlier.": ["Something went wrong. Please try publishing the post, or you may have already submitted your comment earlier."], "Comment edited successfully.": ["<PERSON><PERSON><PERSON> edited successfully."], "Comment marked as resolved.": ["Comment marked as resolved."], "Comment added successfully.": ["Comment added successfully."], "Reply added successfully.": ["Reply added successfully."], "View comment\u0004Comment": ["Comment"], "Add comment button\u0004Comment": ["Comment"], "Are you sure you want to mark this comment as resolved?": ["Are you sure you want to mark this comment as resolved?"], "verb\u0004Update": ["Update"], "Resolved": ["Resolved"], "Select comment action\u0004Select an action": ["Select an action"], "Mark comment as resolved\u0004Resolve": ["Resolve"], "Delete comment\u0004Delete": ["Delete"], "Edit comment\u0004Edit": ["Edit"], "Add reply comment\u0004Reply": ["Reply"], "Show replies button\u0004%s more replies..": ["%s more replies.."], "No comments available": ["No comments available"], "Cancel comment button\u0004Cancel": ["Cancel"], "User avatar": ["User avatar"], "<span>Customize the last part of the Permalink.</span> <a>Learn more.</a>": ["<span>Customise the last part of the Permalink.</span> <a>Learn more.</a>"], "Your work will be reviewed and then approved.": ["Your work will be reviewed and then approved."], "Could not retrieve the featured image data.": ["Could not retrieve the featured image data."], "Copy error": ["Copy error"], "Copy contents": ["Copy contents"], "This change will affect other parts of your site that use this template.": ["This change will affect other parts of your site that use this template."], "Enter or exit distraction free mode.": ["Enter or exit distraction free mode."], "Show or hide the List View.": ["Show or hide the List View."], "Content preview": ["Content preview"], "Empty content": ["Empty content"], "Open Site Editor": ["Open Site Editor"], "Enter Spotlight mode": ["Enter Spotlight mode"], "Exit Spotlight mode": ["Exit Spotlight mode"], "Change template": ["Change template"], "Child pages inherit characteristics from their parent, such as URL structure. For instance, if \"Pricing\" is a child of \"Services\", its URL would be %1$s<wbr />/services<wbr />/pricing.": ["Child pages inherit characteristics from their parent, such as URL structure. For instance, if \"Pricing\" is a child of \"Services\", its URL would be %1$s<wbr />/services<wbr />/pricing."], "Customize the last part of the Permalink.": ["Customise the last part of the Permalink."], "Copied Permalink to clipboard.": ["Copied <PERSON><PERSON><PERSON> to clipboard."], "Choose an image…": ["Choose an image…"], "Are you sure you want to permanently delete \"%s\"?": ["Are you sure you want to permanently delete \"%s\"?"], "Are you sure you want to permanently delete %d item?": ["Are you sure you want to permanently delete %d item?", "Are you sure you want to permanently delete %d items?"], "An error occurred while reverting the items.": ["An error occurred while reverting the items."], "Top toolbar deactivated.": ["Top toolbar deactivated."], "Top toolbar activated.": ["Top toolbar activated."], "verb\u0004Upload": ["Upload"], "noun, breadcrumb\u0004Document": ["Document"], "template part\u0004Delete \"%s\"?": ["Delete \"%s\"?"], "template part\u0004%s (Copy)": ["%s (Copy)"], "template part\u0004\"%s\" duplicated.": ["\"%s\" duplicated."], "settings landmark area\u0004Settings": ["Settings"], "field\u0004Edit %s": ["Edit %s"], "Are you sure you want to move \"%s\" to the trash?": ["Are you sure you want to move \"%s\" to the bin?"], "Approval step": ["Approval step"], "Require approval step when optimizing existing media.": ["Require approval step when optimising existing media."], "Pre-upload compression": ["Pre-upload compression"], "Compress media items before uploading to the server.": ["Compress media items before uploading to the server."], "Customize options related to the media upload flow.": ["Customise options related to the media upload flow."], "Show starter patterns": ["Show starter patterns"], "Shows starter patterns when creating a new page.": ["Shows starter patterns when creating a new page."], "Zoom Out": ["Zoom out"], "Unlock content locked blocks\u0004Unlock": ["Unlock"], "Change status: %s": ["Change status: %s"], "Pin this post to the top of the blog": ["Pin this post to the top of the blog"], "Upload failed, try again.": ["Upload failed, try again."], "Edit or replace the featured image": ["Edit or replace the featured image"], "Adjective: e.g. \"Comments are open\"\u0004Open": ["Open"], "(No author)": ["(No author)"], "They also show up as sub-items in the default navigation menu. <a>Learn more.</a>": ["They also show up as sub-items in the default navigation menu. <a>Learn more.</a>"], "Determines the order of pages. Pages with the same order value are sorted alphabetically. Negative order values are supported.": ["Determines the order of pages. Pages with the same order value are sorted alphabetically. Negative order values are supported."], "Order updated.": ["Order updated."], "Determines the order of pages.": ["Determines the order of pages."], "verb\u0004View": ["View"], "An error occurred while updating.": ["An error occurred while updating."], "Visitors cannot add new comments or replies. Existing comments remain visible.": ["Visitors cannot add new comments or replies. Existing comments remain visible."], "Author avatar": ["Author avatar"], "Select item": ["Select item"], "date order\u0004dmy": ["dmy"], "Some errors occurred while reverting the items: %s": ["Some errors occurred while reverting the items: %s"], "An error occurred while reverting the item.": ["An error occurred while reverting the item."], "Comments closed": ["Comments closed"], "Comments open": ["Comments open"], "Change discussion settings": ["Change discussion settings"], "Change posts per page": ["Change posts per page"], "Change blog title: %s": ["Change blog title: %s"], "Only visible to those who know the password": ["Only visible to those who know the password"], "Change format: %s": ["Change format: %s"], "%1$s, %2$s read time.": ["%1$s, %2$s read time."], "1 minute": ["1 minute"], "Reset to default and clear all customizations?": ["Reset to default and clear all customisations?"], "%s items reset.": ["%s items reset."], "Name updated": ["Name updated"], "View revisions (%s)": ["View revisions (%s)"], "%d pages have been restored.": ["%d pages have been restored."], "%s item moved to the trash.": ["%s item moved to the bin.", "%s items moved to the bin."], "patterns-export": ["patterns-export"], "You’ve tried to select a block that is part of a template that may be used elsewhere on your site. Would you like to edit the template?": ["You’ve tried to select a block that is part of a template that may be used elsewhere on your site. Would you like to edit the template?"], "Temporarily unlock the parent block to edit, delete or make further changes to this block.": ["Temporarily unlock the parent block to edit, delete, or make further changes to this block."], "Edit pattern": ["Edit pattern"], "Only users with permissions to edit the template can move or delete this block": ["Only users with permissions to edit the template can move or delete this block"], "Edit the template to move, delete, or make further changes to this block.": ["Edit the template to move, delete, or make further changes to this block."], "Edit the pattern to move, delete, or make further changes to this block.": ["Edit the pattern to move, delete, or make further changes to this block."], "The deleted block allows instance overrides. Removing it may result in content not displaying where this pattern is used. Are you sure you want to proceed?": ["The deleted block allows instance overrides. Removing it may result in content not displaying where this pattern is used. Are you sure you want to proceed?", "Some of the deleted blocks allow instance overrides. Removing them may result in content not displaying where this pattern is used. Are you sure you want to proceed?"], "Change link: %s": ["Change link: %s"], "Unschedule": ["Unschedule"], "Unpublish": ["Unpublish"], "Edit excerpt": ["Edit excerpt"], "Edit description": ["Edit description"], "Add an excerpt…": ["Add an excerpt…"], "Add a description…": ["Add a description…"], "Write a description": ["Write a description"], "Write a description (optional)": ["Write a description (optional)"], "Change discussion options": ["Change discussion options"], "Pings enabled": ["Pings enabled"], "Pings only": ["Pings only"], "Comments only": ["Comments only"], "Learn more about pingbacks & trackbacks": ["Learn more about pingbacks and trackbacks"], "https://wordpress.org/documentation/article/trackbacks-and-pingbacks/": ["https://wordpress.org/documentation/article/trackbacks-and-pingbacks/"], "Comment status": ["Comment status"], "Existing comments remain visible.": ["Existing comments remain visible."], "Visitors cannot add new comments or replies.": ["Visitors cannot add new comments or replies."], "Closed": ["Closed"], "Visitors can add new comments and replies.": ["Visitors can add new comments and replies."], "Open": ["Open"], "Change author: %s": ["Change author: %s"], "Show template": ["Show template"], "https://wordpress.org/documentation/article/page-post-settings-sidebar/#page-attributes": ["https://wordpress.org/documentation/article/page-post-settings-sidebar/#page-attributes"], "Child pages inherit characteristics from their parent, such as URL structure. For instance, if \"Pricing\" is a child of \"Services\", its URL would be %s<wbr />/services<wbr />/pricing.": ["Child pages inherit characteristics from their parent, such as URL structure. For instance, if \"Pricing\" is a child of \"Services\", its URL would be %s<wbr />/services<wbr />/pricing."], "Change parent: %s": ["Change parent: %s"], "Choose a parent page.": ["Choose a parent page."], "Set the page order.": ["Set the page order."], "Post Meta.": ["Post meta."], "Items deleted.": ["Items deleted."], "Items reset.": ["Items reset."], "\"%s\" reset.": ["\"%s\" reset."], "Template reset.": ["Template reset."], "List View shortcuts": ["List view shortcuts"], "pattern (singular)\u0004Not synced": ["Not synced"], "<span>Published: <time>%s</time></span>": ["<span>Published: <time>%s</time></span>"], "<span>Scheduled: <time>%s</time></span>": ["<span>Scheduled: <time>%s</time></span>"], "<span>Modified: <time>%s</time></span>": ["<span>Modified: <time>%s</time></span>"], "pattern (singular)\u0004Synced": ["Synced"], "Add non breaking space.": ["Add non-breaking space."], "An error occurred while reverting the templates.": ["An error occurred while reverting the templates."], "An error occurred while restoring the posts: %s": ["An error occurred while restoring the posts: %s"], "An error occurred while deleting the items.": ["An error occurred while deleting the items."], "An error occurred while restoring the post.": ["An error occurred while restoring the post."], "An error occurred while restoring the posts.": ["An error occurred while restoring the posts."], "Reset template: %s": ["Reset template: %s"], "An error occurred while reverting the items: %s": ["An error occurred while reverting the items: %s"], "Manage block visibility": ["Manage block visibility"], "Adds a category with the most frequently used blocks in the inserter.": ["Adds a category with the most frequently used blocks in the inserter."], "Inserter": ["Inserter"], "Show text instead of icons on buttons across the interface.": ["Show text instead of icons on buttons across the interface."], "Optimize the editing experience for enhanced control.": ["Optimise the editing experience for enhanced control."], "Accessibility": ["Accessibility"], "Access all block and document tools in a single place.": ["Access all block and document tools in a single place."], "Customize the editor interface to suit your needs.": ["Customise the editor interface to suit your needs."], "Select what settings are shown in the document panel.": ["Select what settings are shown in the document panel."], "Allow right-click contextual menus": ["Allow right-click contextual menus"], "Allows contextual List View menus via right-click, overriding browser defaults.": ["Allows contextual list view menus via right-click, overriding browser defaults."], "Display the block hierarchy trail at the bottom of the editor.": ["Display the block hierarchy trail at the bottom of the editor."], "Interface": ["Interface"], "Are you sure you want to move %d item to the trash ?": ["Are you sure you want to move %d item to the bin?", "Are you sure you want to move %d items to the bin?"], "Upload external images to the Media Library. Images from different domains may load slowly, display incorrectly, or be removed unexpectedly.": ["Upload external images to the Media Library. Images from different domains may load slowly, display incorrectly, or be removed unexpectedly."], "Create new template": ["Create new template"], "There is <strong>%d site change</strong> waiting to be saved.": ["There is <strong>%d site change</strong> waiting to be saved.", "There are <strong>%d site changes</strong> waiting to be saved."], "The following has been modified.": ["The following has been modified."], "Delete %d item?": ["Delete %d item?", "Delete %d items?"], "action label\u0004Duplicate template part": ["Duplicate template part"], "action label\u0004Duplicate pattern": ["Duplicate pattern"], "action label\u0004Duplicate": ["Duplicate"], "Some errors occurred while restoring the posts: %s": ["Some errors occurred while restoring the posts: %s"], "\"%s\" has been restored.": ["\"%s\" has been restored."], "%d posts have been restored.": ["%d posts have been restored."], "Some errors occurred while permanently deleting the items: %s": ["Some errors occurred while permanently deleting the items: %s"], "An error occurred while permanently deleting the items: %s": ["An error occurred while permanently deleting the items: %s"], "An error occurred while permanently deleting the items.": ["An error occurred while permanently deleting the items."], "An error occurred while permanently deleting the item.": ["An error occurred while permanently deleting the item."], "The items were permanently deleted.": ["The items were permanently deleted."], "\"%s\" permanently deleted.": ["\"%s\" permanently deleted."], "Permanently delete": ["Permanently delete"], "Some errors occurred while moving the items to the trash: %s": ["Some errors occurred while moving the items to the bin: %s"], "An error occurred while moving the item to the trash: %s": ["An error occurred while moving the item to the bin: %s"], "An error occurred while moving the items to the trash.": ["An error occurred while moving the items to the bin."], "Revisions (%s)": ["Revisions (%s)"], "Duplicate pattern": ["Duplicate pattern"], "Rename pattern": ["Rename pattern"], "Edit template: %s": ["Edit template: %s"], "Some errors occurred while deleting the items: %s": ["Some errors occurred while deleting the items: %s"], "An error occurred while deleting the items: %s": ["An error occurred while deleting the items: %s"], "template part\u0004\"%s\" deleted.": ["\"%s\" deleted."], "Pre-publish checks enabled.": ["Pre-publish checks enabled."], "Pre-publish checks disabled.": ["Pre-publish checks disabled."], "List View on.": ["List view on."], "List View off.": ["List view off."], "Close List View": ["Close list view"], "Enter Distraction free": ["Enter Distraction free"], "Exit Distraction free": ["Exit Distraction free"], "header landmark area\u0004Header": ["Header"], "Deleting this block will stop your post or page content from displaying on this template. It is not recommended.": ["Deleting this block will stop your post or page content from displaying on this template. It is not recommended.", "Deleting these blocks will stop your post or page content from displaying on this template. It is not recommended."], "An error occurred while reverting the template.": ["An error occurred while reverting the template."], "Export as JSON": ["Export as JSON"], "Open code editor": ["Open code editor"], "Use default template": ["Use default template"], "Choose a template": ["Choose a template"], "An error occurred while reverting the template part.": ["An error occurred while reverting the template part."], "Preview in a new tab": ["Preview in a new tab"], "Disable pre-publish checks": ["Disable pre-publish checks"], "Breadcrumbs visible.": ["Breadcrumbs visible."], "Breadcrumbs hidden.": ["Breadcrumbs hidden."], "Show block breadcrumbs": ["Show block breadcrumbs"], "Hide block breadcrumbs": ["Hide block breadcrumbs"], "Editor preferences": ["Editor preferences"], "You can enable the visual editor in your profile settings.": ["You can enable the visual editor in your profile settings."], "Changes will apply to new posts only. Individual posts may override these settings.": ["Changes will apply to new posts only. Individual posts may override these settings."], "Post overview\u0004Outline": ["Outline"], "Post overview\u0004List View": ["List View"], "https://wordpress.org/documentation/article/page-post-settings-sidebar/#permalink": ["https://wordpress.org/documentation/article/page-post-settings-sidebar/#permalink"], "Sync status": ["Sync status"], "External media": ["External media"], "Select image block.": ["Select image block."], "https://wordpress.org/documentation/article/page-post-settings-sidebar/#excerpt": ["https://wordpress.org/documentation/article/page-post-settings-sidebar/#excerpt"], "Reset template part: %s": ["Reset template part: %s"], "Document not found": ["Document not found"], "Fallback content": ["Fallback content"], "Last edited %s.": ["Last edited %s."], "Change publish date": ["Change publish date"], "An error occurred while updating the name": ["An error occurred while updating the name"], "Publish automatically on a chosen date.": ["Publish automatically on a chosen date."], "Waiting for review before publishing.": ["Waiting for review before publishing."], "Not ready to publish.": ["Not ready to publish."], "An error occurred while moving the item to the trash.": ["An error occurred while moving the item to the bin."], "\"%s\" moved to the trash.": ["\"%s\" moved to the bin."], "Parent": ["Parent"], "An error occurred while updating the order": ["An error occurred while updating the order"], "No title": ["No title"], "This pattern cannot be edited.": ["This pattern cannot be edited."], "Set the default number of posts to display on blog pages, including categories and tags. Some templates may override this setting.": ["Set the default number of posts to display on blog pages, including categories and tags. Some templates may override this setting."], "Posts per page": ["Posts per page"], "Set the Posts Page title. Appears in search results, and when the page is shared on social media.": ["Set the Posts Page title. Appears in search results, and when the page is shared on social media."], "Are you sure you want to delete this comment?": ["Are you sure you want to delete this comment?"], "https://wordpress.org/documentation/article/wordpress-block-editor/": ["https://wordpress.org/documentation/article/wordpress-block-editor/"], "Hide block tools": ["Hide block tools"], "Show block tools": ["Show block tools"], "caption\u0004<a %1$s>Work</a>/ %2$s": ["<a %1$s>Work</a>/ %2$s"], "Search Openverse": ["Search Openverse"], "Openverse": ["Openverse"], "Search audio": ["Search audio"], "Search videos": ["Search videos"], "Search images": ["Search images"], "caption\u0004\"%1$s\"/ %2$s": ["\"%1$s\"/ %2$s"], "caption\u0004<a %1$s>Work</a> by %2$s/ %3$s": ["<a %1$s>Work</a> by %2$s/ %3$s"], "caption\u0004\"%1$s\" by %2$s/ %3$s": ["\"%1$s\" by %2$s/ %3$s"], "Navigate the structure of your document and address issues like empty or incorrect heading levels.": ["Navigate the structure of your document and address issues like empty or incorrect heading levels."], "Time to read:": ["Time to read:"], "Words:": ["Words:"], "Characters:": ["Characters:"], "Distraction free mode deactivated.": ["Distraction free mode deactivated."], "Distraction free mode activated.": ["Distraction free mode activated."], "Write with calmness": ["Write with calmness"], "Document Overview": ["Document Overview"], "Distraction free": ["Distraction free"], "Reduce visual distractions by hiding the toolbar and other elements to focus on writing.": ["Reduce visual distractions by hiding the toolbar and other elements to focus on writing."], "Convert the current paragraph or heading to a heading of level 1 to 6.": ["Convert the current paragraph or heading to a heading of level 1 to 6."], "Convert the current heading to a paragraph.": ["Convert the current heading to a paragraph."], "Sticky": ["<PERSON>y"], "Time to read": ["Time to read"], "<span>%s</span> minute": ["<span>%s</span> minute", "<span>%s</span> minutes"], "<span>< 1</span> minute": ["<span>< 1</span> minute"], "post schedule date format without year\u0004F j g:i a": ["F j g:i a"], "Tomorrow at %s": ["Tomorrow at %s"], "post schedule time format\u0004g:i a": ["g:i a"], "Today at %s": ["Today at %s"], "post schedule full date format\u0004F j, Y g:i a": ["F j, Y g:i a"], "Control how this post is viewed.": ["Control how this post is viewed."], "Only those with the password can view this post.": ["Only those with the password can view this post."], "Apply suggested format: %s": ["Apply suggested format: %s"], "Create template part": ["Create template part"], "View site": ["View site"], "Default template": ["Default template"], "Edit template": ["Edit template"], "Add template": ["Add template"], "Templates define the way content is displayed when viewing your site.": ["Templates define the way content is displayed when viewing your site."], "Describe the template, e.g. \"Post with sidebar\". A custom template can be manually applied to any post or page.": ["Describe the template, e.g. \"Post with sidebar\". A custom template can be manually applied to any post or page."], "Change date: %s": ["Change date: %s"], "Always open List View": ["Always open list view"], "Opens the List View panel by default.": ["Opens the List View panel by default."], "Copy all blocks": ["Copy all blocks"], "Make the selected text inline code.": ["Make the selected text inline code."], "Strikethrough the selected text.": ["Strikethrough the selected text."], "View options": ["View options"], "Categories provide a helpful way to group related posts together and to quickly tell readers what a post is about.": ["Categories provide a helpful way to group related posts together and to quickly tell readers what a post is about."], "Assign a category": ["Assign a category"], "If you take over, the other user will lose editing control to the post, but their changes will be saved.": ["If you take over, the other user will lose editing control of the post, but their changes will be saved."], "<strong>%s</strong> is currently working on this post (<PreviewLink />), which means you cannot make changes, unless you take over.": ["<strong>%s</strong> is currently working on this post (<PreviewLink />), which means you cannot make changes, unless you take over."], "preview": ["preview"], "<strong>%s</strong> now has editing control of this post (<PreviewLink />). Don’t worry, your changes up to this moment have been saved.": ["<strong>%s</strong> now has editing control of this post (<PreviewLink />). Don’t worry, your changes up to this moment have been saved."], "Exit editor": ["Exit editor"], "These changes will affect your whole site.": ["These changes will affect your whole site."], "View Preview": ["View Preview"], "Draft saved.": ["Draft saved."], "Show button text labels": ["Show button text labels"], "The posts page template cannot be changed.": ["The posts page template cannot be changed."], "Insert a link to a post or page.": ["Insert a link to a post or page."], "Choose a pattern": ["Choose a pattern"], "Site updated.": ["Site updated."], "Saving failed.": ["Saving failed."], "This change will affect your whole site.": ["This change will affect your whole site.", "These changes will affect your whole site."], "An error occurred while reverting the template parts.": ["An error occurred while reverting the template parts."], "Rename": ["<PERSON><PERSON>"], "An error occurred while setting the homepage.": ["An error occurred while setting the homepage."], "An error occurred while creating the template part.": ["An error occurred while creating the template part."], "You attempted to edit an item that doesn't exist. Perhaps it was deleted?": ["You attempted to edit an item that doesn't exist. Perhaps it was deleted?"], "Show or hide the Block settings panel": ["Show or hide the Block settings panel"], "Use left and right arrow keys to resize the canvas.": ["Use left and right arrow keys to resize the canvas."], "Drag to resize": ["Drag to resize"], "Template part created.": ["Template part created."], "Template revert failed. Please reload.": ["Template revert failed. Please reload."], "The editor has encountered an unexpected error. Please reload.": ["The editor has encountered an unexpected error. Please reload."], "This template is not revertable.": ["This template is not revertible."], "An error occurred while deleting the item.": ["An error occurred while deleting the item."], "Disable blocks that you don't want to appear in the inserter. They can always be toggled back on later.": ["Disable blocks that you don't want to appear in the inserter. They can always be toggled back on later."], "Review settings, such as visibility and tags.": ["Review settings, such as visibility and tags."], "Publishing": ["Publishing"], "Area": ["Area"], "Open List View": ["Open list view"], "Template Parts": ["Template Parts"], "Featured Image": ["Featured Image"], "Select the items you want to save.": ["Select the items you want to save."], "Saving…": ["Saving…"], "Create custom template": ["Create custom template"], "Custom Template": ["Custom Template"], "Template options": ["Template options"], "Generic label for block inserter button\u0004Block Inserter": ["Block Inserter"], "Custom template created. You're in template mode now.": ["Custom template created. You're in template mode now."], "Templates": ["Templates"], "Block Library": ["Block Library"], "Editing template. Changes made here affect all posts and pages that use the template.": ["Editing template. Changes made here affect all posts and pages that use the template."], "Highlights the current block and fades other content.": ["Highlights the current block and fades other content."], "Appearance": ["Appearance"], "Show most used blocks": ["Show most used blocks"], "Preferences": ["Preferences"], "Characters": ["Characters"], "Design": ["Design"], "Open save panel": ["Open save panel"], "Preview in new tab": ["Preview in new tab"], "Contain text cursor inside block": ["Contain text cursor inside block"], "Mobile": ["Mobile"], "Tablet": ["Tablet"], "Desktop": ["Desktop"], "Save draft": ["Save draft"], "Are you ready to save?": ["Are you ready to save?"], "Template Part": ["Template Part"], "Password protected": ["Password protected"], "View post": ["View post"], "Page attributes": ["Page attributes"], "Keyboard shortcuts": ["Keyboard shortcuts"], "Editor footer": ["Editor footer"], "Slug": ["Slug"], "Pending review": ["Pending review"], "All content copied.": ["All content copied."], "Display these keyboard shortcuts.": ["Display these keyboard shortcuts."], "Skip": ["<PERSON><PERSON>"], "Restore the backup": ["<PERSON>ore the backup"], "The backup of this post in your browser is different from the version below.": ["The backup of this post in your browser is different from the version below."], "Details": ["Details"], "Spotlight mode deactivated.": ["Spotlight mode deactivated."], "Spotlight mode activated.": ["Spotlight mode activated."], "Search Terms": ["Search Terms"], "The current image has no alternative text. The file name is: %s": ["The current image has no alternative text. The file name is: %s"], "%s minute": ["%s minute", "%s minutes"], "Top toolbar": ["Top toolbar"], "%s word": ["%s word", "%s words"], "To edit the featured image, you need permission to upload media.": ["To edit the featured image, you need permission to upload media."], "Start writing with text or HTML": ["Start writing with text or HTML"], "Type text or HTML": ["Type text or HTML"], "Scheduled": ["Scheduled"], "Create": ["Create"], "Code editor selected": ["Code editor selected"], "Visual editor selected": ["Visual editor selected"], "Exit code editor": ["Exit code editor"], "Editing code": ["Editing code"], "Editor settings": ["Editor settings"], "Close Settings": ["Close Settings"], "Document settings": ["Document settings"], "Visibility": ["Visibility"], "Status & visibility": ["Status & visibility"], "Pin to toolbar": ["Pin to toolbar"], "Unpin from toolbar": ["Unpin from toolbar"], "Close plugin": ["Close plugin"], "Discussion": ["Discussion"], "Enable pre-publish checks": ["Enable pre-publish checks"], "Open publish panel": ["Open publish panel"], "Editor content": ["Editor content"], "Editor publish": ["Editor publish"], "Remove a link.": ["Remove a link."], "Convert the selected text into a link.": ["Convert the selected text into a link."], "Underline the selected text.": ["Underline the selected text."], "Make the selected text italic.": ["Make the selected text italic."], "Make the selected text bold.": ["Make the selected text bold."], "Text formatting": ["Text formatting"], "Forward-slash": ["Forward-slash"], "Change the block type after adding a new paragraph.": ["Change the block type after adding a new paragraph."], "Block shortcuts": ["Block shortcuts"], "Selection shortcuts": ["Selection shortcuts"], "Switch between visual editor and code editor.": ["Switch between visual editor and code editor."], "Navigate to the previous part of the editor.": ["Navigate to the previous part of the editor."], "Navigate to the next part of the editor.": ["Navigate to the next part of the editor."], "Show or hide the Settings panel.": ["Show or hide the Settings panel."], "Redo your last undo.": ["Redo your last undo."], "Undo your last changes.": ["Undo your last changes."], "Save your changes.": ["Save your changes."], "Global shortcuts": ["Global shortcuts"], "Focus on one block at a time": ["Focus on one block at a time"], "Spotlight mode": ["Spotlight mode"], "Access all block and document tools in a single place": ["Access all block and document tools in a single place"], "noun\u0004View": ["View"], "Options": ["Options"], "Editor": ["Editor"], "Code editor": ["Code editor"], "Visual editor": ["Visual editor"], "Editor top bar": ["Editor top bar"], "Document tools": ["Document tools"], "Trashing failed": ["Binning failed"], "Updating failed.": ["Updating failed."], "Scheduling failed.": ["Scheduling failed."], "Publishing failed.": ["Publishing failed."], "You have unsaved changes. If you proceed, they will be lost.": ["You have unsaved changes. If you proceed, they will be lost."], "Reset the template": ["Reset the template"], "Keep it as is": ["Keep it as is"], "The content of your post doesn’t match the template assigned to your post type.": ["The content of your post doesn’t match the template assigned to your post type."], "Resetting the template may result in loss of content, do you want to continue?": ["Resetting the template may result in loss of content, do you want to continue?"], "Document Outline": ["Document Outline"], "Paragraphs": ["Paragraphs"], "Headings": ["Headings"], "Document Statistics": ["Document Statistics"], "View the autosave": ["View the autosave"], "There is an autosave of this post that is more recent than the version below.": ["There is an autosave of this post that is more recent than the version below."], "Only visible to site admins and editors.": ["Only visible to site admins and editors."], "Visible to everyone.": ["Visible to everyone."], "Use a secure password": ["Use a secure password"], "Create password": ["Create password"], "Would you like to privately publish this post now?": ["Would you like to privately publish this post now?"], "Move to trash": ["Move to bin"], "Add title": ["Add title"], "Terms": ["Terms"], "Parent Term": ["Parent Term"], "Add new term": ["Add new term"], "Add new category": ["Add new category"], "term\u0004Remove %s": ["Remove %s"], "term\u0004%s removed": ["%s removed"], "term\u0004%s added": ["%s added"], "Term": ["Term"], "Tag": ["Tag"], "Add new Term": ["Add new term"], "Switch to draft": ["Switch to draft"], "Are you sure you want to unschedule this post?": ["Are you sure you want to unschedule this post?"], "Are you sure you want to unpublish this post?": ["Are you sure you want to unpublish this post?"], "Immediately": ["Immediately"], "Saving": ["Saving"], "Autosaving": ["Autosaving"], "Publish:": ["Publish:"], "Visibility:": ["Visibility:"], "Double-check your settings before publishing.": ["Double-check your settings before publishing."], "Are you ready to publish?": ["Are you ready to publish?"], "Your work will be published at the specified date and time.": ["Your work will be published at the specified date and time."], "Are you ready to schedule?": ["Are you ready to schedule?"], "Are you ready to submit for review?": ["Are you ready to submit for review?"], "Copied!": ["Copied!"], "%s address": ["%s address"], "What’s next?": ["What’s next?"], "is now live.": ["is now live."], "is now scheduled. It will go live on": ["is now scheduled. It will go live on"], "Tags help users and search engines navigate your site and find your content. Add a few keywords to describe your post.": ["Tags help users and search engines navigate your site and find your content. Add a few keywords to describe your post."], "Add tags": ["Add tags"], "Apply the \"%1$s\" format.": ["Apply the \"%1$s\" format."], "Your theme uses post formats to highlight different kinds of content, like images or videos. Apply a post format to see this special styling.": ["Your theme uses post formats to highlight different kinds of content, like images or videos. Apply a post format to see this special styling."], "Use a post format": ["Use a post format"], "Always show pre-publish checks.": ["Always show pre-publish checks."], "Close panel": ["Close panel"], "Submit for Review": ["Submit for Review"], "Publishing…": ["Publishing…"], "imperative verb\u0004Preview": ["Preview"], "Generating preview…": ["Generating preview…"], "Enable pingbacks & trackbacks": ["Enable pingbacks and trackbacks"], "Permalink:": ["Permalink:"], "Another user is currently working on this post (<PreviewLink />), which means you cannot make changes, unless you take over.": ["Another user is currently working on this post (<PreviewLink />), which means you cannot make changes, unless you take over."], "Another user now has editing control of this post (<PreviewLink />). Don’t worry, your changes up to this moment have been saved.": ["Another user now has editing control of this post (<PreviewLink />). Don’t worry, your changes up to this moment have been saved."], "Avatar": ["Avatar"], "This post is already being edited": ["This post is already being edited"], "Someone else has taken over this post": ["Someone else has taken over this post"], "Post Format": ["Post Format"], "Chat": ["Cha<PERSON>"], "Standard": ["Standard"], "Aside": ["Aside"], "Learn more about manual excerpts": ["Learn more about manual excerpts"], "Write an excerpt (optional)": ["Write an excerpt (optional)"], "no title": ["no title"], "Order": ["Order"], "%d result found.": ["%d result found.", "%d results found."], "The editor has encountered an unexpected error.": ["The editor has encountered an unexpected error."], "(Multiple H1 headings are not recommended)": ["(Multiple H1 headings are not recommended)"], "(Your theme may already use a H1 for the post title)": ["(Your theme may already use a H1 for the post title)"], "(Incorrect heading level)": ["(Incorrect heading level)"], "(Empty heading)": ["(Empty heading)"], "Category": ["Category"], "Reset": ["Reset"], "(opens in a new tab)": ["(opens in a new tab)"], "Blocks": ["Blocks"], "Pending": ["Pending"], "Take over": ["Take over"], "Go back": ["Go back"], "Gallery": ["Gallery"], "Date": ["Date"], "Current image: %s": ["Current image: %s"], "Homepage": ["Homepage"], "(Untitled)": ["(Untitled)"], "Site Icon": ["Site Icon"], "Detach": ["<PERSON><PERSON>"], "verb\u0004Trash": ["Bin"], "Back": ["Back"], "Trash": ["Bin"], "Tools": ["Tools"], "Footer": ["Footer"], "Replace": ["Replace"], "Block": ["Block"], "Status": ["Status"], "Delete permanently": ["Delete permanently"], "Video": ["Video"], "Videos": ["Videos"], "Audio": ["Audio"], "Saved": ["Saved"], "Remove": ["Remove"], "Featured image": ["Featured image"], "Template": ["Template"], "Posts Page": ["Posts page"], "Navigation": ["Navigation"], "Link": ["Link"], "Words": ["Words"], "Learn more": ["Learn more"], "Parent Category": ["Parent Category"], "Add a featured image": ["Add a featured image"], "Image": ["Image"], "You do not have permission to create Pages.": ["You do not have permission to create pages."], "Quote": ["Quote"], "General": ["General"], "Copy": ["Copy"], "Format": ["Format"], "Suggestion:": ["Suggestion:"], "Untitled": ["Untitled"], "Blog title": ["Blog title"], "Help": ["Help"], "(no title)": ["(no title)"], "Close": ["Close"], "Images": ["Images"], "Add": ["Add"], "Add new tag": ["Add new tag"], "Schedule": ["Schedule"], "Public": ["Public"], "Private": ["Private"], "Save as pending": ["Save as pending"], "Actions": ["Actions"], "Media": ["Media"], "Redo": ["Redo"], "Password": ["Password"], "Content": ["Content"], "Cancel": ["Cancel"], "Plugins": ["Plugins"], "None": ["None"], "Description": ["Description"], "Name": ["Name"], "Save": ["Save"], "Delete": ["Delete"], "Revisions": ["Revisions"], "Excerpt": ["Excerpt"], "Publish": ["Publish"], "Comments": ["Comments"], "Undo": ["Undo"], "View": ["View"], "Restore": ["Rest<PERSON>"], "Author": ["Author"], "Draft": ["Draft"], "Pending Review": ["Pending Review"], "Published": ["Published"], "Title": ["Title"]}}, "comment": {"reference": "wp-includes/js/dist/editor.js"}}