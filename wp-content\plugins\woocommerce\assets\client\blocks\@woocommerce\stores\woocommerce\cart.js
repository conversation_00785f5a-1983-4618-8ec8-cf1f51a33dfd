import*as e from"@wordpress/interactivity";var t={7908:e=>{e.exports=import("@woocommerce/stores/store-notices")}},r={};function o(e){var s=r[e];if(void 0!==s)return s.exports;var n=r[e]={exports:{}};return t[e](n,n.exports,o),n.exports}o.d=(e,t)=>{for(var r in t)o.o(t,r)&&!o.o(e,r)&&Object.defineProperty(e,r,{enumerable:!0,get:t[r]})},o.o=(e,t)=>Object.prototype.hasOwnProperty.call(e,t);const s=(a={store:()=>e.store},i={},o.d(i,a),i),n=({preserveCartData:e=!1})=>{((e,{bubbles:t=!1,cancelable:r=!1,element:o,detail:s={}})=>{if(!CustomEvent)return;o||(o=document.body);const n=new CustomEvent(e,{bubbles:t,cancelable:r,detail:s});o.dispatchEvent(n)})("wc-blocks_added_to_cart",{bubbles:!0,cancelable:!0,detail:{preserveCartData:e}})};var a,i;function c(e,t){return!e.ok}function d(e){return Object.assign(new Error(e.message||"Unknown error."),{code:e.code||"unknown_error"})}let y=!1,l=3e3;function p({quantityChanges:e}){window.dispatchEvent(new CustomEvent("wc-blocks_store_sync_required",{detail:{type:"from_iAPI",quantityChanges:e}}))}const{state:u,actions:h}=(0,s.store)("woocommerce",{actions:{*addCartItem({id:e,quantity:t,variation:r}){let o=u.cart.items.find((({id:t})=>e===t));const s=o?"update-item":"add-item",a=JSON.stringify(u.cart),i={};o?(o.quantity=t,o.key&&(i.cartItemsPendingQuantity=[o.key])):(o={id:e,quantity:t,variation:r},u.cart.items.push(o),i.productsPendingAdd=[e]);try{const e=yield fetch(`${u.restUrl}wc/store/v1/cart/${s}`,{method:"POST",headers:{Nonce:u.nonce,"Content-Type":"application/json"},body:JSON.stringify(o)}),t=yield e.json();if(c(e))throw d(t);t.errors?.forEach((e=>{h.showNoticeError(e)})),u.cart=t,n({preserveCartData:!0}),p({quantityChanges:i})}catch(e){u.cart=JSON.parse(a),h.showNoticeError(e)}},*batchAddCartItems(e){const t=structuredClone(u.cart),r={};try{const t=e.map((e=>{const t=u.cart.items.find((({id:t})=>e.id===t));return t?(t.quantity=e.quantity,t.key&&(r.cartItemsPendingQuantity=[t.key]),{method:"POST",path:"/wc/store/v1/cart/update-item",headers:{Nonce:u.nonce,"Content-Type":"application/json"},body:t}):(e={id:e.id,quantity:e.quantity,variation:e.variation},u.cart.items.push(e),r.productsPendingAdd=r.productsPendingAdd?[...r.productsPendingAdd,e.id]:[e.id],{method:"POST",path:"/wc/store/v1/cart/add-item",headers:{Nonce:u.nonce,"Content-Type":"application/json"},body:e})})),o=yield fetch(`${u.restUrl}wc/store/v1/batch`,{method:"POST",headers:{Nonce:u.nonce,"Content-Type":"application/json"},body:JSON.stringify({requests:t})}),s=yield o.json();s.responses?.forEach((e=>{if(c(o))throw d(e)}));const a=Array.isArray(s.responses)?s.responses.filter((e=>e.status>=200&&e.status<300)):[],i=a[a.length-1]?.body;if(!i)throw new Error("No successful cart response received.");i?.errors&&Array.isArray(i.errors)&&i.errors.forEach((e=>{h.showNoticeError(e)}));const y=i;u.cart=y,n({preserveCartData:!0}),p({quantityChanges:r})}catch(e){u.cart=t,h.showNoticeError(e)}},*refreshCartItems(){if(!y){y=!0;try{const e=yield fetch(`${u.restUrl}wc/store/v1/cart`,{headers:{"Content-Type":"application/json"}}),t=yield e.json();if(c(e))throw d(t);u.cart=t,l=3e3}catch(e){setTimeout(h.refreshCartItems,l),l*=2}finally{y=!1}}},*showNoticeError(e){yield Promise.resolve().then(o.bind(o,7908));const{actions:t}=(0,s.store)("woocommerce/store-notices",{},{lock:"I acknowledge that using a private store means my plugin will inevitably break on the next store release."});t.addNotice({notice:e.message,type:"error",dismissible:!0}),console.error(e)}}},{lock:!0});window.addEventListener("wc-blocks_store_sync_required",(async e=>{"from_@wordpress/data"===e.detail.type&&h.refreshCartItems()}));