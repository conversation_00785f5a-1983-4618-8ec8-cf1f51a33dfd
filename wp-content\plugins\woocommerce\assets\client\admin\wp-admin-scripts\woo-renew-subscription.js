(()=>{"use strict";var e={n:o=>{var n=o&&o.__esModule?()=>o.default:()=>o;return e.d(n,{a:n}),n},d:(o,n)=>{for(var r in n)e.o(n,r)&&!e.o(o,r)&&Object.defineProperty(o,r,{enumerable:!0,get:n[r]})},o:(e,o)=>Object.prototype.hasOwnProperty.call(e,o)};const o=window.wp.domReady;var n=e.n(o);const r=window.wc.tracks;n()((()=>{const e=document.querySelectorAll(".woocommerce-renew-subscription");e.length>0&&((0,r.recordEvent)("woo_renew_subscription_in_plugins_shown"),e.forEach((e=>{e.addEventListener("click",(function(){(0,r.recordEvent)("woo_renew_subscription_in_plugins_clicked")}))})))})),(window.wc=window.wc||{}).wooRenewSubscription={}})();