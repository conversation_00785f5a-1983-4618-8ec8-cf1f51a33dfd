/**
 * Film Collectables Theme JavaScript
 */

jQuery(document).ready(function($) {
    
    // Countdown Timer
    function initCountdownTimers() {
        $('.countdown-timer').each(function() {
            const $timer = $(this);
            const endTime = parseInt($timer.data('end-time')) * 1000;
            
            function updateTimer() {
                const now = new Date().getTime();
                const distance = endTime - now;
                
                if (distance < 0) {
                    $timer.html('<span class="text-red-600 font-bold">ENDED</span>');
                    return;
                }
                
                const days = Math.floor(distance / (1000 * 60 * 60 * 24));
                const hours = Math.floor((distance % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
                const minutes = Math.floor((distance % (1000 * 60 * 60)) / (1000 * 60));
                const seconds = Math.floor((distance % (1000 * 60)) / 1000);
                
                let html = '<div class="grid grid-cols-4 gap-2 text-center">';
                html += `<div><span class="block text-lg font-bold text-indigo-600">${days}</span><span class="text-xs text-gray-500">Days</span></div>`;
                html += `<div><span class="block text-lg font-bold text-indigo-600">${hours}</span><span class="text-xs text-gray-500">Hours</span></div>`;
                html += `<div><span class="block text-lg font-bold text-indigo-600">${minutes}</span><span class="text-xs text-gray-500">Mins</span></div>`;
                html += `<div><span class="block text-lg font-bold text-indigo-600">${seconds}</span><span class="text-xs text-gray-500">Secs</span></div>`;
                html += '</div>';
                
                $timer.html(html);
            }
            
            updateTimer();
            setInterval(updateTimer, 1000);
        });
    }
    
    // Ticket Purchase Form
    function initTicketPurchase() {
        $('#ticket-purchase-form').on('submit', function(e) {
            e.preventDefault();
            
            const $form = $(this);
            const $button = $form.find('button[type="submit"]');
            const originalText = $button.text();
            
            // Show loading state
            $button.prop('disabled', true).html('<div class="spinner inline-block mr-2"></div>Processing...');
            
            const formData = {
                action: 'purchase_ticket',
                nonce: filmcollectables_ajax.nonce,
                competition_id: $form.find('input[name="competition_id"]').val(),
                product_id: $form.find('input[name="product_id"]').val(),
                quantity: $form.find('select[name="quantity"]').val(),
                answer: $form.find('input[name="answer"]:checked').val()
            };
            
            $.ajax({
                url: filmcollectables_ajax.ajax_url,
                type: 'POST',
                data: formData,
                success: function(response) {
                    if (response.success) {
                        // Show success message
                        showNotification('Tickets added to cart successfully!', 'success');
                        
                        // Redirect to cart or update cart count
                        if (response.data.redirect) {
                            setTimeout(() => {
                                window.location.href = response.data.redirect;
                            }, 1500);
                        }
                        
                        // Update cart count in header
                        updateCartCount();
                        
                    } else {
                        showNotification(response.data.message || 'Error adding tickets to cart', 'error');
                    }
                },
                error: function() {
                    showNotification('Network error. Please try again.', 'error');
                },
                complete: function() {
                    // Reset button
                    $button.prop('disabled', false).text(originalText);
                }
            });
        });
    }
    
    // Instant Win Check
    function initInstantWinCheck() {
        // Check for instant wins every 30 seconds
        setInterval(function() {
            const competitionId = $('input[name="competition_id"]').val();
            if (!competitionId) return;
            
            $.ajax({
                url: filmcollectables_ajax.ajax_url,
                type: 'POST',
                data: {
                    action: 'check_instant_win',
                    nonce: filmcollectables_ajax.nonce,
                    competition_id: competitionId
                },
                success: function(response) {
                    if (response.success && response.data.is_winner) {
                        showInstantWinNotification(response.data.message);
                    }
                }
            });
        }, 30000);
    }
    
    // Notification System
    function showNotification(message, type = 'info') {
        const bgColor = type === 'success' ? 'bg-green-500' : 
                       type === 'error' ? 'bg-red-500' : 'bg-blue-500';
        
        const notification = $(`
            <div class="fixed right-4 z-50 ${bgColor} text-white px-6 py-3 rounded-lg shadow-lg transform translate-x-full transition-transform duration-300" style="top: 100px; max-width: 300px;">
                <div class="flex items-center">
                    <span>${message}</span>
                    <button class="ml-4 text-white hover:text-gray-200" onclick="$(this).parent().parent().remove()">
                        <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
                        </svg>
                    </button>
                </div>
            </div>
        `);
        
        $('body').append(notification);
        
        // Animate in
        setTimeout(() => {
            notification.removeClass('translate-x-full');
        }, 100);
        
        // Auto remove after 5 seconds
        setTimeout(() => {
            notification.addClass('translate-x-full');
            setTimeout(() => notification.remove(), 300);
        }, 5000);
    }
    
    // Instant Win Notification
    function showInstantWinNotification(message) {
        const notification = $(`
            <div class="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50">
                <div class="bg-white rounded-lg p-8 max-w-md mx-4 text-center instant-win-notification">
                    <div class="text-6xl mb-4">🎉</div>
                    <h3 class="text-2xl font-bold text-gray-900 mb-4">Instant Win!</h3>
                    <p class="text-gray-600 mb-6">${message}</p>
                    <button class="bg-indigo-600 text-white px-6 py-3 rounded-lg hover:bg-indigo-700" onclick="$(this).closest('.fixed').remove()">
                        Awesome!
                    </button>
                </div>
            </div>
        `);
        
        $('body').append(notification);
        
        // Auto remove after 10 seconds
        setTimeout(() => {
            notification.fadeOut(() => notification.remove());
        }, 10000);
    }
    
    // Update Cart Count
    function updateCartCount() {
        $.ajax({
            url: filmcollectables_ajax.ajax_url,
            type: 'POST',
            data: {
                action: 'get_cart_count',
                nonce: filmcollectables_ajax.nonce
            },
            success: function(response) {
                if (response.success) {
                    const count = response.data.count;
                    const $cartCount = $('.cart-count');
                    
                    if (count > 0) {
                        if ($cartCount.length) {
                            $cartCount.text(count);
                        } else {
                            // Add cart count badge
                            $('a[href*="cart"]').append(`
                                <span class="cart-count absolute -top-1 -right-1 bg-indigo-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                                    ${count}
                                </span>
                            `);
                        }
                    } else {
                        $cartCount.remove();
                    }
                }
            }
        });
    }
    
    // Smooth Scrolling for Anchor Links
    function initSmoothScrolling() {
        $('a[href^="#"]').on('click', function(e) {
            const target = $(this.getAttribute('href'));
            if (target.length) {
                e.preventDefault();
                $('html, body').animate({
                    scrollTop: target.offset().top - 100
                }, 500);
            }
        });
    }
    
    // Image Lazy Loading
    function initLazyLoading() {
        if ('IntersectionObserver' in window) {
            const imageObserver = new IntersectionObserver((entries, observer) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        const img = entry.target;
                        img.src = img.dataset.src;
                        img.classList.remove('lazy');
                        imageObserver.unobserve(img);
                    }
                });
            });
            
            document.querySelectorAll('img[data-src]').forEach(img => {
                imageObserver.observe(img);
            });
        }
    }
    
    // Progress Bar Animation (disabled to prevent animation conflicts)
    function animateProgressBars() {
        // Animation disabled - progress bars now show static values
        // $('.progress-bar').each(function() {
        //     const $bar = $(this);
        //     const width = $bar.css('width');
        //     $bar.css('width', '0').animate({ width: width }, 1000);
        // });
    }
    
    // Mobile Navigation
    function initMobileNavigation() {
        const mobileMenuToggle = $('#mobile-menu-toggle');
        const mobileNav = $('#mobile-nav');
        const mobileNavOverlay = $('#mobile-nav-overlay');
        const mobileNavClose = $('#mobile-nav-close');

        // Open mobile menu
        mobileMenuToggle.on('click', function(e) {
            e.preventDefault();
            $(this).addClass('active');
            mobileNav.addClass('active');
            mobileNavOverlay.addClass('active');
            $('body').addClass('mobile-nav-open');

            // Reset menu item animations
            $('.mobile-nav-menu a, .mobile-nav-link').css({
                'transform': 'translateX(20px)',
                'opacity': '0'
            });

            // Trigger animations after a short delay
            setTimeout(() => {
                $('.mobile-nav-menu a, .mobile-nav-link').each(function(index) {
                    const $item = $(this);
                    setTimeout(() => {
                        $item.css({
                            'transform': 'translateX(0)',
                            'opacity': '1',
                            'transition': 'all 0.3s ease'
                        });
                    }, index * 50);
                });
            }, 100);
        });

        // Close mobile menu
        function closeMobileMenu() {
            mobileMenuToggle.removeClass('active');
            mobileNav.removeClass('active');
            mobileNavOverlay.removeClass('active');
            $('body').removeClass('mobile-nav-open');
        }

        // Close menu when close button is clicked
        mobileNavClose.on('click', function(e) {
            e.preventDefault();
            closeMobileMenu();
        });

        // Close menu when overlay is clicked
        mobileNavOverlay.on('click', closeMobileMenu);

        // Close menu when escape key is pressed
        $(document).on('keydown', function(e) {
            if (e.key === 'Escape' && mobileNav.hasClass('active')) {
                closeMobileMenu();
            }
        });

        // Close menu when a menu item is clicked
        $('.mobile-nav-menu a, .mobile-nav-link').on('click', function(e) {
            // Don't close immediately for external links or if it's a parent menu item
            const href = $(this).attr('href');
            if (href && href !== '#' && !href.startsWith('javascript:')) {
                // Add a small delay to allow the link to be processed
                setTimeout(closeMobileMenu, 150);
            }
        });

        // Handle window resize - close menu if window becomes larger
        $(window).on('resize', function() {
            if ($(window).width() > 768 && mobileNav.hasClass('active')) {
                closeMobileMenu();
            }
        });

        // Improve touch handling for mobile devices
        let touchStartY = 0;
        mobileNav.on('touchstart', function(e) {
            touchStartY = e.originalEvent.touches[0].clientY;
        });

        mobileNav.on('touchmove', function(e) {
            const touchY = e.originalEvent.touches[0].clientY;
            const touchDiff = touchStartY - touchY;

            // Prevent overscroll
            if (this.scrollTop === 0 && touchDiff < 0) {
                e.preventDefault();
            }
            if (this.scrollHeight - this.scrollTop === this.clientHeight && touchDiff > 0) {
                e.preventDefault();
            }
        });
    }

    // Initialize all functions
    initCountdownTimers();
    initMobileNavigation();
    // initTicketPurchase(); // Disabled - using inline forms instead
    // initInstantWinCheck(); // Disabled for now
    initSmoothScrolling();
    initLazyLoading();
    // animateProgressBars(); // Disabled to prevent animation conflicts
    
    // Animate progress bars when they come into view (disabled to prevent conflicts)
    // $(window).on('scroll', function() {
    //     $('.progress-bar').each(function() {
    //         const $bar = $(this);
    //         if (!$bar.hasClass('animated')) {
    //             const elementTop = $bar.offset().top;
    //             const elementBottom = elementTop + $bar.outerHeight();
    //             const viewportTop = $(window).scrollTop();
    //             const viewportBottom = viewportTop + $(window).height();
    //
    //             if (elementBottom > viewportTop && elementTop < viewportBottom) {
    //                 $bar.addClass('animated');
    //                 animateProgressBars();
    //             }
    //         }
    //     });
    // });
    
    // Update cart count on page load (disabled for now)
    // updateCartCount();

    // Test AJAX functionality (disabled for now)
    // testAjaxConnection();

    // Initialize competition data refresh
    initCompetitionDataRefresh();

    // Refresh competition data on page load
    if ($('.competition-card, .competition-item').length > 0) {
        setTimeout(refreshCompetitionData, 1000); // Delay to ensure page is fully loaded
    }

    // Periodic refresh every 30 seconds for competition pages
    if ($('.competition-card, .competition-item').length > 0) {
        setInterval(refreshCompetitionData, 30000);
    }
});

// Test AJAX connection
function testAjaxConnection() {
    if (typeof filmcollectables_ajax === 'undefined') {
        console.log('filmcollectables_ajax object not found - AJAX will not work');
        return;
    }

    console.log('Testing AJAX connection...');
    jQuery.ajax({
        url: filmcollectables_ajax.ajax_url,
        type: 'POST',
        data: {
            action: 'test_ajax'
        },
        success: function(response) {
            console.log('AJAX test successful:', response);
        },
        error: function(xhr, status, error) {
            console.log('AJAX test failed:');
            console.log('Status:', status);
            console.log('Error:', error);
            console.log('Response:', xhr.responseText);
        }
    });
}

// Competition data refresh functionality
function initCompetitionDataRefresh() {
    // Listen for WooCommerce cart fragment refresh events
    jQuery(document.body).on('wc_fragments_refreshed added_to_cart', function() {
        refreshCompetitionData();
    });

    // Also refresh when page becomes visible (user returns from checkout)
    jQuery(document).on('visibilitychange', function() {
        if (!document.hidden) {
            setTimeout(refreshCompetitionData, 1000);
        }
    });

    // Refresh when user navigates back/forward in browser
    jQuery(window).on('pageshow', function(event) {
        // This fires when page is loaded from cache (back/forward navigation)
        if (event.originalEvent.persisted) {
            setTimeout(refreshCompetitionData, 500);
        }
    });

    // Refresh when page gains focus (user switches back to tab)
    jQuery(window).on('focus', function() {
        setTimeout(refreshCompetitionData, 500);
    });
}

function refreshCompetitionData() {
    // Only refresh on pages that show competition cards
    if (!jQuery('.competition-card, .competition-item').length) {
        console.log('No competition cards found on page');
        return;
    }

    // Get all competition IDs on the page
    const competitionIds = [];
    jQuery('.competition-card, .competition-item').each(function() {
        const competitionId = jQuery(this).data('competition-id');
        if (competitionId) {
            competitionIds.push(competitionId);
        }
    });

    console.log('Refreshing competition data for IDs:', competitionIds);

    // Refresh data for each competition
    competitionIds.forEach(function(competitionId) {
        refreshSingleCompetitionData(competitionId);
    });
}

function refreshSingleCompetitionData(competitionId) {
    console.log('Refreshing data for competition ID:', competitionId);

    // Check if AJAX object is available
    if (typeof filmcollectables_ajax === 'undefined') {
        console.log('filmcollectables_ajax object not found');
        return;
    }

    console.log('AJAX URL:', filmcollectables_ajax.ajax_url);
    console.log('Competition ID:', competitionId);

    jQuery.ajax({
        url: filmcollectables_ajax.ajax_url,
        type: 'POST',
        cache: false, // Disable jQuery caching
        data: {
            action: 'simple_get_competition_data',
            competition_id: competitionId,
            _cache_bust: Date.now() // Add timestamp to prevent caching
        },
        success: function(response) {
            console.log('AJAX response for competition ' + competitionId + ':', response);
            if (response.success && response.data) {
                updateCompetitionCard(competitionId, response.data);
            } else {
                console.log('Invalid response data for competition ' + competitionId, response);
            }
        },
        error: function(xhr, status, error) {
            console.log('Failed to refresh competition data for ID: ' + competitionId);
            console.log('Status:', status);
            console.log('Error:', error);
            console.log('Response:', xhr.responseText);
        }
    });
}

function updateCompetitionCard(competitionId, data) {
    console.log('Updating competition card for ID:', competitionId, 'with data:', data);

    const $card = jQuery('.competition-card[data-competition-id="' + competitionId + '"], .competition-item[data-competition-id="' + competitionId + '"]');

    if (!$card.length) {
        console.log('No card found for competition ID:', competitionId);
        return;
    }

    // Update sold tickets count
    $card.find('.sold-tickets').text(data.sold_tickets + ' sold');

    // Update remaining tickets count
    $card.find('.remaining-tickets').text(data.remaining_tickets + ' remaining');

    // Update progress bar (handle both single-product and competitions page structures)
    $card.find('.progress-bar').css('width', data.progress_percentage + '%');
    $card.find('.progress-fill').css('width', data.progress_percentage + '%');

    // Update time left
    if (data.time_left) {
        $card.find('.time-text').text(data.time_left);
    }

    // Update price display
    const priceText = (data.price == 0 || data.price == '0.00') ? 'FREE' : '£' + data.price;
    $card.find('.competition-price').text(priceText);

    // Update status indicators
    if (data.is_sold_out || data.status === 'awaiting_draw') {
        // Add sold out indicator if not already present
        if (!$card.find('.sold-out-indicator').length) {
            $card.find('.time-left').parent().prepend(
                '<div class="sold-out-indicator" style="position: absolute; top: 1rem; left: 1rem;">' +
                '<div style="background: #ef4444; color: white; padding: 0.25rem 0.75rem; border-radius: 0.25rem; font-size: 0.875rem; font-weight: bold;">' +
                'SOLD OUT' +
                '</div>' +
                '</div>'
            );
        }
        // Update button text
        $card.find('a[href*="' + competitionId + '"]').text('VIEW RESULTS');
    } else {
        // Remove sold out indicator if present
        $card.find('.sold-out-indicator').remove();
        // Update button text
        $card.find('a[href*="' + competitionId + '"]').text('ENTER NOW');
    }

    console.log('Competition card updated successfully for ID:', competitionId);

    console.log('Found card element:', $card);

    // Update sold tickets count
    const $soldSpan = $card.find('.progress-info span:first-child');
    $soldSpan.text(data.sold_tickets + ' sold');
    console.log('Updated sold tickets to:', data.sold_tickets + ' sold');

    // Update remaining tickets count
    const $remainingSpan = $card.find('.progress-info span:last-child');
    $remainingSpan.text(data.remaining_tickets + ' remaining');
    console.log('Updated remaining tickets to:', data.remaining_tickets + ' remaining');

    // Update progress bar
    const $progressFill = $card.find('.progress-fill');
    const progressWidth = data.progress_percentage + '%';
    const minWidth = data.sold_tickets > 0 ? '2px' : '0';
    $progressFill.css({
        'width': progressWidth,
        'min-width': minWidth
    });
    console.log('Updated progress bar to:', progressWidth, 'min-width:', minWidth);

    // Animation class removed to prevent conflicts

    // Update instant win badge
    const $existingBadge = $card.find('.instant-win-badge');
    if (data.instant_wins_enabled && data.instant_win_prizes && data.instant_win_prizes.length > 0) {
        if (!$existingBadge.length) {
            // Add instant win badge if it doesn't exist
            $card.prepend('<div class="badge instant-win-badge">🎁 INSTANT WINS</div>');
            console.log('Added instant win badge');
        }
    } else {
        // Remove instant win badge if instant wins are disabled
        if ($existingBadge.length) {
            $existingBadge.remove();
            console.log('Removed instant win badge');
        }
    }
}
