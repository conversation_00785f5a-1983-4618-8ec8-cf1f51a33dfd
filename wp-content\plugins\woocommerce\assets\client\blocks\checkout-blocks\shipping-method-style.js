"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[9319],{25764:(e,i,o)=>{o.r(i),o.d(i,{default:()=>N});var s=o(4921),c=o(41616),t=o(14656),n=o(47143),p=o(47594),r=o(76473),l=o(78331),h=o(94199),a=o(27723),d=o(94530),g=o(38415),u=o(34970),k=o(86087),m=o(13932),_=o(15703),w=o(68471),x=o(75324),b=o(54137),C=o(39295);o(93832),(e=>{let i,o=null;const s=(...s)=>{o=s,i&&clearTimeout(i),i=setTimeout((()=>{i=null,o&&e(...o)}),300)};s.flush=()=>{i&&o&&(e(...o),clearTimeout(i),i=null)},s.clear=()=>{i&&clearTimeout(i),i=null}})((e=>{window.localStorage.setItem("WOOCOMMERCE_CHECKOUT_IS_CUSTOMER_DATA_DIRTY",e?"true":"false")}));var T=o(10790);const S={hidden:!0,message:(0,a.__)("Shipping options are not available","woocommerce")},j=({checked:e,rate:i,showPrice:o,showIcon:c,toggleText:t,multiple:n,onClick:p})=>(0,T.jsxs)(w.$,{render:(0,T.jsx)("div",{}),role:"radio",onClick:p,"aria-checked":"pickup"===e,className:(0,s.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"pickup"===e}),children:[(0,T.jsxs)("span",{className:"wc-block-checkout__shipping-method-option-title-wrapper",children:[!0===c&&(0,T.jsx)(d.A,{icon:g.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,T.jsx)("span",{className:"wc-block-checkout__shipping-method-option-title",children:t})]}),!0===o&&(0,T.jsx)(x.e,{multiple:n,minRate:i.min,maxRate:i.max})]}),A=({checked:e,rate:i,showPrice:o,showIcon:c,toggleText:t,onClick:r,shippingCostRequiresAddress:l=!1})=>{const h=(0,n.useSelect)((e=>e(p.cartStore).getShippingRates().some((({shipping_rates:e})=>!e.every(m.J_))))),g=l&&(()=>{const e=(0,n.select)("wc/store/validation"),i=e.getValidationError("shipping_state"),o=e.getValidationError("shipping_address_1"),s=e.getValidationError("shipping_country"),c=e.getValidationError("shipping_postcode");return[e.getValidationError("shipping_city"),i,o,s,c].some((e=>void 0!==e))})()&&!h,_=void 0!==i.min&&void 0!==i.max,{setValidationErrors:b,clearValidationError:C}=(0,n.useDispatch)(p.validationStore);(0,k.useEffect)((()=>("shipping"!==e||_?C("shipping-rates-error"):b({"shipping-rates-error":S}),()=>C("shipping-rates-error"))),[e,C,_,b]);const j=void 0===i.min||g?(0,T.jsx)("span",{className:"wc-block-checkout__shipping-method-option-price",children:(0,a.__)("calculated with an address","woocommerce")}):(0,T.jsx)(x.e,{minRate:i.min,maxRate:i.max});return(0,T.jsxs)(w.$,{render:(0,T.jsx)("div",{}),role:"radio",onClick:r,"aria-checked":"shipping"===e,className:(0,s.A)("wc-block-checkout__shipping-method-option",{"wc-block-checkout__shipping-method-option--selected":"shipping"===e}),children:[(0,T.jsxs)("span",{className:"wc-block-checkout__shipping-method-option-title-wrapper",children:[!0===c&&(0,T.jsx)(d.A,{icon:u.A,size:28,className:"wc-block-checkout__shipping-method-option-icon"}),(0,T.jsx)("span",{className:"wc-block-checkout__shipping-method-option-title",children:t})]}),!0===o&&j]})},P=({checked:e,onChange:i,showPrice:o,showIcon:s,localPickupText:c,shippingText:t})=>{const{shippingRates:n}=(0,r.m)(),p=(0,_.getSetting)("shippingCostRequiresAddress",!1),l=(0,_.getSetting)("localPickupText",c||C.A);return(0,T.jsxs)("div",{id:"shipping-method",className:"components-button-group wc-block-checkout__shipping-method-container",role:"radiogroup",children:[(0,T.jsx)(A,{checked:e,onClick:()=>{i("shipping")},rate:(0,b.L)(n[0]?.shipping_rates),showPrice:o,showIcon:s,shippingCostRequiresAddress:p,toggleText:t||C.x}),(0,T.jsx)(j,{checked:e,onClick:()=>{i("pickup")},rate:(0,b._)(n[0]?.shipping_rates),multiple:n.length>1,showPrice:o,showIcon:s,toggleText:l})]})};var v=o(72957);const N=(0,c.withFilteredAttributes)(v.A)((({title:e,description:i,children:o,className:c,showPrice:a,showIcon:d,shippingText:g,localPickupText:u})=>{const{showFormStepNumbers:k}=(0,h.O)(),{checkoutIsProcessing:m,prefersCollection:_}=(0,n.useSelect)((e=>{const i=e(p.checkoutStore);return{checkoutIsProcessing:i.isProcessing(),prefersCollection:i.prefersCollection()}})),{setPrefersCollection:w}=(0,n.useDispatch)(p.checkoutStore),{needsShipping:x,isCollectable:b}=(0,r.m)();return l.h0&&x&&b&&l.F7&&l.mH?(0,T.jsxs)(t.FormStep,{id:"shipping-method",disabled:m,className:(0,s.A)("wc-block-checkout__shipping-method",c),title:e,description:i,showStepNumber:k,children:[(0,T.jsx)(P,{checked:_?"pickup":"shipping",onChange:e=>{w("pickup"===e)},showPrice:a,showIcon:d,localPickupText:u,shippingText:g}),o]}):null}))}}]);