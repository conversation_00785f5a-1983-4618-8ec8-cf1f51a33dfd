"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[504],{67698:(e,t,o)=>{o.r(t),o.d(t,{default:()=>f});var r=o(47778),d=o(98846),c=o(83306),n=o(51609),i=o(92279),s=o(33068),u=o(45155),a=o(27723),l=o(3582),p=o(47143),_=o(86087),m=o(73970),x=o(39793);(0,r.productEditorHeaderApiFetchMiddleware)(),(0,r.productApiFetchMiddleware)();const h=(0,n.lazy)((()=>o.e(4925).then(o.bind(o,94925)))),b=(0,n.lazy)((()=>Promise.resolve().then(o.t.bind(o,47778,23)).then((e=>({default:e.__experimentalProductMVPFeedbackModalContainer})))));function f(){const{productId:e}=(0,s.g)();(0,n.useEffect)((()=>{document.body.classList.add("is-product-editor");const e=(0,r.__experimentalInitBlocks)();return()=>{document.body.classList.remove("is-product-editor"),e()}}),[]),(0,n.useEffect)((()=>((0,i.registerPlugin)("wc-admin-product-editor",{scope:"woocommerce-product-block-editor",render:()=>(0,n.useContext)(r.__experimentalEditorLoadingContext)?null:(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(r.__experimentalWooProductMoreMenuItem,{children:({onClose:e})=>(0,x.jsx)(m.y,{onClose:e})}),(0,x.jsx)(u.WooFooterItem,{children:(0,x.jsxs)(x.Fragment,{children:[(0,x.jsx)(r.__experimentalProductMVPCESFooter,{productType:"product"}),(0,x.jsx)(n.Suspense,{fallback:(0,x.jsx)(d.Spinner,{}),children:(0,x.jsx)(b,{productId:e?Number.parseInt(e,10):void 0})})]})}),(0,x.jsx)(n.Suspense,{fallback:(0,x.jsx)(d.Spinner,{}),children:(0,x.jsx)(h,{})})]})}),()=>{(0,i.unregisterPlugin)("wc-admin-product-editor")})),[e]),(0,n.useEffect)((function(){e?(0,c.recordEvent)("product_edit_view",{source:r.TRACKS_SOURCE,product_id:e}):(0,c.recordEvent)("product_add_view",{source:r.TRACKS_SOURCE})}),[e]);const t=function(e){const[t,o]=(0,_.useState)(void 0);return(0,_.useEffect)((()=>{e?o(Number.parseInt(e,10)):(0,p.dispatch)(l.store).saveEntityRecord("postType","product",{title:r.AUTO_DRAFT_NAME,status:"auto-draft"}).then((e=>o(e.id))).catch((e=>{throw o(void 0),e}))}),[e]),t}(e);return t?(0,x.jsx)(r.__experimentalEditor,{productId:t}):(0,x.jsx)("div",{className:"woocommerce-layout__loading",children:(0,x.jsx)(d.Spinner,{"aria-label":(0,a.__)("Creating the product","woocommerce")})})}}}]);