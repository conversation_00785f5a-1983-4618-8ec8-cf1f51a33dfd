"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[9719],{45260:(n,t,r)=>{r.d(t,{A:()=>i});var o=r(5573),e=r(39793);const i=(0,e.jsx)(o.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,e.jsx)(o.<PERSON>,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},86958:(n,t,r)=>{r.d(t,{Zz:()=>e}),"function"==typeof Symbol&&Symbol.observable;var o=function(){return Math.random().toString(36).substring(7).split("").join(".")};function e(){for(var n=arguments.length,t=new Array(n),r=0;r<n;r++)t[r]=arguments[r];return 0===t.length?function(n){return n}:1===t.length?t[0]:t.reduce((function(n,t){return function(){return n(t.apply(void 0,arguments))}}))}o(),o()}}]);