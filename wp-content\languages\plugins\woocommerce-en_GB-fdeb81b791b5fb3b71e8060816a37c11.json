{"translation-revision-date": "2024-10-02 16:14:23+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "TikTok for WooCommerce": ["TikTok for WooCommerce"], "Pinterest for WooCommerce": ["Pinterest for WooCommerce"], "Mercado Pago payments for WooCommerce": ["Mercado Pago payments for WooCommerce"], "MailPoet": ["MailPoet"], "Could not %(actionType)s %(pluginName)s plugin, %(error)s": ["Could not %(actionType)s %(pluginName)s plugin, %(error)s", "Could not %(actionType)s the following plugins: %(pluginName)s with these Errors: %(error)s"], "Razorpay": ["Razorpay"], "Creative Mail for WooCommerce": ["Creative Mail for WooCommerce"], "WooCommerce Shipping & Tax": ["WooCommerce Shipping and Tax"], "WooCommerce Stripe": ["WooCommerce Stripe"], "WooCommerce PayPal": ["WooCommerce PayPal"], "WooCommerce ShipStation Gateway": ["WooCommerce ShipStation Gateway"], "There was a problem updating your settings.": ["There was a problem updating your settings."], "Plugins were successfully installed and activated.": ["Plugins were successfully installed and activated."], "MM/DD/YYYY": ["MM/DD/YYYY"], "Facebook for WooCommerce": ["Facebook for WooCommerce"], "Mailchimp for WooCommerce": ["MailChimp for WooCommerce"], "Klarna Payments for WooCommerce": ["Klarna Payments for WooCommerce"], "Klarna Checkout for WooCommerce": ["<PERSON><PERSON><PERSON> Checkout for WooCommerce"], "Jetpack": ["Jetpack"]}}, "comment": {"reference": "assets/client/admin/data/index.js"}}