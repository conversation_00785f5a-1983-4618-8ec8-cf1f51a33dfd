"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[3149],{12974:(e,t,s)=>{s.d(t,{Ay:()=>n});var i=s(13240);const o=["a","b","em","i","strong","p","br"],c=["target","href","rel","name","download"],n=e=>({__html:(0,i.sanitize)(e,{ALLOWED_TAGS:o,ALLOWED_ATTR:c})})},1275:(e,t,s)=>{s.d(t,{v:()=>m});var i=s(18537),o=s(56427),c=s(86087),n=s(12974),l=s(1069),a=s(39793);const m=({method:e,paymentMethodsState:t,setPaymentMethodsState:s,isExpanded:m,initialVisibilityStatus:r,...d})=>{var _,h,x,p;const u=(0,c.useRef)(null);void 0===r&&null===u.current&&void 0!==t[e.id]&&(u.current=(0,l.TO)(e,t[e.id]));const w=void 0!==r?null!=r&&r:null!==(_=u.current)&&void 0!==_&&_;return m||w?(0,a.jsx)("div",{id:e.id,className:"woocommerce-list__item woocommerce-list__item-enter-done",...d,children:(0,a.jsxs)("div",{className:"woocommerce-list__item-inner",children:["apple_google"!==e.id&&(0,a.jsxs)(a.Fragment,{children:[(0,a.jsx)("div",{className:"woocommerce-list__item-before",children:(0,a.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,a.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,a.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,a.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,n.Ay)((0,i.decodeEntities)(e.description))})]})]}),"apple_google"===e.id&&(0,a.jsxs)("div",{className:"woocommerce-list__item-multi",children:[(0,a.jsxs)("div",{className:"woocommerce-list__item-multi-row multi-row-space",children:[(0,a.jsx)("div",{className:"woocommerce-list__item-before",children:(0,a.jsx)("img",{src:e.icon,alt:e.title+" logo"})}),(0,a.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,a.jsx)("span",{className:"woocommerce-list__item-title",children:e.title}),(0,a.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,n.Ay)((0,i.decodeEntities)(e.description))})]})]}),(0,a.jsxs)("div",{className:"woocommerce-list__item-multi-row",children:[(0,a.jsx)("div",{className:"woocommerce-list__item-before",children:(0,a.jsx)("img",{src:e.extraIcon,alt:e.extraTitle+" logo"})}),(0,a.jsxs)("div",{className:"woocommerce-list__item-text",children:[(0,a.jsx)("span",{className:"woocommerce-list__item-title",children:e.extraTitle}),(0,a.jsx)("span",{className:"woocommerce-list__item-content",dangerouslySetInnerHTML:(0,n.Ay)((0,i.decodeEntities)(null!==(h=e.extraDescription)&&void 0!==h?h:""))})]})]})]}),(0,a.jsx)("div",{className:"woocommerce-list__item-after",children:(0,a.jsx)("div",{className:"woocommerce-list__item-after__actions wc-settings-prevent-change-event",children:(0,a.jsx)(o.ToggleControl,{checked:null!==(x=t[e.id])&&void 0!==x&&x,onChange:i=>{s({...t,[e.id]:i})},disabled:null!==(p=e.required)&&void 0!==p&&p,label:""})})})]})}):null}},95512:(e,t,s)=>{s.r(t),s.d(t,{SettingsPaymentsMethods:()=>_,default:()=>h});var i=s(40314),o=s(86087),c=s(47143),n=s(27723),l=s(56427),a=s(1069),m=s(51881),r=s(1275),d=s(39793);const _=({paymentMethodsState:e,setPaymentMethodsState:t})=>{var s;const[_,h]=(0,o.useState)(!1),{paymentMethods:x,isFetching:p}=(0,c.useSelect)((e=>{const t=e(i.paymentSettingsStore),s=t.getPaymentProviders()||[],o=(0,a.Gh)(s);return{isFetching:t.isFetching(),paymentMethods:(0,a.js)(o)}}),[]),u=x.reduce(((e,{id:t,enabled:s})=>(e[t]=s,e)),{});return(0,o.useEffect)((()=>{null===u||p||t(u)}),[p]),(0,d.jsx)("div",{className:"settings-payments-methods__container",children:p?(0,d.jsx)(m.i,{rows:3,hasDragIcon:!1}):(0,d.jsxs)(d.Fragment,{children:[(0,d.jsx)("div",{className:"woocommerce-list",children:x.map((s=>(0,d.jsx)(r.v,{method:s,paymentMethodsState:e,setPaymentMethodsState:t,isExpanded:_},s.id)))}),!_&&(0,d.jsx)(l.Button,{className:"settings-payments-methods__show-more",onClick:()=>{h(!_)},tabIndex:0,"aria-expanded":_,children:(0,n.sprintf)((0,n.__)("Show more (%s)","woocommerce"),null!==(s=x.filter((e=>!1===e.enabled)).length)&&void 0!==s?s:0)})]})})},h=_}}]);