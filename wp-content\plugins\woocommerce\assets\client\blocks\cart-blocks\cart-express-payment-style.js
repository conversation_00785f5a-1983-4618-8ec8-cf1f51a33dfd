"use strict";(globalThis.webpackChunkwebpackWcBlocksStylingJsonp=globalThis.webpackChunkwebpackWcBlocksStylingJsonp||[]).push([[1451],{18825:(t,s,e)=>{e.r(s),e.d(s,{default:()=>c});var o=e(89525),a=e(25236),n=e(59030),u=e(690),l=e(10790);const c=t=>{const s=(0,o.N)(u.attributes,t),{showButtonStyles:e,buttonHeight:c,buttonBorderRadius:r,className:i}=s;return(0,l.jsx)(n.W.Provider,{value:{showButtonStyles:e,buttonHeight:c,buttonBorderRadius:r},children:(0,l.jsx)(a.A,{className:i})})}}}]);