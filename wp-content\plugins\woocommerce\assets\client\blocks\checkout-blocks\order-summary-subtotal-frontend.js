"use strict";(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[133],{4324:(e,s,c)=>{c.r(s),c.d(s,{default:()=>n});var a=c(4656),r=c(910),t=c(5460),o=c(790);const n=({className:e=""})=>{const{cartTotals:s}=(0,t.V)(),c=(0,r.getCurrencyFromPriceResponse)(s);return(0,o.jsx)(a.<PERSON>s<PERSON>rap<PERSON>,{className:e,children:(0,o.jsx)(a.Subtotal,{currency:c,values:s})})}}}]);