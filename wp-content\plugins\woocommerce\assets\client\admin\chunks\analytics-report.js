"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[8],{26016:(r,e,t)=>{t.r(e),t.d(e,{default:()=>y});var s=t(86087),n=t(29491),o=t(47143),i=t(66087),c=t(96476),a=t(40314),p=t(98846),u=t(94111),h=t(11357),l=t(54574),g=t(39793);const d=({params:r,path:e})=>r.report||e.replace(/^\/+/,"");class m extends s.Component{constructor(){super(...arguments),this.state={hasError:!1}}componentDidCatch(r){this.setState({hasError:!0}),console.warn(r)}render(){if(this.state.hasError)return null;const{isError:r}=this.props;if(r)return(0,g.jsx)(p.AnalyticsError,{});const e=d(this.props),t=(0,i.find)((0,l.A)(),{report:e});if(!t)return(0,g.jsx)(h.N,{});const s=t.component;return(0,g.jsx)(u.CurrencyContext.Provider,{value:(0,u.getFilteredCurrencyInstance)((0,c.getQuery)()),children:(0,g.jsx)(s,{...this.props})})}}const y=(0,n.compose)((0,o.withSelect)(((r,e)=>{const t=(0,c.getQuery)(),{search:s}=t;if(!s)return{};const n=d(e),o=(0,c.getSearchWords)(t),i="categories"===n&&"single_category"===t.filter?"products":n,p=r(a.itemsStore),u=(0,a.searchItemsByString)(p,i,o,{per_page:100}),{isError:h,isRequesting:l,items:g}=u,m=Object.keys(g);return m.length?{isError:h,isRequesting:l,query:{...e.query,[i]:m.join(",")}}:{isError:h,isRequesting:l}})))(m)}}]);