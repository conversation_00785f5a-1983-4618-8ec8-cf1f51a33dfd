/*! For license information please see 1087.js.LICENSE.txt */
(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[1087],{92428:(e,t,n)=>{"use strict";n.d(t,{A:()=>f});var i=n(56427),r=n(86087),s=n(34183),l=n(39793),o=n(27723),a=n(73463);function c({title:e,onClose:t}){return(0,l.jsx)(i.__experimentalVStack,{className:"dataforms-layouts-panel__dropdown-header",spacing:4,children:(0,l.jsxs)(i.__experimentalHStack,{alignment:"center",children:[(0,l.jsx)(i.__experimentalHeading,{level:2,size:13,children:e}),(0,l.jsx)(i.__experimentalSpacer,{}),t&&(0,l.jsx)(i.<PERSON><PERSON>,{__next40pxDefaultSize:!1,className:"dataforms-layouts-panel__dropdown-header-action",label:(0,o.__)("Close"),icon:a.A,onClick:t})]})})}function u({data:e,field:t,onChange:n}){const[s,a]=(0,r.useState)(null),u=(0,r.useMemo)((()=>({anchor:s,placement:"left-start",offset:36,shift:!0})),[s]);return(0,l.jsxs)(i.__experimentalHStack,{ref:a,className:"dataforms-layouts-panel__field",children:[(0,l.jsx)("div",{className:"dataforms-layouts-panel__field-label",children:t.label}),(0,l.jsx)("div",{children:(0,l.jsx)(i.Dropdown,{contentClassName:"dataforms-layouts-panel__field-dropdown",popoverProps:u,focusOnMount:!0,toggleProps:{size:"compact",variant:"tertiary",tooltipPosition:"middle left"},renderToggle:({isOpen:n,onToggle:r})=>(0,l.jsx)(i.Button,{className:"dataforms-layouts-panel__field-control",size:"compact",variant:"tertiary","aria-expanded":n,"aria-label":(0,o.sprintf)((0,o.__)("Edit %s"),t.label),onClick:r,children:(0,l.jsx)(t.render,{item:e})}),renderContent:({onClose:i})=>(0,l.jsxs)(l.Fragment,{children:[(0,l.jsx)(c,{title:t.label,onClose:i}),(0,l.jsx)(t.Edit,{data:e,field:t,onChange:n,hideLabelFromVision:!0},t.id)]})})})]})}const d=[{type:"regular",component:function({data:e,fields:t,form:n,onChange:o}){const a=(0,r.useMemo)((()=>{var e;return(0,s.t)((null!==(e=n.fields)&&void 0!==e?e:[]).map((e=>t.find((({id:t})=>t===e)))).filter((e=>!!e)))}),[t,n.fields]);return(0,l.jsx)(i.__experimentalVStack,{spacing:4,children:a.map((t=>(0,l.jsx)(t.Edit,{data:e,field:t,onChange:o},t.id)))})}},{type:"panel",component:function({data:e,fields:t,form:n,onChange:o}){const a=(0,r.useMemo)((()=>{var e;return(0,s.t)((null!==(e=n.fields)&&void 0!==e?e:[]).map((e=>t.find((({id:t})=>t===e)))).filter((e=>!!e)))}),[t,n.fields]);return(0,l.jsx)(i.__experimentalVStack,{spacing:2,children:a.map((t=>(0,l.jsx)(u,{data:e,field:t,onChange:o},t.id)))})}}];function f({form:e,...t}){var n;const i=(r=null!==(n=e.type)&&void 0!==n?n:"regular",d.find((e=>e.type===r)));var r;return i?(0,l.jsx)(i.component,{form:e,...t}):null}},26882:(e,t,n)=>{"use strict";n.d(t,{a4:()=>m,fx:()=>f,lF:()=>w,up:()=>d});var i=n(56427),r=n(27723),s=n(86087),l=n(47143),o=n(73463),a=n(34151),c=n(13933),u=n(39793);function d(e,t){return(0,s.useMemo)((()=>e.some((e=>e.supportsBulk&&(!e.isEligible||e.isEligible(t))))),[e,t])}function f(e,t){return(0,s.useMemo)((()=>t.some((t=>e.some((e=>e.supportsBulk&&(!e.isEligible||e.isEligible(t))))))),[e,t])}function m({selection:e,onChangeSelection:t,data:n,actions:l,getItemId:o}){const a=(0,s.useMemo)((()=>n.filter((e=>l.some((t=>t.supportsBulk&&(!t.isEligible||t.isEligible(e))))))),[n,l]),c=n.filter((t=>e.includes(o(t))&&a.includes(t))),d=c.length===a.length;return(0,u.jsx)(i.CheckboxControl,{className:"dataviews-view-table-selection-checkbox",__nextHasNoMarginBottom:!0,checked:d,indeterminate:!d&&!!c.length,onChange:()=>{t(d?[]:a.map((e=>o(e))))},"aria-label":d?(0,r.__)("Deselect all"):(0,r.__)("Select all")})}function v({action:e,onClick:t,isBusy:n,items:r}){const s="string"==typeof e.label?e.label:e.label(r);return(0,u.jsx)(i.Button,{disabled:n,accessibleWhenDisabled:!0,label:s,icon:e.icon,isDestructive:e.isDestructive,size:"compact",onClick:t,isBusy:n,tooltipPosition:"top"})}const p=[];function g({action:e,selectedItems:t,actionInProgress:n,setActionInProgress:i}){const r=(0,l.useRegistry)(),o=(0,s.useMemo)((()=>t.filter((t=>!e.isEligible||e.isEligible(t)))),[e,t]);return"RenderModal"in e?(0,u.jsx)(c.Ud,{action:e,items:o,ActionTrigger:v},e.id):(0,u.jsx)(v,{action:e,onClick:async()=>{i(e.id),await e.callback(t,{registry:r}),i(null)},items:o,isBusy:n===e.id},e.id)}function h(e,t,n,s,l,a,c,d,f){const v=a.length>0?(0,r.sprintf)((0,r._n)("%d Item selected","%d Items selected",a.length),a.length):(0,r.sprintf)((0,r._n)("%d Item","%d Items",e.length),e.length);return(0,u.jsxs)(i.__experimentalHStack,{expanded:!1,className:"dataviews-bulk-actions-footer__container",spacing:3,children:[(0,u.jsx)(m,{selection:s,onChangeSelection:f,data:e,actions:t,getItemId:n}),(0,u.jsx)("span",{className:"dataviews-bulk-actions-footer__item-count",children:v}),(0,u.jsxs)(i.__experimentalHStack,{className:"dataviews-bulk-actions-footer__action-buttons",expanded:!1,spacing:1,children:[l.map((e=>(0,u.jsx)(g,{action:e,selectedItems:a,actionInProgress:c,setActionInProgress:d},e.id))),a.length>0&&(0,u.jsx)(i.Button,{icon:o.A,showTooltip:!0,tooltipPosition:"top",size:"compact",label:(0,r.__)("Cancel"),disabled:!!c,accessibleWhenDisabled:!1,onClick:()=>{f(p)}})]})]})}function x({selection:e,actions:t,onChangeSelection:n,data:i,getItemId:r}){const[l,o]=(0,s.useState)(null),a=(0,s.useRef)(null),c=(0,s.useMemo)((()=>t.filter((e=>e.supportsBulk))),[t]),u=(0,s.useMemo)((()=>i.filter((e=>c.some((t=>!t.isEligible||t.isEligible(e)))))),[i,c]),d=(0,s.useMemo)((()=>i.filter((t=>e.includes(r(t))&&u.includes(t)))),[e,i,r,u]),f=(0,s.useMemo)((()=>t.filter((e=>e.supportsBulk&&e.icon&&d.some((t=>!e.isEligible||e.isEligible(t)))))),[t,d]);return l?(a.current||(a.current=h(i,t,r,e,f,d,l,o,n)),a.current):(a.current&&(a.current=null),h(i,t,r,e,f,d,l,o,n))}function w(){const{data:e,selection:t,actions:n=p,onChangeSelection:i,getItemId:r}=(0,s.useContext)(a.A);return(0,u.jsx)(x,{selection:t,onChangeSelection:i,data:e,actions:n,getItemId:r})}},34151:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var i=n(86087),r=n(36655);const s=(0,i.createContext)({view:{type:r.Ad},onChangeView:()=>{},fields:[],data:[],paginationInfo:{totalItems:0,totalPages:0},selection:[],onChangeSelection:()=>{},setOpenedFilter:()=>{},openedFilter:null,getItemId:e=>e.id,density:0})},13933:(e,t,n)=>{"use strict";n.d(t,{Ay:()=>h,DT:()=>g,Ud:()=>p,cX:()=>v});var i=n(56427),r=n(27723),s=n(86087),l=n(33879),o=n(47143),a=n(28509),c=n(39793);const{DropdownMenuV2:u,kebabCase:d}=(0,a.T)(i.privateApis);function f({action:e,onClick:t,items:n}){const r="string"==typeof e.label?e.label:e.label(n);return(0,c.jsx)(i.Button,{label:r,icon:e.icon,isDestructive:e.isDestructive,size:"compact",onClick:t})}function m({action:e,onClick:t,items:n}){const i="string"==typeof e.label?e.label:e.label(n);return(0,c.jsx)(u.Item,{onClick:t,hideOnClick:!("RenderModal"in e),children:(0,c.jsx)(u.ItemLabel,{children:i})})}function v({action:e,items:t,closeModal:n}){const r="string"==typeof e.label?e.label:e.label(t);return(0,c.jsx)(i.Modal,{title:e.modalHeader||r,__experimentalHideHeader:!!e.hideModalHeader,onRequestClose:null!=n?n:()=>{},focusOnMount:"firstContentElement",size:"small",overlayClassName:`dataviews-action-modal dataviews-action-modal__${d(e.id)}`,children:(0,c.jsx)(e.RenderModal,{items:t,closeModal:n})})}function p({action:e,items:t,ActionTrigger:n,isBusy:i}){const[r,l]=(0,s.useState)(!1),o={action:e,onClick:()=>{l(!0)},items:t,isBusy:i};return(0,c.jsxs)(c.Fragment,{children:[(0,c.jsx)(n,{...o}),r&&(0,c.jsx)(v,{action:e,items:t,closeModal:()=>l(!1)})]})}function g({actions:e,item:t}){const n=(0,o.useRegistry)();return(0,c.jsx)(u.Group,{children:e.map((e=>"RenderModal"in e?(0,c.jsx)(p,{action:e,items:[t],ActionTrigger:m},e.id):(0,c.jsx)(m,{action:e,onClick:()=>{e.callback([t],{registry:n})},items:[t]},e.id)))})}function h({item:e,actions:t,isCompact:n}){const r=(0,o.useRegistry)(),{primaryActions:l,eligibleActions:a}=(0,s.useMemo)((()=>{const n=t.filter((t=>!t.isEligible||t.isEligible(e)));return{primaryActions:n.filter((e=>e.isPrimary&&!!e.icon)),eligibleActions:n}}),[t,e]);return n?(0,c.jsx)(x,{item:e,actions:a}):(0,c.jsxs)(i.__experimentalHStack,{spacing:1,justify:"flex-end",className:"dataviews-item-actions",style:{flexShrink:"0",width:"auto"},children:[!!l.length&&l.map((t=>"RenderModal"in t?(0,c.jsx)(p,{action:t,items:[e],ActionTrigger:f},t.id):(0,c.jsx)(f,{action:t,onClick:()=>{t.callback([e],{registry:r})},items:[e]},t.id))),(0,c.jsx)(x,{item:e,actions:a})]})}function x({item:e,actions:t}){return(0,c.jsx)(u,{trigger:(0,c.jsx)(i.Button,{size:"compact",icon:l.A,label:(0,r.__)("Actions"),accessibleWhenDisabled:!0,disabled:!t.length,className:"dataviews-all-actions-button"}),placement:"bottom-end",children:(0,c.jsx)(g,{actions:t,item:e})})}},97961:(e,t,n)=>{"use strict";n.d(t,{A:()=>Hi});var i=n(56427),r=n(86087),s=n(34151),l=n(49749),o=n(27723),a=n(4921),c=n(73463),u=Object.defineProperty,d=Object.defineProperties,f=Object.getOwnPropertyDescriptors,m=Object.getOwnPropertySymbols,v=Object.prototype.hasOwnProperty,p=Object.prototype.propertyIsEnumerable,g=(e,t,n)=>t in e?u(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,h=(e,t)=>{for(var n in t||(t={}))v.call(t,n)&&g(e,n,t[n]);if(m)for(var n of m(t))p.call(t,n)&&g(e,n,t[n]);return e},x=(e,t)=>d(e,f(t)),w=(e,t)=>{var n={};for(var i in e)v.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&m)for(var i of m(e))t.indexOf(i)<0&&p.call(e,i)&&(n[i]=e[i]);return n},b=Object.defineProperty,y=Object.defineProperties,_=Object.getOwnPropertyDescriptors,C=Object.getOwnPropertySymbols,S=Object.prototype.hasOwnProperty,j=Object.prototype.propertyIsEnumerable,I=(e,t,n)=>t in e?b(e,t,{enumerable:!0,configurable:!0,writable:!0,value:n}):e[t]=n,E=(e,t)=>{for(var n in t||(t={}))S.call(t,n)&&I(e,n,t[n]);if(C)for(var n of C(t))j.call(t,n)&&I(e,n,t[n]);return e},V=(e,t)=>y(e,_(t)),k=(e,t)=>{var n={};for(var i in e)S.call(e,i)&&t.indexOf(i)<0&&(n[i]=e[i]);if(null!=e&&C)for(var i of C(e))t.indexOf(i)<0&&j.call(e,i)&&(n[i]=e[i]);return n};function A(...e){}function O(e,t){return"function"==typeof Object.hasOwn?Object.hasOwn(e,t):Object.prototype.hasOwnProperty.call(e,t)}function N(...e){return(...t)=>{for(const n of e)"function"==typeof n&&n(...t)}}function P(e){return e.normalize("NFD").replace(/[\u0300-\u036f]/g,"")}function M(e){return e}function F(e,t){if(!e){if("string"!=typeof t)throw new Error("Invariant failed");throw new Error(t)}}function L(e){return e.disabled||!0===e["aria-disabled"]||"true"===e["aria-disabled"]}function D(e){const t={};for(const n in e)void 0!==e[n]&&(t[n]=e[n]);return t}function T(...e){for(const t of e)if(void 0!==t)return t}var H=n(51609),B=n.t(H,2);function R(e,t){"function"==typeof e?e(t):e&&(e.current=t)}var z,K="undefined"!=typeof window&&!!(null==(z=window.document)?void 0:z.createElement);function U(e){return e?e.ownerDocument||e:document}function W(e,t=!1){const{activeElement:n}=U(e);if(!(null==n?void 0:n.nodeName))return null;if("IFRAME"===n.tagName&&n.contentDocument)return W(n.contentDocument.body,t);if(t){const e=n.getAttribute("aria-activedescendant");if(e){const t=U(n).getElementById(e);if(t)return t}}return n}function G(e,t){return e===t||e.contains(t)}function $(e){const t=e.tagName.toLowerCase();return"button"===t||!("input"!==t||!e.type)&&-1!==q.indexOf(e.type)}var q=["button","color","file","image","reset","submit"];function Z(e){try{const t=e instanceof HTMLInputElement&&null!==e.selectionStart,n="TEXTAREA"===e.tagName;return t||n||!1}catch(e){return!1}}function X(e){return e.isContentEditable||Z(e)}function Y(e){let t=0,n=0;if(Z(e))t=e.selectionStart||0,n=e.selectionEnd||0;else if(e.isContentEditable){const i=U(e).getSelection();if((null==i?void 0:i.rangeCount)&&i.anchorNode&&G(e,i.anchorNode)&&i.focusNode&&G(e,i.focusNode)){const r=i.getRangeAt(0),s=r.cloneRange();s.selectNodeContents(e),s.setEnd(r.startContainer,r.startOffset),t=s.toString().length,s.setEnd(r.endContainer,r.endOffset),n=s.toString().length}}return{start:t,end:n}}function J(e,t){const n=null==e?void 0:e.getAttribute("role");return n&&-1!==["dialog","menu","listbox","tree","grid"].indexOf(n)?n:t}function Q(e){if(!e)return null;if(e.clientHeight&&e.scrollHeight>e.clientHeight){const{overflowY:t}=getComputedStyle(e);if("visible"!==t&&"hidden"!==t)return e}else if(e.clientWidth&&e.scrollWidth>e.clientWidth){const{overflowX:t}=getComputedStyle(e);if("visible"!==t&&"hidden"!==t)return e}return Q(e.parentElement)||document.scrollingElement||document.body}function ee(e,...t){/text|search|password|tel|url/i.test(e.type)&&e.setSelectionRange(...t)}function te(){return!!K&&/mac|iphone|ipad|ipod/i.test(navigator.platform)}function ne(){return K&&te()&&/apple/i.test(navigator.vendor)}function ie(e){return Boolean(e.currentTarget&&!G(e.currentTarget,e.target))}function re(e){return e.target===e.currentTarget}function se(e,t){const n=new FocusEvent("blur",t),i=e.dispatchEvent(n),r=V(E({},t),{bubbles:!0});return e.dispatchEvent(new FocusEvent("focusout",r)),i}function le(e,t){const n=new MouseEvent("click",t);return e.dispatchEvent(n)}function oe(e,t){const n=t||e.currentTarget,i=e.relatedTarget;return!i||!G(n,i)}function ae(e,t,n,i){const r=(e=>{if(i){const t=setTimeout(e,i);return()=>clearTimeout(t)}const t=requestAnimationFrame(e);return()=>cancelAnimationFrame(t)})((()=>{e.removeEventListener(t,s,!0),n()})),s=()=>{r(),n()};return e.addEventListener(t,s,{once:!0,capture:!0}),r}function ce(e,t,n,i=window){const r=[];try{i.document.addEventListener(e,t,n);for(const s of Array.from(i.frames))r.push(ce(e,t,n,s))}catch(e){}return()=>{try{i.document.removeEventListener(e,t,n)}catch(e){}for(const e of r)e()}}var ue=h({},B),de=ue.useId,fe=(ue.useDeferredValue,ue.useInsertionEffect),me=K?H.useLayoutEffect:H.useEffect;function ve(e){const t=(0,H.useRef)((()=>{throw new Error("Cannot call an event handler while rendering.")}));return fe?fe((()=>{t.current=e})):t.current=e,(0,H.useCallback)(((...e)=>{var n;return null==(n=t.current)?void 0:n.call(t,...e)}),[])}function pe(...e){return(0,H.useMemo)((()=>{if(e.some(Boolean))return t=>{for(const n of e)R(n,t)}}),e)}function ge(e){if(de){const t=de();return e||t}const[t,n]=(0,H.useState)(e);return me((()=>{if(e||t)return;const i=Math.random().toString(36).substr(2,6);n(`id-${i}`)}),[e,t]),e||t}function he(e,t){const n=e=>{if("string"==typeof e)return e},[i,r]=(0,H.useState)((()=>n(t)));return me((()=>{const i=e&&"current"in e?e.current:e;r((null==i?void 0:i.tagName.toLowerCase())||n(t))}),[e,t]),i}function xe(e,t){const n=(0,H.useRef)(!1);(0,H.useEffect)((()=>{if(n.current)return e();n.current=!0}),t),(0,H.useEffect)((()=>()=>{n.current=!1}),[])}function we(e){return ve("function"==typeof e?e:()=>e)}function be(e,t,n=[]){const i=(0,H.useCallback)((n=>(e.wrapElement&&(n=e.wrapElement(n)),t(n))),[...n,e.wrapElement]);return x(h({},e),{wrapElement:i})}var ye=!1,_e=0,Ce=0;function Se(e){(function(e){const t=e.movementX||e.screenX-_e,n=e.movementY||e.screenY-Ce;return _e=e.screenX,Ce=e.screenY,t||n||!1})(e)&&(ye=!0)}function je(){ye=!1}var Ie=n(39793);function Ee(e){const t=H.forwardRef(((t,n)=>e(x(h({},t),{ref:n}))));return t.displayName=e.displayName||e.name,t}function Ve(e,t){return H.memo(e,t)}function ke(e,t){const n=t,{wrapElement:i,render:r}=n,s=w(n,["wrapElement","render"]),l=pe(t.ref,function(e){return function(e){return!!e&&!!(0,H.isValidElement)(e)&&("ref"in e.props||"ref"in e)}(e)?h({},e.props).ref||e.ref:null}(r));let o;if(H.isValidElement(r)){const e=x(h({},r.props),{ref:l});o=H.cloneElement(r,function(e,t){const n=h({},e);for(const i in t){if(!O(t,i))continue;if("className"===i){const i="className";n[i]=e[i]?`${e[i]} ${t[i]}`:t[i];continue}if("style"===i){const i="style";n[i]=e[i]?h(h({},e[i]),t[i]):t[i];continue}const r=t[i];if("function"==typeof r&&i.startsWith("on")){const t=e[i];if("function"==typeof t){n[i]=(...e)=>{r(...e),t(...e)};continue}}n[i]=r}return n}(s,e))}else o=r?r(s):(0,Ie.jsx)(e,h({},s));return i?i(o):o}function Ae(e){const t=(t={})=>e(t);return t.displayName=e.name,t}function Oe(e=[],t=[]){const n=H.createContext(void 0),i=H.createContext(void 0),r=()=>H.useContext(n),s=t=>e.reduceRight(((e,n)=>(0,Ie.jsx)(n,x(h({},t),{children:e}))),(0,Ie.jsx)(n.Provider,h({},t)));return{context:n,scopedContext:i,useContext:r,useScopedContext:(e=!1)=>{const t=H.useContext(i),n=r();return e?t:t||n},useProviderContext:()=>{const e=H.useContext(i),t=r();if(!e||e!==t)return t},ContextProvider:s,ScopedContextProvider:e=>(0,Ie.jsx)(s,x(h({},e),{children:t.reduceRight(((t,n)=>(0,Ie.jsx)(n,x(h({},e),{children:t}))),(0,Ie.jsx)(i.Provider,h({},e)))}))}}var Ne=Oe(),Pe=Ne.useContext,Me=(Ne.useScopedContext,Ne.useProviderContext,Oe([Ne.ContextProvider],[Ne.ScopedContextProvider])),Fe=Me.useContext,Le=(Me.useScopedContext,Me.useProviderContext),De=Me.ContextProvider,Te=Me.ScopedContextProvider,He=(0,H.createContext)(void 0),Be=(0,H.createContext)(void 0),Re=((0,H.createContext)(null),(0,H.createContext)(null),Oe([De],[Te])),ze=Re.useContext;function Ke(e,t){const n=e.__unstableInternals;return F(n,"Invalid store"),n[t]}function Ue(e,...t){let n=e,i=n,r=Symbol(),s=A;const l=new Set,o=new Set,a=new Set,c=new Set,u=new Set,d=new WeakMap,f=new WeakMap,m=(e,t,n=c)=>(n.add(t),f.set(t,e),()=>{var e;null==(e=d.get(t))||e(),d.delete(t),f.delete(t),n.delete(t)}),v=(e,s,l=!1)=>{var a;if(!O(n,e))return;const m=(v=s,p=n[e],function(e){return"function"==typeof e}(v)?v(function(e){return"function"==typeof e}(p)?p():p):v);var v,p;if(m===n[e])return;if(!l)for(const n of t)null==(a=null==n?void 0:n.setState)||a.call(n,e,m);const g=n;n=V(E({},n),{[e]:m});const h=Symbol();r=h,o.add(e);const x=(t,i,r)=>{var s;const l=f.get(t);l&&!l.some((t=>r?r.has(t):t===e))||(null==(s=d.get(t))||s(),d.set(t,t(n,i)))};for(const e of c)x(e,g);queueMicrotask((()=>{if(r!==h)return;const e=n;for(const e of u)x(e,i,o);i=e,o.clear()}))},p={getState:()=>n,setState:v,__unstableInternals:{setup:e=>(a.add(e),()=>a.delete(e)),init:()=>{const e=l.size,i=Symbol();l.add(i);const r=()=>{l.delete(i),l.size||s()};if(e)return r;const o=(c=n,Object.keys(c)).map((e=>N(...t.map((t=>{var n;const i=null==(n=null==t?void 0:t.getState)?void 0:n.call(t);if(i&&O(i,e))return qe(t,[e],(t=>{v(e,t[e],!0)}))})))));var c;const u=[];for(const e of a)u.push(e());const d=t.map(Ge);return s=N(...o,...u,...d),r},subscribe:(e,t)=>m(e,t),sync:(e,t)=>(d.set(t,t(n,n)),m(e,t)),batch:(e,t)=>(d.set(t,t(n,i)),m(e,t,u)),pick:e=>Ue(function(e,t){const n={};for(const i of t)O(e,i)&&(n[i]=e[i]);return n}(n,e),p),omit:e=>Ue(function(e,t){const n=E({},e);for(const e of t)O(n,e)&&delete n[e];return n}(n,e),p)}};return p}function We(e,...t){if(e)return Ke(e,"setup")(...t)}function Ge(e,...t){if(e)return Ke(e,"init")(...t)}function $e(e,...t){if(e)return Ke(e,"subscribe")(...t)}function qe(e,...t){if(e)return Ke(e,"sync")(...t)}function Ze(e,...t){if(e)return Ke(e,"batch")(...t)}function Xe(e,...t){if(e)return Ke(e,"omit")(...t)}function Ye(...e){const t=e.reduce(((e,t)=>{var n;const i=null==(n=null==t?void 0:t.getState)?void 0:n.call(t);return i?Object.assign(e,i):e}),{});return Ue(t,...e)}Re.useScopedContext,Re.useProviderContext,Re.ContextProvider,Re.ScopedContextProvider;var Je=n(94652),{useSyncExternalStore:Qe}=Je;function et(e,t=M){const n=H.useCallback((t=>e?$e(e,null,t):()=>{}),[e]),i=()=>{const n="string"==typeof t?t:null,i="function"==typeof t?t:null,r=null==e?void 0:e.getState();return i?i(r):r&&n&&O(r,n)?r[n]:void 0};return Qe(n,i,i)}function tt(e,t,n,i){const r=O(t,n)?t[n]:void 0,s=i?t[i]:void 0,l=function(e){const t=(0,H.useRef)(e);return me((()=>{t.current=e})),t}({value:r,setValue:s});me((()=>qe(e,[n],((e,t)=>{const{value:i,setValue:r}=l.current;r&&e[n]!==t[n]&&e[n]!==i&&r(e[n])}))),[e,n]),me((()=>{if(void 0!==r)return e.setState(n,r),Ze(e,[n],(()=>{void 0!==r&&e.setState(n,r)}))}))}function nt(e,t,n){return tt(e=function(e,t,n){return xe(t,[n.store]),tt(e,n,"items","setItems"),e}(e,t,n),n,"activeId","setActiveId"),tt(e,n,"includesBaseElement"),tt(e,n,"virtualFocus"),tt(e,n,"orientation"),tt(e,n,"rtl"),tt(e,n,"focusLoop"),tt(e,n,"focusWrap"),tt(e,n,"focusShift"),e}function it(e,t,n){return function(e,t,n){return xe(t,[n.store,n.disclosure]),tt(e,n,"open","setOpen"),tt(e,n,"mounted","setMounted"),tt(e,n,"animated"),Object.assign(e,{disclosure:n.disclosure})}(e,t,n)}function rt(e={}){var t;e.store;const n=null==(t=e.store)?void 0:t.getState(),i=T(e.items,null==n?void 0:n.items,e.defaultItems,[]),r=new Map(i.map((e=>[e.id,e]))),s={items:i,renderedItems:T(null==n?void 0:n.renderedItems,[])},l=null==(o=e.store)?void 0:o.__unstablePrivateStore;var o;const a=Ue({items:i,renderedItems:s.renderedItems},l),c=Ue(s,e.store),u=e=>{const t=function(e){const t=e.map(((e,t)=>[t,e]));let n=!1;return t.sort((([e,t],[i,r])=>{const s=t.element,l=r.element;return s===l?0:s&&l?function(e,t){return Boolean(t.compareDocumentPosition(e)&Node.DOCUMENT_POSITION_PRECEDING)}(s,l)?(e>i&&(n=!0),-1):(e<i&&(n=!0),1):0})),n?t.map((([e,t])=>t)):e}(e);a.setState("renderedItems",t),c.setState("renderedItems",t)};We(c,(()=>Ge(a))),We(a,(()=>Ze(a,["items"],(e=>{c.setState("items",e.items)})))),We(a,(()=>Ze(a,["renderedItems"],(e=>{let t=!0,n=requestAnimationFrame((()=>{const{renderedItems:t}=c.getState();e.renderedItems!==t&&u(e.renderedItems)}));if("function"!=typeof IntersectionObserver)return()=>cancelAnimationFrame(n);const i=function(e){var t;const n=e.find((e=>!!e.element)),i=[...e].reverse().find((e=>!!e.element));let r=null==(t=null==n?void 0:n.element)?void 0:t.parentElement;for(;r&&(null==i?void 0:i.element);){if(i&&r.contains(i.element))return r;r=r.parentElement}return U(r).body}(e.renderedItems),r=new IntersectionObserver((()=>{t?t=!1:(cancelAnimationFrame(n),n=requestAnimationFrame((()=>u(e.renderedItems))))}),{root:i});for(const t of e.renderedItems)t.element&&r.observe(t.element);return()=>{cancelAnimationFrame(n),r.disconnect()}}))));const d=(e,t,n=!1)=>{let i;return t((t=>{const n=t.findIndex((({id:t})=>t===e.id)),s=t.slice();if(-1!==n){i=t[n];const l=E(E({},i),e);s[n]=l,r.set(e.id,l)}else s.push(e),r.set(e.id,e);return s})),()=>{t((t=>{if(!i)return n&&r.delete(e.id),t.filter((({id:t})=>t!==e.id));const s=t.findIndex((({id:t})=>t===e.id));if(-1===s)return t;const l=t.slice();return l[s]=i,r.set(e.id,i),l}))}},f=e=>d(e,(e=>a.setState("items",e)),!0);return V(E({},c),{registerItem:f,renderItem:e=>N(f(e),d(e,(e=>a.setState("renderedItems",e)))),item:e=>{if(!e)return null;let t=r.get(e);if(!t){const{items:n}=c.getState();t=n.find((t=>t.id===e)),t&&r.set(e,t)}return t||null},__unstablePrivateStore:a})}function st(e){const t=[];for(const n of e)t.push(...n);return t}function lt(e){return e.slice().reverse()}var ot={id:null};function at(e,t){return e.find((e=>t?!e.disabled&&e.id!==t:!e.disabled))}function ct(e,t){return e.filter((e=>e.rowId===t))}function ut(e){const t=[];for(const n of e){const e=t.find((e=>{var t;return(null==(t=e[0])?void 0:t.rowId)===n.rowId}));e?e.push(n):t.push([n])}return t}function dt(e){let t=0;for(const{length:n}of e)n>t&&(t=n);return t}function ft(e,t,n){const i=dt(e);for(const r of e)for(let e=0;e<i;e+=1){const i=r[e];if(!i||n&&i.disabled){const i=0===e&&n?at(r):r[e-1];r[e]=i&&t!==i.id&&n?i:{id:"__EMPTY_ITEM__",disabled:!0,rowId:null==i?void 0:i.rowId}}}return e}function mt(e){const t=ut(e),n=dt(t),i=[];for(let e=0;e<n;e+=1)for(const n of t){const t=n[e];t&&i.push(V(E({},t),{rowId:t.rowId?`${e}`:void 0}))}return i}var vt=ne()&&K&&!!navigator.maxTouchPoints;function pt(e={}){var t=e,{tag:n}=t,i=k(t,["tag"]);const r=Ye(i.store,function(e,...t){if(e)return Ke(e,"pick")(...t)}(n,["value","rtl"])),s=null==n?void 0:n.getState(),l=null==r?void 0:r.getState(),o=T(i.activeId,null==l?void 0:l.activeId,i.defaultActiveId,null),a=function(e={}){var t;const n=null==(t=e.store)?void 0:t.getState(),i=rt(e),r=T(e.activeId,null==n?void 0:n.activeId,e.defaultActiveId),s=Ue(V(E({},i.getState()),{activeId:r,baseElement:T(null==n?void 0:n.baseElement,null),includesBaseElement:T(e.includesBaseElement,null==n?void 0:n.includesBaseElement,null===r),moves:T(null==n?void 0:n.moves,0),orientation:T(e.orientation,null==n?void 0:n.orientation,"both"),rtl:T(e.rtl,null==n?void 0:n.rtl,!1),virtualFocus:T(e.virtualFocus,null==n?void 0:n.virtualFocus,!1),focusLoop:T(e.focusLoop,null==n?void 0:n.focusLoop,!1),focusWrap:T(e.focusWrap,null==n?void 0:n.focusWrap,!1),focusShift:T(e.focusShift,null==n?void 0:n.focusShift,!1)}),i,e.store);We(s,(()=>qe(s,["renderedItems","activeId"],(e=>{s.setState("activeId",(t=>{var n;return void 0!==t?t:null==(n=at(e.renderedItems))?void 0:n.id}))}))));const l=(e,t,n,i)=>{var r,l;const{activeId:o,rtl:a,focusLoop:c,focusWrap:u,includesBaseElement:d}=s.getState(),f=a&&"vertical"!==t?lt(e):e;if(null==o)return null==(r=at(f))?void 0:r.id;const m=f.find((e=>e.id===o));if(!m)return null==(l=at(f))?void 0:l.id;const v=!!m.rowId,p=f.indexOf(m),g=f.slice(p+1),h=ct(g,m.rowId);if(void 0!==i){const e=function(e,t){return e.filter((e=>t?!e.disabled&&e.id!==t:!e.disabled))}(h,o),t=e.slice(i)[0]||e[e.length-1];return null==t?void 0:t.id}const x=function(e){return"vertical"===e?"horizontal":"horizontal"===e?"vertical":void 0}(v?t||"horizontal":t),w=c&&c!==x,b=v&&u&&u!==x;if(n=n||!v&&w&&d,w){const e=function(e,t,n=!1){const i=e.findIndex((e=>e.id===t));return[...e.slice(i+1),...n?[ot]:[],...e.slice(0,i)]}(b&&!n?f:ct(f,m.rowId),o,n),t=at(e,o);return null==t?void 0:t.id}if(b){const e=at(n?h:g,o);return n?(null==e?void 0:e.id)||null:null==e?void 0:e.id}const y=at(h,o);return!y&&n?null:null==y?void 0:y.id};return V(E(E({},i),s),{setBaseElement:e=>s.setState("baseElement",e),setActiveId:e=>s.setState("activeId",e),move:e=>{void 0!==e&&(s.setState("activeId",e),s.setState("moves",(e=>e+1)))},first:()=>{var e;return null==(e=at(s.getState().renderedItems))?void 0:e.id},last:()=>{var e;return null==(e=at(lt(s.getState().renderedItems)))?void 0:e.id},next:e=>{const{renderedItems:t,orientation:n}=s.getState();return l(t,n,!1,e)},previous:e=>{var t;const{renderedItems:n,orientation:i,includesBaseElement:r}=s.getState(),o=!(null==(t=at(n))?void 0:t.rowId)&&r;return l(lt(n),i,o,e)},down:e=>{const{activeId:t,renderedItems:n,focusShift:i,focusLoop:r,includesBaseElement:o}=s.getState(),a=i&&!e,c=mt(st(ft(ut(n),t,a)));return l(c,"vertical",r&&"horizontal"!==r&&o,e)},up:e=>{const{activeId:t,renderedItems:n,focusShift:i,includesBaseElement:r}=s.getState(),o=i&&!e,a=mt(lt(st(ft(ut(n),t,o))));return l(a,"vertical",r,e)}})}(V(E({},i),{activeId:o,includesBaseElement:T(i.includesBaseElement,null==l?void 0:l.includesBaseElement,!0),orientation:T(i.orientation,null==l?void 0:l.orientation,"vertical"),focusLoop:T(i.focusLoop,null==l?void 0:l.focusLoop,!0),focusWrap:T(i.focusWrap,null==l?void 0:l.focusWrap,!0),virtualFocus:T(i.virtualFocus,null==l?void 0:l.virtualFocus,!0)})),c=function(e={}){var t=e,{popover:n}=t,i=k(t,["popover"]);const r=Ye(i.store,Xe(n,["arrowElement","anchorElement","contentElement","popoverElement","disclosureElement"])),s=null==r?void 0:r.getState(),l=function(e={}){return function(e={}){const t=Ye(e.store,Xe(e.disclosure,["contentElement","disclosureElement"])),n=null==t?void 0:t.getState(),i=T(e.open,null==n?void 0:n.open,e.defaultOpen,!1),r=T(e.animated,null==n?void 0:n.animated,!1),s=Ue({open:i,animated:r,animating:!!r&&i,mounted:i,contentElement:T(null==n?void 0:n.contentElement,null),disclosureElement:T(null==n?void 0:n.disclosureElement,null)},t);return We(s,(()=>qe(s,["animated","animating"],(e=>{e.animated||s.setState("animating",!1)})))),We(s,(()=>$e(s,["open"],(()=>{s.getState().animated&&s.setState("animating",!0)})))),We(s,(()=>qe(s,["open","animating"],(e=>{s.setState("mounted",e.open||e.animating)})))),V(E({},s),{disclosure:e.disclosure,setOpen:e=>s.setState("open",e),show:()=>s.setState("open",!0),hide:()=>s.setState("open",!1),toggle:()=>s.setState("open",(e=>!e)),stopAnimation:()=>s.setState("animating",!1),setContentElement:e=>s.setState("contentElement",e),setDisclosureElement:e=>s.setState("disclosureElement",e)})}(e)}(V(E({},i),{store:r})),o=T(i.placement,null==s?void 0:s.placement,"bottom"),a=Ue(V(E({},l.getState()),{placement:o,currentPlacement:o,anchorElement:T(null==s?void 0:s.anchorElement,null),popoverElement:T(null==s?void 0:s.popoverElement,null),arrowElement:T(null==s?void 0:s.arrowElement,null),rendered:Symbol("rendered")}),l,r);return V(E(E({},l),a),{setAnchorElement:e=>a.setState("anchorElement",e),setPopoverElement:e=>a.setState("popoverElement",e),setArrowElement:e=>a.setState("arrowElement",e),render:()=>a.setState("rendered",Symbol("rendered"))})}(V(E({},i),{placement:T(i.placement,null==l?void 0:l.placement,"bottom-start")})),u=T(i.value,null==l?void 0:l.value,i.defaultValue,""),d=T(i.selectedValue,null==l?void 0:l.selectedValue,null==s?void 0:s.values,i.defaultSelectedValue,""),f=Array.isArray(d),m=V(E(E({},a.getState()),c.getState()),{value:u,selectedValue:d,resetValueOnSelect:T(i.resetValueOnSelect,null==l?void 0:l.resetValueOnSelect,f),resetValueOnHide:T(i.resetValueOnHide,null==l?void 0:l.resetValueOnHide,f&&!n),activeValue:null==l?void 0:l.activeValue}),v=Ue(m,a,c,r);return vt&&We(v,(()=>qe(v,["virtualFocus"],(()=>{v.setState("virtualFocus",!1)})))),We(v,(()=>{if(n)return N(qe(v,["selectedValue"],(e=>{Array.isArray(e.selectedValue)&&n.setValues(e.selectedValue)})),qe(n,["values"],(e=>{v.setState("selectedValue",e.values)})))})),We(v,(()=>qe(v,["resetValueOnHide","mounted"],(e=>{e.resetValueOnHide&&(e.mounted||v.setState("value",u))})))),We(v,(()=>Ze(v,["mounted"],(e=>{e.mounted||(v.setState("activeId",o),v.setState("moves",0))})))),We(v,(()=>qe(v,["moves","activeId"],((e,t)=>{e.moves===t.moves&&v.setState("activeValue",void 0)})))),We(v,(()=>Ze(v,["moves","renderedItems"],((e,t)=>{if(e.moves===t.moves)return;const{activeId:n}=v.getState(),i=a.item(n);v.setState("activeValue",null==i?void 0:i.value)})))),V(E(E(E({},c),a),v),{tag:n,setValue:e=>v.setState("value",e),resetValue:()=>v.setState("value",m.value),setSelectedValue:e=>v.setState("selectedValue",e)})}function gt(e={}){const t=ze();e=x(h({},e),{tag:void 0!==e.tag?e.tag:t});const[n,i]=function(e,t){const[n,i]=H.useState((()=>e(t)));me((()=>Ge(n)),[n]);const r=H.useCallback((e=>et(n,e)),[n]);return[H.useMemo((()=>x(h({},n),{useState:r})),[n,r]),ve((()=>{i((n=>e(h(h({},t),n.getState()))))}))]}(pt,e);return function(e,t,n){return xe(t,[n.tag]),tt(e,n,"value","setValue"),tt(e,n,"selectedValue","setSelectedValue"),tt(e,n,"resetValueOnHide"),tt(e,n,"resetValueOnSelect"),Object.assign(nt(function(e,t,n){return xe(t,[n.popover]),tt(e,n,"placement"),it(e,t,n)}(e,t,n),t,n),{tag:n.tag})}(n,i,e)}var ht=Oe(),xt=(ht.useContext,ht.useScopedContext,ht.useProviderContext),wt=Oe([ht.ContextProvider],[ht.ScopedContextProvider]),bt=(wt.useContext,wt.useScopedContext,wt.useProviderContext,wt.ContextProvider),yt=wt.ScopedContextProvider,_t=((0,H.createContext)(void 0),(0,H.createContext)(void 0),Oe([bt],[yt])),Ct=(_t.useContext,_t.useScopedContext,_t.useProviderContext),St=_t.ContextProvider,jt=_t.ScopedContextProvider,It=(0,H.createContext)(void 0),Et=Oe([St,De],[jt,Te]),Vt=Et.useContext,kt=Et.useScopedContext,At=Et.useProviderContext,Ot=Et.ContextProvider,Nt=Et.ScopedContextProvider,Pt=(0,H.createContext)(void 0),Mt=(0,H.createContext)(!1);function Ft(e={}){const t=gt(e);return(0,Ie.jsx)(Ot,{value:t,children:e.children})}var Lt=Ae((function(e){var t=e,{store:n}=t,i=w(t,["store"]);const r=At();F(n=n||r,!1);const s=n.useState((e=>{var t;return null==(t=e.baseElement)?void 0:t.id}));return D(i=h({htmlFor:s},i))})),Dt=Ve(Ee((function(e){return ke("label",Lt(e))}))),Tt=Ae((function(e){var t=e,{store:n}=t,i=w(t,["store"]);const r=Ct();return n=n||r,x(h({},i),{ref:pe(null==n?void 0:n.setAnchorElement,i.ref)})}));function Ht(e,t){return t&&e.item(t)||null}Ee((function(e){return ke("div",Tt(e))}));var Bt=Symbol("FOCUS_SILENTLY");function Rt(e,t,n){if(!t)return!1;if(t===n)return!1;const i=e.item(t.id);return!(!i||n&&i.element===n)}var zt=(0,H.createContext)(!0);function Kt(e){return!!e.matches("input:not([type='hidden']):not([disabled]), select:not([disabled]), textarea:not([disabled]), a[href], button:not([disabled]), [tabindex], summary, iframe, object, embed, area[href], audio[controls], video[controls], [contenteditable]:not([contenteditable='false'])")&&!!function(e){if("function"==typeof e.checkVisibility)return e.checkVisibility();const t=e;return t.offsetWidth>0||t.offsetHeight>0||e.getClientRects().length>0}(e)&&!e.closest("[inert]")}function Ut(e){const t=W(e);if(!t)return!1;if(t===e)return!0;const n=t.getAttribute("aria-activedescendant");return!!n&&n===e.id}function Wt(e){const t=W(e);if(!t)return!1;if(G(e,t))return!0;const n=t.getAttribute("aria-activedescendant");return!!n&&"id"in e&&(n===e.id||!!e.querySelector(`#${CSS.escape(n)}`))}var Gt=ne(),$t=["text","search","url","tel","email","password","number","date","month","week","time","datetime","datetime-local"];function qt(e){return!("input"!==e.tagName.toLowerCase()||!e.type||"radio"!==e.type&&"checkbox"!==e.type)}function Zt(e,t,n,i,r){return e?t?n&&!i?-1:void 0:n?r:r||0:r}function Xt(e,t){return ve((n=>{null==e||e(n),n.defaultPrevented||t&&(n.stopPropagation(),n.preventDefault())}))}var Yt=!0;function Jt(e){const t=e.target;t&&"hasAttribute"in t&&(t.hasAttribute("data-focus-visible")||(Yt=!1))}function Qt(e){e.metaKey||e.ctrlKey||e.altKey||(Yt=!0)}var en=Ae((function(e){var t=e,{focusable:n=!0,accessibleWhenDisabled:i,autoFocus:r,onFocusVisible:s}=t,l=w(t,["focusable","accessibleWhenDisabled","autoFocus","onFocusVisible"]);const o=(0,H.useRef)(null);(0,H.useEffect)((()=>{n&&(ce("mousedown",Jt,!0),ce("keydown",Qt,!0))}),[n]),Gt&&(0,H.useEffect)((()=>{if(!n)return;const e=o.current;if(!e)return;if(!qt(e))return;const t=function(e){return"labels"in e?e.labels:null}(e);if(!t)return;const i=()=>queueMicrotask((()=>e.focus()));for(const e of t)e.addEventListener("mouseup",i);return()=>{for(const e of t)e.removeEventListener("mouseup",i)}}),[n]);const a=n&&L(l),c=!!a&&!i,[u,d]=(0,H.useState)(!1);(0,H.useEffect)((()=>{n&&c&&u&&d(!1)}),[n,c,u]),(0,H.useEffect)((()=>{if(!n)return;if(!u)return;const e=o.current;if(!e)return;if("undefined"==typeof IntersectionObserver)return;const t=new IntersectionObserver((()=>{Kt(e)||d(!1)}));return t.observe(e),()=>t.disconnect()}),[n,u]);const f=Xt(l.onKeyPressCapture,a),m=Xt(l.onMouseDownCapture,a),v=Xt(l.onClickCapture,a),p=l.onMouseDown,g=ve((e=>{if(null==p||p(e),e.defaultPrevented)return;if(!n)return;const t=e.currentTarget;if(!Gt)return;if(ie(e))return;if(!$(t)&&!qt(t))return;let i=!1;const r=()=>{i=!0};t.addEventListener("focusin",r,{capture:!0,once:!0}),ae(t,"mouseup",(()=>{t.removeEventListener("focusin",r,!0),i||function(e){!Wt(e)&&Kt(e)&&e.focus()}(t)}))})),b=(e,t)=>{if(t&&(e.currentTarget=t),!n)return;const i=e.currentTarget;i&&Ut(i)&&(null==s||s(e),e.defaultPrevented||d(!0))},y=l.onKeyDownCapture,_=ve((e=>{if(null==y||y(e),e.defaultPrevented)return;if(!n)return;if(u)return;if(e.metaKey)return;if(e.altKey)return;if(e.ctrlKey)return;if(!re(e))return;const t=e.currentTarget;ae(t,"focusout",(()=>b(e,t)))})),C=l.onFocusCapture,S=ve((e=>{if(null==C||C(e),e.defaultPrevented)return;if(!n)return;if(!re(e))return void d(!1);const t=e.currentTarget;Yt||function(e){const{tagName:t,readOnly:n,type:i}=e;return"TEXTAREA"===t&&!n||"SELECT"===t&&!n||("INPUT"!==t||n?!!e.isContentEditable||!("combobox"!==e.getAttribute("role")||!e.dataset.name):$t.includes(i))}(e.target)?ae(e.target,"focusout",(()=>b(e,t))):d(!1)})),j=l.onBlur,I=ve((e=>{null==j||j(e),n&&oe(e)&&d(!1)})),E=(0,H.useContext)(zt),V=ve((e=>{n&&r&&e&&E&&queueMicrotask((()=>{Ut(e)||Kt(e)&&e.focus()}))})),k=he(o),A=n&&function(e){return!e||"button"===e||"summary"===e||"input"===e||"select"===e||"textarea"===e||"a"===e}(k),O=n&&function(e){return!e||"button"===e||"input"===e||"select"===e||"textarea"===e}(k),N=l.style,P=(0,H.useMemo)((()=>c?h({pointerEvents:"none"},N):N),[c,N]);return D(l=x(h({"data-focus-visible":n&&u||void 0,"data-autofocus":r||void 0,"aria-disabled":a||void 0},l),{ref:pe(o,V,l.ref),style:P,tabIndex:Zt(n,c,A,O,l.tabIndex),disabled:!(!O||!c)||void 0,contentEditable:a?void 0:l.contentEditable,onKeyPressCapture:f,onClickCapture:v,onMouseDownCapture:m,onMouseDown:g,onKeyDownCapture:_,onFocusCapture:S,onBlur:I}))}));function tn(e,t,n){return ve((i=>{var r;if(null==t||t(i),i.defaultPrevented)return;if(i.isPropagationStopped())return;if(!re(i))return;if(function(e){return"Shift"===e.key||"Control"===e.key||"Alt"===e.key||"Meta"===e.key}(i))return;if(function(e){const t=e.target;return!(t&&!Z(t)||1!==e.key.length||e.ctrlKey||e.metaKey)}(i))return;const s=e.getState(),l=null==(r=Ht(e,s.activeId))?void 0:r.element;if(!l)return;const o=i,{view:a}=o,c=w(o,["view"]);l!==(null==n?void 0:n.current)&&l.focus(),function(e,t,n){const i=new KeyboardEvent(t,n);return e.dispatchEvent(i)}(l,i.type,c)||i.preventDefault(),i.currentTarget.contains(l)&&i.stopPropagation()}))}Ee((function(e){return ke("div",en(e))}));var nn=Ae((function(e){var t=e,{store:n,composite:i=!0,focusOnMove:r=i,moveOnKeyPress:s=!0}=t,l=w(t,["store","composite","focusOnMove","moveOnKeyPress"]);const o=Le();F(n=n||o,!1);const a=(0,H.useRef)(null),c=(0,H.useRef)(null),u=function(e){const[t,n]=(0,H.useState)(!1),i=(0,H.useCallback)((()=>n(!0)),[]),r=e.useState((t=>Ht(e,t.activeId)));return(0,H.useEffect)((()=>{const e=null==r?void 0:r.element;t&&e&&(n(!1),e.focus({preventScroll:!0}))}),[r,t]),i}(n),d=n.useState("moves"),[,f]=function(e){const[t,n]=(0,H.useState)(null);return me((()=>{if(null==t)return;if(!e)return;let n=null;return e((e=>(n=e,t))),()=>{e(n)}}),[t,e]),[t,n]}(i?n.setBaseElement:null);(0,H.useEffect)((()=>{var e;if(!n)return;if(!d)return;if(!i)return;if(!r)return;const{activeId:t}=n.getState(),s=null==(e=Ht(n,t))?void 0:e.element;var l;s&&("scrollIntoView"in(l=s)?(l.focus({preventScroll:!0}),l.scrollIntoView(E({block:"nearest",inline:"nearest"},undefined))):l.focus())}),[n,d,i,r]),me((()=>{if(!n)return;if(!d)return;if(!i)return;const{baseElement:e,activeId:t}=n.getState();if(null!==t)return;if(!e)return;const r=c.current;c.current=null,r&&se(r,{relatedTarget:e}),Ut(e)||e.focus()}),[n,d,i]);const m=n.useState("activeId"),v=n.useState("virtualFocus");me((()=>{var e;if(!n)return;if(!i)return;if(!v)return;const t=c.current;if(c.current=null,!t)return;const r=(null==(e=Ht(n,m))?void 0:e.element)||W(t);r!==t&&se(t,{relatedTarget:r})}),[n,m,v,i]);const p=tn(n,l.onKeyDownCapture,c),g=tn(n,l.onKeyUpCapture,c),b=l.onFocusCapture,y=ve((e=>{if(null==b||b(e),e.defaultPrevented)return;if(!n)return;const{virtualFocus:t}=n.getState();if(!t)return;const i=e.relatedTarget,r=function(e){const t=e[Bt];return delete e[Bt],t}(e.currentTarget);re(e)&&r&&(e.stopPropagation(),c.current=i)})),_=l.onFocus,C=ve((e=>{if(null==_||_(e),e.defaultPrevented)return;if(!i)return;if(!n)return;const{relatedTarget:t}=e,{virtualFocus:r}=n.getState();r?re(e)&&!Rt(n,t)&&queueMicrotask(u):re(e)&&n.setActiveId(null)})),S=l.onBlurCapture,j=ve((e=>{var t;if(null==S||S(e),e.defaultPrevented)return;if(!n)return;const{virtualFocus:i,activeId:r}=n.getState();if(!i)return;const s=null==(t=Ht(n,r))?void 0:t.element,l=e.relatedTarget,o=Rt(n,l),a=c.current;c.current=null,re(e)&&o?(l===s?a&&a!==l&&se(a,e):s?se(s,e):a&&se(a,e),e.stopPropagation()):!Rt(n,e.target)&&s&&se(s,e)})),I=l.onKeyDown,V=we(s),k=ve((e=>{var t;if(null==I||I(e),e.defaultPrevented)return;if(!n)return;if(!re(e))return;const{orientation:i,items:r,renderedItems:s,activeId:l}=n.getState(),o=Ht(n,l);if(null==(t=null==o?void 0:o.element)?void 0:t.isConnected)return;const a="horizontal"!==i,c="vertical"!==i,u=function(e){return e.some((e=>!!e.rowId))}(s);if(("ArrowLeft"===e.key||"ArrowRight"===e.key||"Home"===e.key||"End"===e.key)&&Z(e.currentTarget))return;const d={ArrowUp:(u||a)&&(()=>{if(u){const e=r&&function(e){return function(e){return e.find((e=>!e.disabled))}(st(lt(function(e){const t=[];for(const n of e){const e=t.find((e=>{var t;return(null==(t=e[0])?void 0:t.rowId)===n.rowId}));e?e.push(n):t.push([n])}return t}(e))))}(r);return null==e?void 0:e.id}return null==n?void 0:n.last()}),ArrowRight:(u||c)&&n.first,ArrowDown:(u||a)&&n.first,ArrowLeft:(u||c)&&n.last,Home:n.first,End:n.last,PageUp:n.first,PageDown:n.last},f=d[e.key];if(f){const t=f();if(void 0!==t){if(!V(e))return;e.preventDefault(),n.move(t)}}}));l=be(l,(e=>(0,Ie.jsx)(De,{value:n,children:e})),[n]);const A=n.useState((e=>{var t;if(n&&i&&e.virtualFocus)return null==(t=Ht(n,e.activeId))?void 0:t.id}));l=x(h({"aria-activedescendant":A},l),{ref:pe(a,f,l.ref),onKeyDownCapture:p,onKeyUpCapture:g,onFocusCapture:y,onFocus:C,onBlurCapture:j,onKeyDown:k});const O=n.useState((e=>i&&(e.virtualFocus||null===e.activeId)));return en(h({focusable:O},l))}));function rn(e,t,n){if(!n)return!1;const i=e.find((e=>!e.disabled&&e.value));return(null==i?void 0:i.value)===t}function sn(e,t){return!!t&&null!=e&&(e=P(e),t.length>e.length&&0===t.toLowerCase().indexOf(e.toLowerCase()))}Ee((function(e){return ke("div",nn(e))}));var ln=Ae((function(e){var t=e,{store:n,focusable:i=!0,autoSelect:r=!1,getAutoSelectId:s,setValueOnChange:l,showMinLength:o=0,showOnChange:a,showOnMouseDown:c,showOnClick:u=c,showOnKeyDown:d,showOnKeyPress:f=d,blurActiveItemOnClick:m,setValueOnClick:v=!0,moveOnKeyPress:p=!0,autoComplete:g="list"}=t,b=w(t,["store","focusable","autoSelect","getAutoSelectId","setValueOnChange","showMinLength","showOnChange","showOnMouseDown","showOnClick","showOnKeyDown","showOnKeyPress","blurActiveItemOnClick","setValueOnClick","moveOnKeyPress","autoComplete"]);const y=At();F(n=n||y,!1);const _=(0,H.useRef)(null),[C,S]=(0,H.useReducer)((()=>[]),[]),j=(0,H.useRef)(!1),I=(0,H.useRef)(!1),E=n.useState((e=>e.virtualFocus&&r)),V="inline"===g||"both"===g,[k,O]=(0,H.useState)(V);!function(e,t){const n=(0,H.useRef)(!1);me((()=>{n.current?V&&O(!0):n.current=!0}),t),me((()=>()=>{n.current=!1}),[])}(0,[V]);const N=n.useState("value"),P=(0,H.useRef)();(0,H.useEffect)((()=>qe(n,["selectedValue","activeId"],((e,t)=>{P.current=t.selectedValue}))),[]);const M=n.useState((e=>{var t;if(V&&k){if(e.activeValue&&Array.isArray(e.selectedValue)){if(e.selectedValue.includes(e.activeValue))return;if(null==(t=P.current)?void 0:t.includes(e.activeValue))return}return e.activeValue}})),L=n.useState("renderedItems"),D=n.useState("open"),T=n.useState("contentElement"),B=(0,H.useMemo)((()=>{if(!V)return N;if(!k)return N;if(rn(L,M,E)){if(sn(N,M)){const e=(null==M?void 0:M.slice(N.length))||"";return N+e}return N}return M||N}),[V,k,L,M,E,N]);(0,H.useEffect)((()=>{const e=_.current;if(!e)return;const t=()=>O(!0);return e.addEventListener("combobox-item-move",t),()=>{e.removeEventListener("combobox-item-move",t)}}),[]),(0,H.useEffect)((()=>{if(!V)return;if(!k)return;if(!M)return;if(!rn(L,M,E))return;if(!sn(N,M))return;let e=A;return queueMicrotask((()=>{const t=_.current;if(!t)return;const{start:n,end:i}=Y(t),r=N.length,s=M.length;ee(t,r,s),e=()=>{if(!Ut(t))return;const{start:e,end:l}=Y(t);e===r&&l===s&&ee(t,n,i)}})),()=>e()}),[C,V,k,M,L,E,N]);const R=(0,H.useRef)(null),z=ve(s),K=(0,H.useRef)(null);(0,H.useEffect)((()=>{if(!D)return;if(!T)return;const e=Q(T);if(!e)return;R.current=e;const t=()=>{j.current=!1},i=()=>{if(!n)return;if(!j.current)return;const{activeId:e}=n.getState();null!==e&&e!==K.current&&(j.current=!1)},r={passive:!0,capture:!0};return e.addEventListener("wheel",t,r),e.addEventListener("touchmove",t,r),e.addEventListener("scroll",i,r),()=>{e.removeEventListener("wheel",t,!0),e.removeEventListener("touchmove",t,!0),e.removeEventListener("scroll",i,!0)}}),[D,T,n]),me((()=>{N&&(I.current||(j.current=!0))}),[N]),me((()=>{"always"!==E&&D||(j.current=D)}),[E,D]);const U=n.useState("resetValueOnSelect");xe((()=>{var e,t;const i=j.current;if(!n)return;if(!D)return;if(!(E&&i||U))return;const{baseElement:r,contentElement:s,activeId:l}=n.getState();if(!r||Ut(r)){if(null==s?void 0:s.hasAttribute("data-placing")){const e=new MutationObserver(S);return e.observe(s,{attributeFilter:["data-placing"]}),()=>e.disconnect()}if(E&&i){const t=z(L),i=void 0!==t?t:null!=(e=function(e){const t=e.find((e=>{var t;return!e.disabled&&"tab"!==(null==(t=e.element)?void 0:t.getAttribute("role"))}));return null==t?void 0:t.id}(L))?e:n.first();K.current=i,n.move(null!=i?i:null)}else{const e=null==(t=n.item(l))?void 0:t.element;e&&"scrollIntoView"in e&&e.scrollIntoView({block:"nearest",inline:"nearest"})}}}),[n,D,C,N,E,U,z,L]),(0,H.useEffect)((()=>{if(!V)return;const e=_.current;if(!e)return;const t=[e,T].filter((e=>!!e)),i=e=>{t.every((t=>oe(e,t)))&&(null==n||n.setValue(B))};for(const e of t)e.addEventListener("focusout",i);return()=>{for(const e of t)e.removeEventListener("focusout",i)}}),[V,T,n,B]);const W=e=>e.currentTarget.value.length>=o,G=b.onChange,$=we(null!=a?a:W),q=we(null!=l?l:!n.tag),Z=ve((e=>{if(null==G||G(e),e.defaultPrevented)return;if(!n)return;const t=e.currentTarget,{value:i,selectionStart:r,selectionEnd:s}=t,l=e.nativeEvent;if(j.current=!0,function(e){return"input"===e.type}(l)&&(l.isComposing&&(j.current=!1,I.current=!0),V)){const e="insertText"===l.inputType||"insertCompositionText"===l.inputType,t=r===i.length;O(e&&t)}if(q(e)){const e=i===n.getState().value;n.setValue(i),queueMicrotask((()=>{ee(t,r,s)})),V&&E&&e&&S()}$(e)&&n.show(),E&&j.current||n.setActiveId(null)})),X=b.onCompositionEnd,te=ve((e=>{j.current=!0,I.current=!1,null==X||X(e),e.defaultPrevented||E&&S()})),ne=b.onMouseDown,ie=we(null!=m?m:()=>!!(null==n?void 0:n.getState().includesBaseElement)),re=we(v),se=we(null!=u?u:W),le=ve((e=>{null==ne||ne(e),e.defaultPrevented||e.button||e.ctrlKey||n&&(ie(e)&&n.setActiveId(null),re(e)&&n.setValue(B),se(e)&&ae(e.currentTarget,"mouseup",n.show))})),ce=b.onKeyDown,ue=we(null!=f?f:W),de=ve((e=>{if(null==ce||ce(e),e.repeat||(j.current=!1),e.defaultPrevented)return;if(e.ctrlKey)return;if(e.altKey)return;if(e.shiftKey)return;if(e.metaKey)return;if(!n)return;const{open:t,activeId:i}=n.getState();t||null===i&&("ArrowUp"!==e.key&&"ArrowDown"!==e.key||ue(e)&&(e.preventDefault(),n.show()))})),fe=b.onBlur,he=ve((e=>{j.current=!1,null==fe||fe(e),e.defaultPrevented})),be=ge(b.id),ye=function(e){return"inline"===e||"list"===e||"both"===e||"none"===e}(g)?g:void 0,_e=n.useState((e=>null===e.activeId));return b=x(h({id:be,role:"combobox","aria-autocomplete":ye,"aria-haspopup":J(T,"listbox"),"aria-expanded":D,"aria-controls":null==T?void 0:T.id,"data-active-item":_e||void 0,value:B},b),{ref:pe(_,b.ref),onChange:Z,onCompositionEnd:te,onMouseDown:le,onKeyDown:de,onBlur:he}),b=nn(x(h({store:n,focusable:i},b),{moveOnKeyPress:e=>!function(e,...t){const n="function"==typeof e?e(...t):e;return null!=n&&!n}(p,e)&&(V&&O(!0),!0)})),b=Tt(h({store:n},b)),h({autoComplete:"off"},b)})),on=Ee((function(e){return ke("input",ln(e))})),an=n(75795);function cn(e,t){const n=setTimeout(t,e);return()=>clearTimeout(n)}function un(...e){return e.join(", ").split(", ").reduce(((e,t)=>{const n=t.endsWith("ms")?1:1e3,i=Number.parseFloat(t||"0s")*n;return i>e?i:e}),0)}function dn(e,t,n){return!(n||!1===t||e&&!t)}var fn=Ae((function(e){var t=e,{store:n,alwaysVisible:i}=t,r=w(t,["store","alwaysVisible"]);const s=xt();F(n=n||s,!1);const l=(0,H.useRef)(null),o=ge(r.id),[a,c]=(0,H.useState)(null),u=n.useState("open"),d=n.useState("mounted"),f=n.useState("animated"),m=n.useState("contentElement"),v=et(n.disclosure,"contentElement");me((()=>{l.current&&(null==n||n.setContentElement(l.current))}),[n]),me((()=>{let e;return null==n||n.setState("animated",(t=>(e=t,!0))),()=>{void 0!==e&&(null==n||n.setState("animated",e))}}),[n]),me((()=>{if(f){if(null==m?void 0:m.isConnected)return function(e){let t=requestAnimationFrame((()=>{t=requestAnimationFrame(e)}));return()=>cancelAnimationFrame(t)}((()=>{c(u?"enter":d?"leave":null)}));c(null)}}),[f,m,u,d]),me((()=>{if(!n)return;if(!f)return;const e=()=>null==n?void 0:n.setState("animating",!1),t=()=>(0,an.flushSync)(e);if(!a||!m)return void e();if("leave"===a&&u)return;if("enter"===a&&!u)return;if("number"==typeof f)return cn(f,t);const{transitionDuration:i,animationDuration:r,transitionDelay:s,animationDelay:l}=getComputedStyle(m),{transitionDuration:o="0",animationDuration:c="0",transitionDelay:d="0",animationDelay:p="0"}=v?getComputedStyle(v):{},g=un(s,l,d,p)+un(i,r,o,c);return g?cn(Math.max(g-1e3/60,0),t):("enter"===a&&n.setState("animated",!1),void e())}),[n,f,m,v,u,a]),r=be(r,(e=>(0,Ie.jsx)(yt,{value:n,children:e})),[n]);const p=dn(d,r.hidden,i),g=r.style,b=(0,H.useMemo)((()=>p?x(h({},g),{display:"none"}):g),[p,g]);return D(r=x(h({id:o,"data-open":u||void 0,"data-enter":"enter"===a||void 0,"data-leave":"leave"===a||void 0,hidden:p},r),{ref:pe(o?n.setContentElement:null,l,r.ref),style:b}))})),mn=Ee((function(e){return ke("div",fn(e))})),vn=(Ee((function(e){var t=e,{unmountOnHide:n}=t,i=w(t,["unmountOnHide"]);const r=xt();return!1===et(i.store||r,(e=>!n||(null==e?void 0:e.mounted)))?null:(0,Ie.jsx)(mn,h({},i))})),Ae((function(e){var t=e,{store:n,alwaysVisible:i}=t,r=w(t,["store","alwaysVisible"]);const s=kt(!0),l=Vt(),o=!!(n=n||l)&&n===s;F(n,!1);const a=(0,H.useRef)(null),c=ge(r.id),u=n.useState("mounted"),d=dn(u,r.hidden,i),f=d?x(h({},r.style),{display:"none"}):r.style,m=n.useState((e=>Array.isArray(e.selectedValue))),v=function(e,t,n){const[i,r]=(0,H.useState)(n);return me((()=>{const n=e&&"current"in e?e.current:e;if(!n)return;const i=()=>{const e=n.getAttribute(t);null!=e&&r(e)},s=new MutationObserver(i);return s.observe(n,{attributeFilter:[t]}),i(),()=>s.disconnect()}),[e,t]),i}(a,"role",r.role),p=("listbox"===v||"tree"===v||"grid"===v)&&m||void 0,[g,b]=(0,H.useState)(!1),y=n.useState("contentElement");me((()=>{if(!u)return;const e=a.current;if(!e)return;if(y!==e)return;const t=()=>{b(!!e.querySelector("[role='listbox']"))},n=new MutationObserver(t);return n.observe(e,{subtree:!0,childList:!0,attributeFilter:["role"]}),t(),()=>n.disconnect()}),[u,y]),g||(r=h({role:"listbox","aria-multiselectable":p},r)),r=be(r,(e=>(0,Ie.jsx)(Nt,{value:n,children:(0,Ie.jsx)(It.Provider,{value:v,children:e})})),[n,v]);const _=!c||s&&o?null:n.setContentElement;return D(r=x(h({id:c,hidden:d},r),{ref:pe(_,a,r.ref),style:f}))}))),pn=Ee((function(e){return ke("div",vn(e))}));function gn(e){const t=e.relatedTarget;return(null==t?void 0:t.nodeType)===Node.ELEMENT_NODE?t:null}var hn=Symbol("composite-hover"),xn=Ae((function(e){var t=e,{store:n,focusOnHover:i=!0,blurOnHoverEnd:r=!!i}=t,s=w(t,["store","focusOnHover","blurOnHoverEnd"]);const l=Fe();F(n=n||l,!1);const o=((0,H.useEffect)((()=>{ce("mousemove",Se,!0),ce("mousedown",je,!0),ce("mouseup",je,!0),ce("keydown",je,!0),ce("scroll",je,!0)}),[]),ve((()=>ye))),a=s.onMouseMove,c=we(i),u=ve((e=>{if(null==a||a(e),!e.defaultPrevented&&o()&&c(e)){if(!Wt(e.currentTarget)){const e=null==n?void 0:n.getState().baseElement;e&&!Ut(e)&&e.focus()}null==n||n.setActiveId(e.currentTarget.id)}})),d=s.onMouseLeave,f=we(r),m=ve((e=>{var t;null==d||d(e),e.defaultPrevented||o()&&(function(e){const t=gn(e);return!!t&&G(e.currentTarget,t)}(e)||function(e){let t=gn(e);if(!t)return!1;do{if(O(t,hn)&&t[hn])return!0;t=t.parentElement}while(t);return!1}(e)||c(e)&&f(e)&&(null==n||n.setActiveId(null),null==(t=null==n?void 0:n.getState().baseElement)||t.focus()))})),v=(0,H.useCallback)((e=>{e&&(e[hn]=!0)}),[]);return D(s=x(h({},s),{ref:pe(v,s.ref),onMouseMove:u,onMouseLeave:m}))})),wn=(Ve(Ee((function(e){return ke("div",xn(e))}))),Ae((function(e){var t=e,{store:n,shouldRegisterItem:i=!0,getItem:r=M,element:s}=t,l=w(t,["store","shouldRegisterItem","getItem","element"]);const o=Pe();n=n||o;const a=ge(l.id),c=(0,H.useRef)(s);return(0,H.useEffect)((()=>{const e=c.current;if(!a)return;if(!e)return;if(!i)return;const t=r({id:a,element:e});return null==n?void 0:n.renderItem(t)}),[a,i,r,n]),D(l=x(h({},l),{ref:pe(c,l.ref)}))})));function bn(e){if(!e.isTrusted)return!1;const t=e.currentTarget;return"Enter"===e.key?$(t)||"SUMMARY"===t.tagName||"A"===t.tagName:" "===e.key&&($(t)||"SUMMARY"===t.tagName||"INPUT"===t.tagName||"SELECT"===t.tagName)}Ee((function(e){return ke("div",wn(e))}));var yn=Symbol("command"),_n=Ae((function(e){var t=e,{clickOnEnter:n=!0,clickOnSpace:i=!0}=t,r=w(t,["clickOnEnter","clickOnSpace"]);const s=(0,H.useRef)(null),l=he(s),o=r.type,[a,c]=(0,H.useState)((()=>!!l&&$({tagName:l,type:o})));(0,H.useEffect)((()=>{s.current&&c($(s.current))}),[]);const[u,d]=(0,H.useState)(!1),f=(0,H.useRef)(!1),m=L(r),[v,p]=function(e,t,n){const i=e.onLoadedMetadataCapture,r=(0,H.useMemo)((()=>Object.assign((()=>{}),x(h({},i),{[t]:n}))),[i,t,n]);return[null==i?void 0:i[t],{onLoadedMetadataCapture:r}]}(r,yn,!0),g=r.onKeyDown,b=ve((e=>{null==g||g(e);const t=e.currentTarget;if(e.defaultPrevented)return;if(v)return;if(m)return;if(!re(e))return;if(Z(t))return;if(t.isContentEditable)return;const r=n&&"Enter"===e.key,s=i&&" "===e.key,l="Enter"===e.key&&!n,o=" "===e.key&&!i;if(l||o)e.preventDefault();else if(r||s){const n=bn(e);if(r){if(!n){e.preventDefault();const n=e,{view:i}=n,r=w(n,["view"]),s=()=>le(t,r);K&&/firefox\//i.test(navigator.userAgent)?ae(t,"keyup",s):queueMicrotask(s)}}else s&&(f.current=!0,n||(e.preventDefault(),d(!0)))}})),y=r.onKeyUp,_=ve((e=>{if(null==y||y(e),e.defaultPrevented)return;if(v)return;if(m)return;if(e.metaKey)return;const t=i&&" "===e.key;if(f.current&&t&&(f.current=!1,!bn(e))){e.preventDefault(),d(!1);const t=e.currentTarget,n=e,{view:i}=n,r=w(n,["view"]);queueMicrotask((()=>le(t,r)))}}));return r=x(h(h({"data-active":u||void 0,type:a?"button":void 0},p),r),{ref:pe(s,r.ref),onKeyDown:b,onKeyUp:_}),en(r)}));function Cn(e,t=!1){const{top:n}=e.getBoundingClientRect();return t?n+e.clientHeight:n}function Sn(e,t,n,i=!1){var r;if(!t)return;if(!n)return;const{renderedItems:s}=t.getState(),l=Q(e);if(!l)return;const o=function(e,t=!1){const n=e.clientHeight,{top:i}=e.getBoundingClientRect(),r=1.5*Math.max(.875*n,n-40),s=t?n-r+i:r+i;return"HTML"===e.tagName?s+e.scrollTop:s}(l,i);let a,c;for(let e=0;e<s.length;e+=1){const s=a;if(a=n(e),!a)break;if(a===s)continue;const l=null==(r=Ht(t,a))?void 0:r.element;if(!l)continue;const u=Cn(l,i)-o,d=Math.abs(u);if(i&&u<=0||!i&&u>=0){void 0!==c&&c<d&&(a=s);break}c=d}return a}Ee((function(e){return ke("button",_n(e))}));var jn=Ae((function(e){var t=e,{store:n,rowId:i,preventScrollOnKeyDown:r=!1,moveOnKeyPress:s=!0,tabbable:l=!1,getItem:o,"aria-setsize":a,"aria-posinset":c}=t,u=w(t,["store","rowId","preventScrollOnKeyDown","moveOnKeyPress","tabbable","getItem","aria-setsize","aria-posinset"]);const d=Fe();n=n||d;const f=ge(u.id),m=(0,H.useRef)(null),v=(0,H.useContext)(Be),p=et(n,(e=>i||(e&&(null==v?void 0:v.baseElement)&&v.baseElement===e.baseElement?v.id:void 0))),g=L(u)&&!u.accessibleWhenDisabled,b=(0,H.useCallback)((e=>{const t=x(h({},e),{id:f||e.id,rowId:p,disabled:!!g});return o?o(t):t}),[f,p,g,o]),y=u.onFocus,_=(0,H.useRef)(!1),C=ve((e=>{if(null==y||y(e),e.defaultPrevented)return;if(ie(e))return;if(!f)return;if(!n)return;if(function(e,t){return!re(e)&&Rt(t,e.target)}(e,n))return;const{virtualFocus:t,baseElement:i}=n.getState();var r;(n.setActiveId(f),X(e.currentTarget)&&function(e,t=!1){if(Z(e))e.setSelectionRange(t?e.value.length:0,e.value.length);else if(e.isContentEditable){const n=U(e).getSelection();null==n||n.selectAllChildren(e),t&&(null==n||n.collapseToEnd())}}(e.currentTarget),t)&&(re(e)&&(X(r=e.currentTarget)||"INPUT"===r.tagName&&!$(r)||(null==i?void 0:i.isConnected)&&(ne()&&e.currentTarget.hasAttribute("data-autofocus")&&e.currentTarget.scrollIntoView({block:"nearest",inline:"nearest"}),_.current=!0,e.relatedTarget===i||Rt(n,e.relatedTarget)?function(e){e[Bt]=!0,e.focus({preventScroll:!0})}(i):i.focus())))})),S=u.onBlurCapture,j=ve((e=>{if(null==S||S(e),e.defaultPrevented)return;const t=null==n?void 0:n.getState();(null==t?void 0:t.virtualFocus)&&_.current&&(_.current=!1,e.preventDefault(),e.stopPropagation())})),I=u.onKeyDown,E=we(r),V=we(s),k=ve((e=>{if(null==I||I(e),e.defaultPrevented)return;if(!re(e))return;if(!n)return;const{currentTarget:t}=e,i=n.getState(),r=n.item(f),s=!!(null==r?void 0:r.rowId),l="horizontal"!==i.orientation,o="vertical"!==i.orientation,a=()=>!(!s&&!o&&i.baseElement&&Z(i.baseElement)),c={ArrowUp:(s||l)&&n.up,ArrowRight:(s||o)&&n.next,ArrowDown:(s||l)&&n.down,ArrowLeft:(s||o)&&n.previous,Home:()=>{if(a())return!s||e.ctrlKey?null==n?void 0:n.first():null==n?void 0:n.previous(-1)},End:()=>{if(a())return!s||e.ctrlKey?null==n?void 0:n.last():null==n?void 0:n.next(-1)},PageUp:()=>Sn(t,n,null==n?void 0:n.up,!0),PageDown:()=>Sn(t,n,null==n?void 0:n.down)}[e.key];if(c){if(X(t)){const n=Y(t),i=o&&"ArrowLeft"===e.key,r=o&&"ArrowRight"===e.key,s=l&&"ArrowUp"===e.key,a=l&&"ArrowDown"===e.key;if(r||a){const{length:e}=function(e){if(Z(e))return e.value;if(e.isContentEditable){const t=U(e).createRange();return t.selectNodeContents(e),t.toString()}return""}(t);if(n.end!==e)return}else if((i||s)&&0!==n.start)return}const i=c();if(E(e)||void 0!==i){if(!V(e))return;e.preventDefault(),n.move(i)}}})),A=et(n,(e=>(null==e?void 0:e.baseElement)||void 0)),O=(0,H.useMemo)((()=>({id:f,baseElement:A})),[f,A]);u=be(u,(e=>(0,Ie.jsx)(He.Provider,{value:O,children:e})),[O]);const N=et(n,(e=>!!e&&e.activeId===f)),P=et(n,(e=>null!=a?a:e&&(null==v?void 0:v.ariaSetSize)&&v.baseElement===e.baseElement?v.ariaSetSize:void 0)),M=et(n,(e=>{if(null!=c)return c;if(!e)return;if(!(null==v?void 0:v.ariaPosInSet))return;if(v.baseElement!==e.baseElement)return;const t=e.renderedItems.filter((e=>e.rowId===p));return v.ariaPosInSet+t.findIndex((e=>e.id===f))})),F=et(n,(e=>!(null==e?void 0:e.renderedItems.length)||!e.virtualFocus&&(!!l||e.activeId===f)));return u=x(h({id:f,"data-active-item":N||void 0},u),{ref:pe(m,u.ref),tabIndex:F?u.tabIndex:-1,onFocus:C,onBlurCapture:j,onKeyDown:k}),u=_n(u),u=wn(x(h({store:n},u),{getItem:b,shouldRegisterItem:!!f&&u.shouldRegisterItem})),D(x(h({},u),{"aria-setsize":P,"aria-posinset":M}))}));function In(e){var t;return null!=(t={menu:"menuitem",listbox:"option",tree:"treeitem"}[e])?t:"option"}Ve(Ee((function(e){return ke("button",jn(e))})));var En=Ae((function(e){var t,n=e,{store:i,value:r,hideOnClick:s,setValueOnClick:l,selectValueOnClick:o=!0,resetValueOnSelect:a,focusOnHover:c=!1,moveOnKeyPress:u=!0,getItem:d}=n,f=w(n,["store","value","hideOnClick","setValueOnClick","selectValueOnClick","resetValueOnSelect","focusOnHover","moveOnKeyPress","getItem"]);const m=kt();F(i=i||m,!1);const v=(0,H.useCallback)((e=>{const t=x(h({},e),{value:r});return d?d(t):t}),[r,d]),p=i.useState((e=>Array.isArray(e.selectedValue))),g=i.useState((e=>function(e,t){if(null!=t)return null!=e&&(Array.isArray(e)?e.includes(t):e===t)}(e.selectedValue,r))),b=i.useState("resetValueOnSelect");l=null!=l?l:!p,s=null!=s?s:null!=r&&!p;const y=f.onClick,_=we(l),C=we(o),S=we(null!=(t=null!=a?a:b)?t:p),j=we(s),I=ve((e=>{null==y||y(e),e.defaultPrevented||function(e){const t=e.currentTarget;if(!t)return!1;const n=t.tagName.toLowerCase();return!!e.altKey&&("a"===n||"button"===n&&"submit"===t.type||"input"===n&&"submit"===t.type)}(e)||function(e){const t=e.currentTarget;if(!t)return!1;const n=te();if(n&&!e.metaKey)return!1;if(!n&&!e.ctrlKey)return!1;const i=t.tagName.toLowerCase();return"a"===i||"button"===i&&"submit"===t.type||"input"===i&&"submit"===t.type}(e)||(null!=r&&(C(e)&&(S(e)&&(null==i||i.resetValue()),null==i||i.setSelectedValue((e=>Array.isArray(e)?e.includes(r)?e.filter((e=>e!==r)):[...e,r]:r))),_(e)&&(null==i||i.setValue(r))),j(e)&&(null==i||i.hide()))})),E=f.onKeyDown,V=ve((e=>{if(null==E||E(e),e.defaultPrevented)return;const t=null==i?void 0:i.getState().baseElement;t&&(Ut(t)||(1===e.key.length||"Backspace"===e.key||"Delete"===e.key)&&(queueMicrotask((()=>t.focus())),Z(t)&&(null==i||i.setValue(t.value))))}));p&&null!=g&&(f=h({"aria-selected":g},f)),f=be(f,(e=>(0,Ie.jsx)(Pt.Provider,{value:r,children:(0,Ie.jsx)(Mt.Provider,{value:null!=g&&g,children:e})})),[r,g]);const k=(0,H.useContext)(It);f=x(h({role:In(k),children:r},f),{onClick:I,onKeyDown:V});const A=we(u);return f=jn(x(h({store:i},f),{getItem:v,moveOnKeyPress:e=>{if(!A(e))return!1;const t=new Event("combobox-item-move"),n=null==i?void 0:i.getState().baseElement;return null==n||n.dispatchEvent(t),!0}})),xn(h({store:i,focusOnHover:c},f))})),Vn=Ve(Ee((function(e){return ke("div",En(e))})));function kn(e){return P(e).toLowerCase()}var An=Ae((function(e){var t=e,{store:n,value:i,userValue:r}=t,s=w(t,["store","value","userValue"]);const l=kt();n=n||l;const o=(0,H.useContext)(Pt),a=null!=i?i:o,c=et(n,(e=>null!=r?r:null==e?void 0:e.value)),u=(0,H.useMemo)((()=>{if(a)return c?function(e,t){if(!e)return e;if(!t)return e;const n=(i=t,Array.isArray(i)?i:void 0!==i?[i]:[]).filter(Boolean).map(kn);var i;const r=[],s=(e,t=!1)=>(0,Ie.jsx)("span",{"data-autocomplete-value":t?"":void 0,"data-user-value":t?void 0:"",children:e},r.length),l=function(e){return e.sort((([e],[t])=>e-t))}(function(e){return e.filter((([e,t],n,i)=>!i.some((([i,r],s)=>s!==n&&i<=e&&i+r>=e+t))))}(function(e,t){const n=[];for(const i of t){let t=0;const r=i.length;for(;-1!==e.indexOf(i,t);){const s=e.indexOf(i,t);-1!==s&&n.push([s,r]),t=s+1}}return n}(kn(e),new Set(n))));if(!l.length)return r.push(s(e,!0)),r;const[o]=l[0],a=[e.slice(0,o),...l.flatMap((([t,n],i)=>{var r;const s=e.slice(t,t+n),o=null==(r=l[i+1])?void 0:r[0];return[s,e.slice(t+n,o)]}))];return a.forEach(((e,t)=>{e&&r.push(s(e,t%2==0))})),r}(a,c):a}),[a,c]);return D(s=h({children:u},s))})),On=Ee((function(e){return ke("span",An(e))})),Nn=n(9303),Pn=n.n(Nn),Mn=n(29491),Fn=n(5573);const Ln=(0,Ie.jsx)(Fn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,Ie.jsx)(Fn.Path,{d:"M16.7 7.1l-6.3 8.5-3.3-2.5-.9 1.2 4.5 3.4L17.9 8z"})}),Dn=(0,Ie.jsx)(Fn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,Ie.jsx)(Fn.Path,{d:"M13 5c-3.3 0-6 2.7-6 6 0 1.4.5 2.7 1.3 3.7l-3.8 3.8 1.1 1.1 3.8-3.8c1 .8 2.3 1.3 3.7 1.3 3.3 0 6-2.7 6-6S16.3 5 13 5zm0 10.5c-2.5 0-4.5-2-4.5-4.5s2-4.5 4.5-4.5 4.5 2 4.5 4.5-2 4.5-4.5 4.5z"})}),Tn=(0,Ie.jsx)(Fn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,Ie.jsx)(Fn.Circle,{cx:12,cy:12,r:3})});function Hn(e=""){return Pn()(e.trim().toLowerCase())}const Bn=[],Rn=(e,t)=>e.singleSelection?t?.value:Array.isArray(t?.value)?t.value:!Array.isArray(t?.value)&&t?.value?[t.value]:Bn,zn=(e,t,n)=>e.singleSelection?n:Array.isArray(t?.value)?t.value.includes(n)?t.value.filter((e=>e!==n)):[...t.value,n]:[n];function Kn(e,t){return`${e}-${t}`}function Un({view:e,filter:t,onChangeView:n}){const s=(0,Mn.useInstanceId)(Un,"dataviews-filter-list-box"),[l,a]=(0,r.useState)(1===t.operators?.length?void 0:null),c=e.filters?.find((e=>e.field===t.field)),u=Rn(t,c);return(0,Ie.jsx)(i.Composite,{virtualFocus:!0,focusLoop:!0,activeId:l,setActiveId:a,role:"listbox",className:"dataviews-filters__search-widget-listbox","aria-label":(0,o.sprintf)((0,o.__)("List of: %1$s"),t.name),onFocusVisible:()=>{!l&&t.elements.length&&a(Kn(s,t.elements[0].value))},render:(0,Ie.jsx)(i.Composite.Typeahead,{}),children:t.elements.map((r=>(0,Ie.jsxs)(i.Composite.Hover,{render:(0,Ie.jsx)(i.Composite.Item,{id:Kn(s,r.value),render:(0,Ie.jsx)("div",{"aria-label":r.label,role:"option",className:"dataviews-filters__search-widget-listitem"}),onClick:()=>{var i,s;const l=c?[...(null!==(i=e.filters)&&void 0!==i?i:[]).map((e=>e.field===t.field?{...e,operator:c.operator||t.operators[0],value:zn(t,c,r.value)}:e))]:[...null!==(s=e.filters)&&void 0!==s?s:[],{field:t.field,operator:t.operators[0],value:zn(t,c,r.value)}];n({...e,page:1,filters:l})}}),children:[(0,Ie.jsxs)("span",{className:"dataviews-filters__search-widget-listitem-check",children:[t.singleSelection&&u===r.value&&(0,Ie.jsx)(i.Icon,{icon:Tn}),!t.singleSelection&&u.includes(r.value)&&(0,Ie.jsx)(i.Icon,{icon:Ln})]}),(0,Ie.jsx)("span",{children:r.label})]},r.value)))})}function Wn({view:e,filter:t,onChangeView:n}){const[s,l]=(0,r.useState)(""),a=(0,r.useDeferredValue)(s),c=e.filters?.find((e=>e.field===t.field)),u=Rn(t,c),d=(0,r.useMemo)((()=>{const e=Hn(a);return t.elements.filter((t=>Hn(t.label).includes(e)))}),[t.elements,a]);return(0,Ie.jsxs)(Ft,{selectedValue:u,setSelectedValue:i=>{var r,s;const l=c?[...(null!==(r=e.filters)&&void 0!==r?r:[]).map((e=>e.field===t.field?{...e,operator:c.operator||t.operators[0],value:i}:e))]:[...null!==(s=e.filters)&&void 0!==s?s:[],{field:t.field,operator:t.operators[0],value:i}];n({...e,page:1,filters:l})},setValue:l,children:[(0,Ie.jsxs)("div",{className:"dataviews-filters__search-widget-filter-combobox__wrapper",children:[(0,Ie.jsx)(Dt,{render:(0,Ie.jsx)(i.VisuallyHidden,{children:(0,o.__)("Search items")}),children:(0,o.__)("Search items")}),(0,Ie.jsx)(on,{autoSelect:"always",placeholder:(0,o.__)("Search"),className:"dataviews-filters__search-widget-filter-combobox__input"}),(0,Ie.jsx)("div",{className:"dataviews-filters__search-widget-filter-combobox__icon",children:(0,Ie.jsx)(i.Icon,{icon:Dn})})]}),(0,Ie.jsxs)(pn,{className:"dataviews-filters__search-widget-filter-combobox-list",alwaysVisible:!0,children:[d.map((e=>(0,Ie.jsxs)(Vn,{resetValueOnSelect:!1,value:e.value,className:"dataviews-filters__search-widget-listitem",hideOnClick:!1,setValueOnClick:!1,focusOnHover:!0,children:[(0,Ie.jsxs)("span",{className:"dataviews-filters__search-widget-listitem-check",children:[t.singleSelection&&u===e.value&&(0,Ie.jsx)(i.Icon,{icon:Tn}),!t.singleSelection&&u.includes(e.value)&&(0,Ie.jsx)(i.Icon,{icon:Ln})]}),(0,Ie.jsxs)("span",{children:[(0,Ie.jsx)(On,{className:"dataviews-filters__search-widget-filter-combobox-item-value",value:e.label}),!!e.description&&(0,Ie.jsx)("span",{className:"dataviews-filters__search-widget-listitem-description",children:e.description})]})]},e.value))),!d.length&&(0,Ie.jsx)("p",{children:(0,o.__)("No results found")})]})]})}function Gn(e){const t=e.filter.elements.length>10?Wn:Un;return(0,Ie.jsx)(t,{...e})}var $n=n(36655);const qn="Enter",Zn=" ",Xn=({activeElements:e,filterInView:t,filter:n})=>{if(void 0===e||0===e.length)return n.name;const i={Name:(0,Ie.jsx)("span",{className:"dataviews-filters__summary-filter-text-name"}),Value:(0,Ie.jsx)("span",{className:"dataviews-filters__summary-filter-text-value"})};return t?.operator===$n.ld?(0,r.createInterpolateElement)((0,o.sprintf)((0,o.__)("<Name>%1$s is any: </Name><Value>%2$s</Value>"),n.name,e.map((e=>e.label)).join(", ")),i):t?.operator===$n.Vw?(0,r.createInterpolateElement)((0,o.sprintf)((0,o.__)("<Name>%1$s is none: </Name><Value>%2$s</Value>"),n.name,e.map((e=>e.label)).join(", ")),i):t?.operator===$n.y5?(0,r.createInterpolateElement)((0,o.sprintf)((0,o.__)("<Name>%1$s is all: </Name><Value>%2$s</Value>"),n.name,e.map((e=>e.label)).join(", ")),i):t?.operator===$n.UX?(0,r.createInterpolateElement)((0,o.sprintf)((0,o.__)("<Name>%1$s is not all: </Name><Value>%2$s</Value>"),n.name,e.map((e=>e.label)).join(", ")),i):t?.operator===$n.gm?(0,r.createInterpolateElement)((0,o.sprintf)((0,o.__)("<Name>%1$s is: </Name><Value>%2$s</Value>"),n.name,e[0].label),i):t?.operator===$n._k?(0,r.createInterpolateElement)((0,o.sprintf)((0,o.__)("<Name>%1$s is not: </Name><Value>%2$s</Value>"),n.name,e[0].label),i):(0,o.sprintf)((0,o.__)("Unknown status for %1$s"),n.name)};function Yn({filter:e,view:t,onChangeView:n}){const r=e.operators?.map((e=>({value:e,label:$n.we[e]?.label}))),s=t.filters?.find((t=>t.field===e.field)),l=s?.operator||e.operators[0];return r.length>1&&(0,Ie.jsxs)(i.__experimentalHStack,{spacing:2,justify:"flex-start",className:"dataviews-filters__summary-operators-container",children:[(0,Ie.jsx)(i.FlexItem,{className:"dataviews-filters__summary-operators-filter-name",children:e.name}),(0,Ie.jsx)(i.SelectControl,{label:(0,o.__)("Conditions"),value:l,options:r,onChange:i=>{var r,l;const o=i,a=s?[...(null!==(r=t.filters)&&void 0!==r?r:[]).map((t=>t.field===e.field?{...t,operator:o}:t))]:[...null!==(l=t.filters)&&void 0!==l?l:[],{field:e.field,operator:o,value:void 0}];n({...t,page:1,filters:a})},size:"small",__nextHasNoMarginBottom:!0,hideLabelFromVision:!0})]})}function Jn({addFilterRef:e,openedFilter:t,...n}){const s=(0,r.useRef)(null),{filter:l,view:u,onChangeView:d}=n,f=u.filters?.find((e=>e.field===l.field)),m=l.elements.filter((e=>l.singleSelection?e.value===f?.value:f?.value?.includes(e.value))),v=l.isPrimary,p=void 0!==f?.value,g=!v||p;return(0,Ie.jsx)(i.Dropdown,{defaultOpen:t===l.field,contentClassName:"dataviews-filters__summary-popover",popoverProps:{placement:"bottom-start",role:"dialog"},onClose:()=>{s.current?.focus()},renderToggle:({isOpen:t,onToggle:n})=>(0,Ie.jsxs)("div",{className:"dataviews-filters__summary-chip-container",children:[(0,Ie.jsx)(i.Tooltip,{text:(0,o.sprintf)((0,o.__)("Filter by: %1$s"),l.name.toLowerCase()),placement:"top",children:(0,Ie.jsx)("div",{className:(0,a.A)("dataviews-filters__summary-chip",{"has-reset":g,"has-values":p}),role:"button",tabIndex:0,onClick:n,onKeyDown:e=>{[qn,Zn].includes(e.key)&&(n(),e.preventDefault())},"aria-pressed":t,"aria-expanded":t,ref:s,children:(0,Ie.jsx)(Xn,{activeElements:m,filterInView:f,filter:l})})}),g&&(0,Ie.jsx)(i.Tooltip,{text:v?(0,o.__)("Reset"):(0,o.__)("Remove"),placement:"top",children:(0,Ie.jsx)("button",{className:(0,a.A)("dataviews-filters__summary-chip-remove",{"has-values":p}),onClick:()=>{d({...u,page:1,filters:u.filters?.filter((e=>e.field!==l.field))}),v?s.current?.focus():e.current?.focus()},children:(0,Ie.jsx)(i.Icon,{icon:c.A})})})]}),renderContent:()=>(0,Ie.jsxs)(i.__experimentalVStack,{spacing:0,justify:"flex-start",children:[(0,Ie.jsx)(Yn,{...n}),(0,Ie.jsx)(Gn,{...n})]})})}var Qn=n(28509);const{DropdownMenuV2:ei}=(0,Qn.T)(i.privateApis);function ti({filters:e,view:t,onChangeView:n,setOpenedFilter:i,trigger:r}){const s=e.filter((e=>!e.isVisible));return(0,Ie.jsx)(ei,{trigger:r,children:s.map((e=>(0,Ie.jsx)(ei.Item,{onClick:()=>{i(e.field),n({...t,page:1,filters:[...t.filters||[],{field:e.field,value:void 0,operator:e.operators[0]}]})},children:(0,Ie.jsx)(ei.ItemLabel,{children:e.name})},e.field)))})}const ni=(0,r.forwardRef)((function({filters:e,view:t,onChangeView:n,setOpenedFilter:r},s){if(!e.length||e.every((({isPrimary:e})=>e)))return null;const l=e.filter((e=>!e.isVisible));return(0,Ie.jsx)(ti,{trigger:(0,Ie.jsx)(i.Button,{accessibleWhenDisabled:!0,size:"compact",className:"dataviews-filters-button",variant:"tertiary",disabled:!l.length,ref:s,children:(0,o.__)("Add filter")}),filters:e,view:t,onChangeView:n,setOpenedFilter:r})}));function ii({filters:e,view:t,onChangeView:n}){const r=!t.search&&!t.filters?.some((t=>{return void 0!==t.value||(n=t.field,!e.some((e=>e.field===n&&e.isPrimary)));var n}));return(0,Ie.jsx)(i.Button,{disabled:r,accessibleWhenDisabled:!0,size:"compact",variant:"tertiary",className:"dataviews-filters__reset-button",onClick:()=>{n({...t,page:1,search:"",filters:[]})},children:(0,o.__)("Reset")})}var ri=n(37870);function si(e,t){return(0,r.useMemo)((()=>{const n=[];return e.forEach((e=>{if(!e.elements?.length)return;const i=(0,ri.Z)(e);if(0===i.length)return;const r=!!e.filterBy?.isPrimary;n.push({field:e.id,name:e.label,elements:e.elements,singleSelection:i.some((e=>[$n.gm,$n._k].includes(e))),operators:i,isVisible:r||!!t.filters?.some((t=>t.field===e.id&&$n.CD.includes(t.operator))),isPrimary:r})})),n.sort(((e,t)=>e.isPrimary&&!t.isPrimary?-1:!e.isPrimary&&t.isPrimary?1:e.name.localeCompare(t.name))),n}),[e,t])}function li({filters:e,view:t,onChangeView:n,setOpenedFilter:s,isShowingFilter:a,setIsShowingFilter:c}){const u=(0,r.useCallback)((e=>{n(e),c(!0)}),[n,c]),d=!!e.filter((e=>e.isVisible)).length;return 0===e.length?null:d?(0,Ie.jsxs)("div",{className:"dataviews-filters__container-visibility-toggle",children:[(0,Ie.jsx)(i.Button,{className:"dataviews-filters__visibility-toggle",size:"compact",icon:l.A,label:(0,o.__)("Toggle filter display"),onClick:()=>{a||s(null),c(!a)},isPressed:a,"aria-expanded":a}),d&&!!t.filters?.length&&(0,Ie.jsx)("span",{className:"dataviews-filters-toggle__count",children:t.filters?.length})]}):(0,Ie.jsx)(ti,{filters:e,view:t,onChangeView:u,setOpenedFilter:s,trigger:(0,Ie.jsx)(i.Button,{className:"dataviews-filters__visibility-toggle",size:"compact",icon:l.A,label:(0,o.__)("Add filter"),isPressed:!1,"aria-expanded":!1})})}const oi=(0,r.memo)((function(){const{fields:e,view:t,onChangeView:n,openedFilter:l,setOpenedFilter:o}=(0,r.useContext)(s.A),a=(0,r.useRef)(null),c=si(e,t),u=(0,Ie.jsx)(ni,{filters:c,view:t,onChangeView:n,ref:a,setOpenedFilter:o},"add-filter"),d=c.filter((e=>e.isVisible));if(0===d.length)return null;const f=[...d.map((e=>(0,Ie.jsx)(Jn,{filter:e,view:t,onChangeView:n,addFilterRef:a,openedFilter:l},e.field))),u];return f.push((0,Ie.jsx)(ii,{filters:c,view:t,onChangeView:n},"reset-filters")),(0,Ie.jsx)(i.__experimentalHStack,{justify:"flex-start",style:{width:"fit-content"},className:"dataviews-filters__container",wrap:!0,children:f})}));var ai=n(45197);function ci(){const{actions:e=[],data:t,fields:n,getItemId:i,isLoading:l,view:o,onChangeView:a,selection:c,onChangeSelection:u,setOpenedFilter:d,density:f}=(0,r.useContext)(s.A),m=ai.Ad.find((e=>e.type===o.type))?.component;return(0,Ie.jsx)(m,{actions:e,data:t,fields:n,getItemId:i,isLoading:l,onChangeView:a,onChangeSelection:u,selection:c,setOpenedFilter:d,view:o,density:f})}const ui=(0,Ie.jsx)(Fn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,Ie.jsx)(Fn.Path,{d:"M6.6 6L5.4 7l4.5 5-4.5 5 1.1 1 5.5-6-5.4-6zm6 0l-1.1 1 4.5 5-4.5 5 1.1 1 5.5-6-5.5-6z"})}),di=(0,Ie.jsx)(Fn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,Ie.jsx)(Fn.Path,{d:"M11.6 7l-1.1-1L5 12l5.5 6 1.1-1L7 12l4.6-5zm6 0l-1.1-1-5.5 6 5.5 6 1.1-1-4.6-5 4.6-5z"})}),fi=(0,r.memo)((function(){var e;const{view:t,onChangeView:n,paginationInfo:{totalItems:l=0,totalPages:a}}=(0,r.useContext)(s.A);if(!l||!a)return null;const c=null!==(e=t.page)&&void 0!==e?e:1,u=Array.from(Array(a)).map(((e,t)=>{const n=t+1;return{value:n.toString(),label:n.toString(),"aria-label":c===n?(0,o.sprintf)((0,o.__)("Page %1$s of %2$s"),c,a):n.toString()}}));return!!l&&1!==a&&(0,Ie.jsxs)(i.__experimentalHStack,{expanded:!1,className:"dataviews-pagination",justify:"end",spacing:6,children:[(0,Ie.jsx)(i.__experimentalHStack,{justify:"flex-start",expanded:!1,spacing:1,className:"dataviews-pagination__page-select",children:(0,r.createInterpolateElement)((0,o.sprintf)((0,o._x)("<div>Page</div>%1$s<div>of %2$s</div>","paging"),"<CurrentPage />",a),{div:(0,Ie.jsx)("div",{"aria-hidden":!0}),CurrentPage:(0,Ie.jsx)(i.SelectControl,{"aria-label":(0,o.__)("Current page"),value:c.toString(),options:u,onChange:e=>{n({...t,page:+e})},size:"small",__nextHasNoMarginBottom:!0,variant:"minimal"})})}),(0,Ie.jsxs)(i.__experimentalHStack,{expanded:!1,spacing:1,children:[(0,Ie.jsx)(i.Button,{onClick:()=>n({...t,page:c-1}),disabled:1===c,accessibleWhenDisabled:!0,label:(0,o.__)("Previous page"),icon:(0,o.isRTL)()?ui:di,showTooltip:!0,size:"compact",tooltipPosition:"top"}),(0,Ie.jsx)(i.Button,{onClick:()=>n({...t,page:c+1}),disabled:c>=a,accessibleWhenDisabled:!0,label:(0,o.__)("Next page"),icon:(0,o.isRTL)()?di:ui,showTooltip:!0,size:"compact",tooltipPosition:"top"})]})]})}));var mi=n(26882);const vi=[];function pi(){const{view:e,paginationInfo:{totalItems:t=0,totalPages:n},data:l,actions:o=vi}=(0,r.useContext)(s.A),a=(0,mi.fx)(o,l)&&[$n.Ad,$n.Ul].includes(e.type);return!t||!n||n<=1&&!a?null:!!t&&(0,Ie.jsxs)(i.__experimentalHStack,{expanded:!1,justify:"end",className:"dataviews-footer",children:[a&&(0,Ie.jsx)(mi.lF,{}),(0,Ie.jsx)(fi,{})]})}const gi=(0,r.memo)((function({label:e}){const{view:t,onChangeView:n}=(0,r.useContext)(s.A),[l,a,c]=(0,Mn.useDebouncedInput)(t.search);(0,r.useEffect)((()=>{var e;a(null!==(e=t.search)&&void 0!==e?e:"")}),[t.search,a]);const u=(0,r.useRef)(n),d=(0,r.useRef)(t);(0,r.useEffect)((()=>{u.current=n,d.current=t}),[n,t]),(0,r.useEffect)((()=>{c!==d.current?.search&&u.current({...d.current,page:1,search:c})}),[c]);const f=e||(0,o.__)("Search");return(0,Ie.jsx)(i.SearchControl,{className:"dataviews-search",__nextHasNoMarginBottom:!0,onChange:a,value:l,label:f,placeholder:f,size:"compact"})})),hi=(0,Ie.jsx)(Fn.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,Ie.jsx)(Fn.Path,{d:"M6.5 12.4L12 8l5.5 4.4-.9 1.2L12 10l-4.5 3.6-1-1.2z"})}),xi=(0,Ie.jsx)(Fn.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,Ie.jsx)(Fn.Path,{d:"M17.5 11.6L12 16l-5.5-4.4.9-1.2L12 14l4.5-3.6 1 1.2z"})}),wi=(0,Ie.jsx)(Fn.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,Ie.jsx)(Fn.Path,{d:"M3.99961 13C4.67043 13.3354 4.6703 13.3357 4.67017 13.3359L4.67298 13.3305C4.67621 13.3242 4.68184 13.3135 4.68988 13.2985C4.70595 13.2686 4.7316 13.2218 4.76695 13.1608C4.8377 13.0385 4.94692 12.8592 5.09541 12.6419C5.39312 12.2062 5.84436 11.624 6.45435 11.0431C7.67308 9.88241 9.49719 8.75 11.9996 8.75C14.502 8.75 16.3261 9.88241 17.5449 11.0431C18.1549 11.624 18.6061 12.2062 18.9038 12.6419C19.0523 12.8592 19.1615 13.0385 19.2323 13.1608C19.2676 13.2218 19.2933 13.2686 19.3093 13.2985C19.3174 13.3135 19.323 13.3242 19.3262 13.3305L19.3291 13.3359C19.3289 13.3357 19.3288 13.3354 19.9996 13C20.6704 12.6646 20.6703 12.6643 20.6701 12.664L20.6697 12.6632L20.6688 12.6614L20.6662 12.6563L20.6583 12.6408C20.6517 12.6282 20.6427 12.6108 20.631 12.5892C20.6078 12.5459 20.5744 12.4852 20.5306 12.4096C20.4432 12.2584 20.3141 12.0471 20.1423 11.7956C19.7994 11.2938 19.2819 10.626 18.5794 9.9569C17.1731 8.61759 14.9972 7.25 11.9996 7.25C9.00203 7.25 6.82614 8.61759 5.41987 9.9569C4.71736 10.626 4.19984 11.2938 3.85694 11.7956C3.68511 12.0471 3.55605 12.2584 3.4686 12.4096C3.42484 12.4852 3.39142 12.5459 3.36818 12.5892C3.35656 12.6108 3.34748 12.6282 3.34092 12.6408L3.33297 12.6563L3.33041 12.6614L3.32948 12.6632L3.32911 12.664C3.32894 12.6643 3.32879 12.6646 3.99961 13ZM11.9996 16C13.9326 16 15.4996 14.433 15.4996 12.5C15.4996 10.567 13.9326 9 11.9996 9C10.0666 9 8.49961 10.567 8.49961 12.5C8.49961 14.433 10.0666 16 11.9996 16Z"})});var bi=n(96601);const yi=(0,Ie.jsx)(Fn.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,Ie.jsx)(Fn.Path,{fillRule:"evenodd",d:"M10.289 4.836A1 1 0 0111.275 4h1.306a1 1 0 01.987.836l.244 1.466c.787.26 1.503.679 2.108 1.218l1.393-.522a1 1 0 011.216.437l.653 1.13a1 1 0 01-.23 1.273l-1.148.944a6.025 6.025 0 010 2.435l1.149.946a1 1 0 01.23 1.272l-.653 1.13a1 1 0 01-1.216.437l-1.394-.522c-.605.54-1.32.958-2.108 1.218l-.244 1.466a1 1 0 01-.987.836h-1.306a1 1 0 01-.986-.836l-.244-1.466a5.995 5.995 0 01-2.108-1.218l-1.394.522a1 1 0 01-1.217-.436l-.653-1.131a1 1 0 01.23-1.272l1.149-.946a6.026 6.026 0 010-2.435l-1.148-.944a1 1 0 01-.23-1.272l.653-1.131a1 1 0 011.217-.437l1.393.522a5.994 5.994 0 012.108-1.218l.244-1.466zM14.929 12a3 3 0 11-6 0 3 3 0 016 0z",clipRule:"evenodd"})});var _i=n(10979),Ci=n.n(_i);const Si={xhuge:{min:3,max:6,default:5},huge:{min:2,max:4,default:4},xlarge:{min:2,max:3,default:3},large:{min:1,max:2,default:2},mobile:{min:1,max:2,default:2}};function ji({density:e,setDensity:t}){const n=function(){const e=(0,Mn.useViewportMatch)("xhuge",">="),t=(0,Mn.useViewportMatch)("huge",">="),n=(0,Mn.useViewportMatch)("xlarge",">="),i=(0,Mn.useViewportMatch)("large",">="),r=(0,Mn.useViewportMatch)("mobile",">=");return e?"xhuge":t?"huge":n?"xlarge":i?"large":r?"mobile":null}();(0,r.useEffect)((()=>{t((e=>{if(!n||!e)return 0;const t=Si[n];return e<t.min?t.min:e>t.max?t.max:e}))}),[t,n]);const s=Si[n||"mobile"],l=e||s.default,a=(0,r.useMemo)((()=>Array.from({length:s.max-s.min+1},((e,t)=>({value:s.min+t})))),[s]);return n?(0,Ie.jsx)(i.RangeControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,showTooltip:!1,label:(0,o.__)("Preview size"),value:s.max+s.min-l,marks:a,min:s.min,max:s.max,withInputField:!1,onChange:(e=0)=>{t(s.max+s.min-e)},step:1}):null}const{DropdownMenuV2:Ii}=(0,Qn.T)(i.privateApis);function Ei({defaultLayouts:e={list:{},grid:{},table:{}}}){const{view:t,onChangeView:n}=(0,r.useContext)(s.A),l=Object.keys(e);if(l.length<=1)return null;const a=ai.Ad.find((e=>t.type===e.type));return(0,Ie.jsx)(Ii,{trigger:(0,Ie.jsx)(i.Button,{size:"compact",icon:a?.icon,label:(0,o.__)("Layout")}),children:l.map((i=>{const r=ai.Ad.find((e=>e.type===i));return r?(0,Ie.jsx)(Ii.RadioItem,{value:i,name:"view-actions-available-view",checked:i===t.type,hideOnClick:!0,onChange:i=>{switch(i.target.value){case"list":case"grid":case"table":return n({...t,type:i.target.value,...e[i.target.value]})}!0===globalThis.SCRIPT_DEBUG&&Ci()("Invalid dataview")},children:(0,Ie.jsx)(Ii.ItemLabel,{children:r.label})},i):null}))})}function Vi(){const{view:e,fields:t,onChangeView:n}=(0,r.useContext)(s.A),l=(0,r.useMemo)((()=>t.filter((e=>!1!==e.enableSorting)).map((e=>({label:e.label,value:e.id})))),[t]);return(0,Ie.jsx)(i.SelectControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,label:(0,o.__)("Sort by"),value:e.sort?.field,options:l,onChange:t=>{n({...e,sort:{direction:e?.sort?.direction||"desc",field:t}})}})}function ki(){const{view:e,fields:t,onChangeView:n}=(0,r.useContext)(s.A);if(0===t.filter((e=>!1!==e.enableSorting)).length)return null;let l=e.sort?.direction;return!l&&e.sort?.field&&(l="desc"),(0,Ie.jsx)(i.__experimentalToggleGroupControl,{className:"dataviews-view-config__sort-direction",__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,isBlock:!0,label:(0,o.__)("Order"),value:l,onChange:i=>{"asc"!==i&&"desc"!==i?!0===globalThis.SCRIPT_DEBUG&&Ci()("Invalid direction"):n({...e,sort:{direction:i,field:e.sort?.field||t.find((e=>!1!==e.enableSorting))?.id||""}})},children:$n.GJ.map((e=>(0,Ie.jsx)(i.__experimentalToggleGroupControlOptionIcon,{value:e,icon:$n.ls[e],label:$n.CL[e]},e)))})}const Ai=[10,20,50,100];function Oi(){const{view:e,onChangeView:t}=(0,r.useContext)(s.A);return(0,Ie.jsx)(i.__experimentalToggleGroupControl,{__nextHasNoMarginBottom:!0,__next40pxDefaultSize:!0,isBlock:!0,label:(0,o.__)("Items per page"),value:e.perPage||10,disabled:!e?.sort?.field,onChange:n=>{const i="number"==typeof n||void 0===n?n:parseInt(n,10);t({...e,perPage:i,page:1})},children:Ai.map((e=>(0,Ie.jsx)(i.__experimentalToggleGroupControlOption,{value:e,label:e.toString()},e)))})}function Ni({field:{id:e,label:t,index:n,isVisible:r,isHidable:s},fields:l,view:a,onChangeView:c}){const u=(0,ai.Zm)(a,l);return(0,Ie.jsx)(i.__experimentalItem,{children:(0,Ie.jsxs)(i.__experimentalHStack,{expanded:!0,className:`dataviews-field-control__field dataviews-field-control__field-${e}`,children:[(0,Ie.jsx)("span",{children:t}),(0,Ie.jsxs)(i.__experimentalHStack,{justify:"flex-end",expanded:!1,className:"dataviews-field-control__actions",children:[a.type===$n.Ad&&r&&(0,Ie.jsxs)(Ie.Fragment,{children:[(0,Ie.jsx)(i.Button,{disabled:n<1,accessibleWhenDisabled:!0,size:"compact",onClick:()=>{var t;c({...a,fields:[...null!==(t=u.slice(0,n-1))&&void 0!==t?t:[],e,u[n-1],...u.slice(n+1)]})},icon:hi,label:(0,o.sprintf)((0,o.__)("Move %s up"),t)}),(0,Ie.jsx)(i.Button,{disabled:n>=u.length-1,accessibleWhenDisabled:!0,size:"compact",onClick:()=>{var t;c({...a,fields:[...null!==(t=u.slice(0,n))&&void 0!==t?t:[],u[n+1],e,...u.slice(n+2)]})},icon:xi,label:(0,o.sprintf)((0,o.__)("Move %s down"),t)})," "]}),(0,Ie.jsx)(i.Button,{className:"dataviews-field-control__field-visibility-button",disabled:!s,accessibleWhenDisabled:!0,size:"compact",onClick:()=>{c({...a,fields:r?u.filter((t=>t!==e)):[...u,e]}),setTimeout((()=>{const t=document.querySelector(`.dataviews-field-control__field-${e} .dataviews-field-control__field-visibility-button`);t instanceof HTMLElement&&t.focus()}),50)},icon:r?wi:bi.A,label:r?(0,o.sprintf)((0,o.__)("Hide %s"),t):(0,o.sprintf)((0,o.__)("Show %s"),t)})]})]})},e)}function Pi(){const{view:e,fields:t,onChangeView:n}=(0,r.useContext)(s.A),l=(0,r.useMemo)((()=>(0,ai.Zm)(e,t)),[e,t]),a=(0,r.useMemo)((()=>(0,ai.LZ)(e,t)),[e,t]),c=(0,r.useMemo)((()=>(0,ai.PO)(e)),[e]),u=t.filter((({id:e})=>l.includes(e))).map((({id:e,label:t,enableHiding:n})=>({id:e,label:t,index:l.indexOf(e),isVisible:!0,isHidable:!c.includes(e)&&n})));e.type===$n.Ad&&e.layout?.combinedFields&&e.layout.combinedFields.forEach((({id:e,label:t})=>{u.push({id:e,label:t,index:l.indexOf(e),isVisible:!0,isHidable:c.includes(e)})})),u.sort(((e,t)=>e.index-t.index));const d=t.filter((({id:e})=>a.includes(e))).map((({id:e,label:t,enableHiding:n},i)=>({id:e,label:t,index:i,isVisible:!1,isHidable:n})));return u?.length||d?.length?(0,Ie.jsxs)(i.__experimentalVStack,{spacing:6,className:"dataviews-field-control",children:[!!u?.length&&(0,Ie.jsx)(i.__experimentalItemGroup,{isBordered:!0,isSeparated:!0,children:u.map((i=>(0,Ie.jsx)(Ni,{field:i,fields:t,view:e,onChangeView:n},i.id)))}),!!d?.length&&(0,Ie.jsx)(Ie.Fragment,{children:(0,Ie.jsxs)(i.__experimentalVStack,{spacing:4,children:[(0,Ie.jsx)(i.BaseControl.VisualLabel,{style:{margin:0},children:(0,o.__)("Hidden")}),(0,Ie.jsx)(i.__experimentalItemGroup,{isBordered:!0,isSeparated:!0,children:d.map((i=>(0,Ie.jsx)(Ni,{field:i,fields:t,view:e,onChangeView:n},i.id)))})]})})]}):null}function Mi({title:e,description:t,children:n}){return(0,Ie.jsxs)(i.__experimentalGrid,{columns:12,className:"dataviews-settings-section",gap:4,children:[(0,Ie.jsxs)("div",{className:"dataviews-settings-section__sidebar",children:[(0,Ie.jsx)(i.__experimentalHeading,{level:2,className:"dataviews-settings-section__title",children:e}),t&&(0,Ie.jsx)(i.__experimentalText,{variant:"muted",className:"dataviews-settings-section__description",children:t})]}),(0,Ie.jsx)(i.__experimentalGrid,{columns:8,gap:4,className:"dataviews-settings-section__content",children:n})]})}function Fi({density:e,setDensity:t}){const{view:n}=(0,r.useContext)(s.A);return(0,Ie.jsxs)(i.__experimentalVStack,{className:"dataviews-view-config",spacing:6,children:[(0,Ie.jsxs)(Mi,{title:(0,o.__)("Appearance"),children:[(0,Ie.jsxs)(i.__experimentalHStack,{expanded:!0,className:"is-divided-in-two",children:[(0,Ie.jsx)(Vi,{}),(0,Ie.jsx)(ki,{})]}),n.type===$n.Ul&&(0,Ie.jsx)(ji,{density:e,setDensity:t}),(0,Ie.jsx)(Oi,{})]}),(0,Ie.jsx)(Mi,{title:(0,o.__)("Properties"),children:(0,Ie.jsx)(Pi,{})})]})}const Li=(0,r.memo)((function({density:e,setDensity:t,defaultLayouts:n={list:{},grid:{},table:{}}}){return(0,Ie.jsxs)(Ie.Fragment,{children:[(0,Ie.jsx)(Ei,{defaultLayouts:n}),(0,Ie.jsx)(i.Dropdown,{popoverProps:{placement:"bottom-end",offset:9},contentClassName:"dataviews-view-config",renderToggle:({onToggle:e})=>(0,Ie.jsx)(i.Button,{size:"compact",icon:yi,label:(0,o._x)("View options","View is used as a noun"),onClick:e}),renderContent:()=>(0,Ie.jsx)(Fi,{density:e,setDensity:t})})]})}));var Di=n(34183);const Ti=e=>e.id;function Hi({view:e,onChangeView:t,fields:n,search:l=!0,searchLabel:o,actions:a=[],data:c,getItemId:u=Ti,isLoading:d=!1,paginationInfo:f,defaultLayouts:m,selection:v,onChangeSelection:p,header:g}){const[h,x]=(0,r.useState)([]),[w,b]=(0,r.useState)(0),y=void 0===v||void 0===p,_=y?h:v,[C,S]=(0,r.useState)(null),j=(0,r.useMemo)((()=>(0,Di.t)(n)),[n]),I=(0,r.useMemo)((()=>_.filter((e=>c.some((t=>u(t)===e))))),[_,c,u]),E=si(j,e),[V,k]=(0,r.useState)((()=>(E||[]).some((e=>e.isPrimary))));return(0,Ie.jsx)(s.A.Provider,{value:{view:e,onChangeView:t,fields:j,actions:a,data:c,isLoading:d,paginationInfo:f,selection:I,onChangeSelection:function(e){const t="function"==typeof e?e(_):e;y&&x(t),p&&p(t)},openedFilter:C,setOpenedFilter:S,getItemId:u,density:w},children:(0,Ie.jsxs)("div",{className:"dataviews-wrapper",children:[(0,Ie.jsxs)(i.__experimentalHStack,{alignment:"top",justify:"space-between",className:"dataviews__view-actions",spacing:1,children:[(0,Ie.jsxs)(i.__experimentalHStack,{justify:"start",expanded:!1,className:"dataviews__search",children:[l&&(0,Ie.jsx)(gi,{label:o}),(0,Ie.jsx)(li,{filters:E,view:e,onChangeView:t,setOpenedFilter:S,setIsShowingFilter:k,isShowingFilter:V})]}),(0,Ie.jsxs)(i.__experimentalHStack,{spacing:1,expanded:!1,style:{flexShrink:0},children:[(0,Ie.jsx)(Li,{defaultLayouts:m,density:w,setDensity:b}),g]})]}),V&&(0,Ie.jsx)(oi,{}),(0,Ie.jsx)(ci,{}),(0,Ie.jsx)(pi,{})]})})}},36655:(e,t,n)=>{"use strict";n.d(t,{CD:()=>v,Ul:()=>_,mA:()=>C,Ad:()=>y,we:()=>p,gm:()=>a,y5:()=>f,ld:()=>u,Vw:()=>d,_k:()=>c,UX:()=>m,GJ:()=>g,vI:()=>h,ls:()=>b,CL:()=>w,LW:()=>x});var i=n(27723),r=n(5573),s=n(39793);const l=(0,s.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(r.Path,{d:"M12 3.9 6.5 9.5l1 1 3.8-3.7V20h1.5V6.8l3.7 3.7 1-1z"})}),o=(0,s.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(r.Path,{d:"m16.5 13.5-3.7 3.7V4h-1.5v13.2l-3.8-3.7-1 1 5.5 5.6 5.5-5.6z"})}),a="is",c="isNot",u="isAny",d="isNone",f="isAll",m="isNotAll",v=[a,c,u,d,f,m],p={[a]:{key:"is-filter",label:(0,i.__)("Is")},[c]:{key:"is-not-filter",label:(0,i.__)("Is not")},[u]:{key:"is-any-filter",label:(0,i.__)("Is any")},[d]:{key:"is-none-filter",label:(0,i.__)("Is none")},[f]:{key:"is-all-filter",label:(0,i.__)("Is all")},[m]:{key:"is-not-all-filter",label:(0,i.__)("Is not all")}},g=["asc","desc"],h={asc:"↑",desc:"↓"},x={asc:"ascending",desc:"descending"},w={asc:(0,i.__)("Sort ascending"),desc:(0,i.__)("Sort descending")},b={asc:l,desc:o},y="table",_="grid",C="list"},45197:(e,t,n)=>{"use strict";n.d(t,{Ad:()=>H,LZ:()=>K,PO:()=>B,Zm:()=>z});var i=n(27723),r=n(5573),s=n(39793);const l=(0,s.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)(r.Path,{d:"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM5 4.5h14c.3 0 .5.2.5.5v3.5h-15V5c0-.3.2-.5.5-.5zm8 5.5h6.5v3.5H13V10zm-1.5 3.5h-7V10h7v3.5zm-7 5.5v-4h7v4.5H5c-.3 0-.5-.2-.5-.5zm14.5.5h-6V15h6.5v4c0 .3-.2.5-.5.5z"})}),o=(0,s.jsx)(r.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,s.jsx)(r.Path,{d:"M6 5.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5H6a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM4 6a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2H6a2 2 0 01-2-2V6zm11-.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5h-3a.5.5 0 01-.5-.5V6a.5.5 0 01.5-.5zM13 6a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2h-3a2 2 0 01-2-2V6zm5 8.5h-3a.5.5 0 00-.5.5v3a.5.5 0 00.5.5h3a.5.5 0 00.5-.5v-3a.5.5 0 00-.5-.5zM15 13a2 2 0 00-2 2v3a2 2 0 002 2h3a2 2 0 002-2v-3a2 2 0 00-2-2h-3zm-9 1.5h3a.5.5 0 01.5.5v3a.5.5 0 01-.5.5H6a.5.5 0 01-.5-.5v-3a.5.5 0 01.5-.5zM4 15a2 2 0 012-2h3a2 2 0 012 2v3a2 2 0 01-2 2H6a2 2 0 01-2-2v-3z",fillRule:"evenodd",clipRule:"evenodd"})}),a=(0,s.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(r.Path,{d:"M4 8.8h8.9V7.2H4v1.6zm0 7h8.9v-1.5H4v1.5zM18 13c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-3c1.1 0 2-.9 2-2s-.9-2-2-2-2 .9-2 2 .9 2 2 2z"})}),c=(0,s.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(r.Path,{d:"M11.1 15.8H20v-1.5h-8.9v1.5zm0-8.6v1.5H20V7.2h-8.9zM6 13c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2zm0-7c-1.1 0-2 .9-2 2s.9 2 2 2 2-.9 2-2-.9-2-2-2z"})});var u=n(4921),d=n(56427),f=n(86087);function m({selection:e,onChangeSelection:t,item:n,getItemId:r,primaryField:l,disabled:o}){const a=r(n),c=!o&&e.includes(a);let u;return u=l?.getValue&&n?(0,i.sprintf)(c?(0,i.__)("Deselect item: %s"):(0,i.__)("Select item: %s"),l.getValue({item:n})):c?(0,i.__)("Select a new item"):(0,i.__)("Deselect item"),(0,s.jsx)(d.CheckboxControl,{className:"dataviews-selection-checkbox",__nextHasNoMarginBottom:!0,"aria-label":u,"aria-disabled":o,checked:c,onChange:()=>{o||t(e.includes(a)?e.filter((e=>a!==e)):[...e,a])}})}var v=n(13933),p=n(36655),g=n(26882),h=n(49749);const x=(0,s.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(r.Path,{d:"M20 11.2H6.8l3.7-3.7-1-1L3.9 12l5.6 5.5 1-1-3.7-3.7H20z"})}),w=(0,s.jsx)(r.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,s.jsx)(r.Path,{d:"m14.5 6.5-1 1 3.7 3.7H4v1.6h13.2l-3.7 3.7 1 1 5.6-5.5z"})});var b=n(96601),y=n(28509),_=n(37870);const{DropdownMenuV2:C}=(0,y.T)(d.privateApis);function S({children:e}){return f.Children.toArray(e).filter(Boolean).map(((e,t)=>(0,s.jsxs)(f.Fragment,{children:[t>0&&(0,s.jsx)(C.Separator,{}),e]},t)))}const j=(0,f.forwardRef)((function({fieldId:e,view:t,fields:n,onChangeView:r,onHide:l,setOpenedFilter:o},a){const c=z(t,n),u=c?.indexOf(e),f=t.sort?.field===e;let m,v=!1,g=!1,y=!1,j=[];const I=t.layout?.combinedFields?.find((t=>t.id===e)),E=n.find((t=>t.id===e));if(I)m=I.header||I.label;else{if(!E)return null;v=!1!==E.enableHiding,g=!1!==E.enableSorting,m=E.header,j=(0,_.Z)(E),y=!(t.filters?.some((t=>e===t.field))||!E.elements?.length||!j.length||E.filterBy?.isPrimary)}return(0,s.jsx)(C,{align:"start",trigger:(0,s.jsxs)(d.Button,{size:"compact",className:"dataviews-view-table-header-button",ref:a,variant:"tertiary",children:[m,t.sort&&f&&(0,s.jsx)("span",{"aria-hidden":"true",children:p.vI[t.sort.direction]})]}),style:{minWidth:"240px"},children:(0,s.jsxs)(S,{children:[g&&(0,s.jsx)(C.Group,{children:p.GJ.map((n=>{const i=t.sort&&f&&t.sort.direction===n,l=`${e}-${n}`;return(0,s.jsx)(C.RadioItem,{name:"view-table-sorting",value:l,checked:i,onChange:()=>{r({...t,sort:{field:e,direction:n}})},children:(0,s.jsx)(C.ItemLabel,{children:p.CL[n]})},l)}))}),y&&(0,s.jsx)(C.Group,{children:(0,s.jsx)(C.Item,{prefix:(0,s.jsx)(d.Icon,{icon:h.A}),onClick:()=>{o(e),r({...t,page:1,filters:[...t.filters||[],{field:e,value:void 0,operator:j[0]}]})},children:(0,s.jsx)(C.ItemLabel,{children:(0,i.__)("Add filter")})})}),(0,s.jsxs)(C.Group,{children:[(0,s.jsx)(C.Item,{prefix:(0,s.jsx)(d.Icon,{icon:x}),disabled:u<1,onClick:()=>{var n;r({...t,fields:[...null!==(n=c.slice(0,u-1))&&void 0!==n?n:[],e,c[u-1],...c.slice(u+1)]})},children:(0,s.jsx)(C.ItemLabel,{children:(0,i.__)("Move left")})}),(0,s.jsx)(C.Item,{prefix:(0,s.jsx)(d.Icon,{icon:w}),disabled:u>=c.length-1,onClick:()=>{var n;r({...t,fields:[...null!==(n=c.slice(0,u))&&void 0!==n?n:[],c[u+1],e,...c.slice(u+2)]})},children:(0,s.jsx)(C.ItemLabel,{children:(0,i.__)("Move right")})}),v&&E&&(0,s.jsx)(C.Item,{prefix:(0,s.jsx)(d.Icon,{icon:b.A}),onClick:()=>{l(E),r({...t,fields:c.filter((t=>t!==e))})},children:(0,s.jsx)(C.ItemLabel,{children:(0,i.__)("Hide column")})})]})]})})}));function I({column:e,fields:t,view:n,...i}){const r=t.find((t=>t.id===e));if(r)return(0,s.jsx)(E,{...i,field:r});const l=n.layout?.combinedFields?.find((t=>t.id===e));return l?(0,s.jsx)(V,{...i,fields:t,view:n,field:l}):null}function E({primaryField:e,item:t,field:n}){return(0,s.jsx)("div",{className:(0,u.A)("dataviews-view-table__cell-content-wrapper",{"dataviews-view-table__primary-field":e?.id===n.id}),children:(0,s.jsx)(n.render,{item:t})})}function V({field:e,...t}){const n=e.children.map((e=>(0,s.jsx)(I,{...t,column:e},e)));return"horizontal"===e.direction?(0,s.jsx)(d.__experimentalHStack,{spacing:3,children:n}):(0,s.jsx)(d.__experimentalVStack,{spacing:0,children:n})}function k({hasBulkActions:e,item:t,actions:n,fields:i,id:r,view:l,primaryField:o,selection:a,getItemId:c,onChangeSelection:d}){const p=(0,g.up)(n,t),h=p&&a.includes(r),[x,w]=(0,f.useState)(!1),b=(0,f.useRef)(!1),y=z(l,i);return(0,s.jsxs)("tr",{className:(0,u.A)("dataviews-view-table__row",{"is-selected":p&&h,"is-hovered":x,"has-bulk-actions":p}),onMouseEnter:()=>{w(!0)},onMouseLeave:()=>{w(!1)},onTouchStart:()=>{b.current=!0},onClick:()=>{p&&(b.current||"Range"===document.getSelection()?.type||d(a.includes(r)?a.filter((e=>r!==e)):[r]))},children:[e&&(0,s.jsx)("td",{className:"dataviews-view-table__checkbox-column",style:{width:"1%"},children:(0,s.jsx)("div",{className:"dataviews-view-table__cell-content-wrapper",children:(0,s.jsx)(m,{item:t,selection:a,onChangeSelection:d,getItemId:c,primaryField:o,disabled:!p})})}),y.map((e=>{var n;const{width:r,maxWidth:a,minWidth:c}=null!==(n=l.layout?.styles?.[e])&&void 0!==n?n:{};return(0,s.jsx)("td",{style:{width:r,maxWidth:a,minWidth:c},children:(0,s.jsx)(I,{primaryField:o,fields:i,item:t,column:e,view:l})},e)})),!!n?.length&&(0,s.jsx)("td",{className:"dataviews-view-table__actions-column",onClick:e=>e.stopPropagation(),children:(0,s.jsx)(v.Ay,{item:t,actions:n})})]})}function A({selection:e,onChangeSelection:t,getItemId:n,item:i,actions:r,mediaField:l,primaryField:o,visibleFields:a,badgeFields:c,columnFields:f}){const p=(0,g.up)(r,i),h=n(i),x=e.includes(h),w=l?.render?(0,s.jsx)(l.render,{item:i}):null,b=o?.render?(0,s.jsx)(o.render,{item:i}):null;return(0,s.jsxs)(d.__experimentalVStack,{spacing:0,className:(0,u.A)("dataviews-view-grid__card",{"is-selected":p&&x}),onClickCapture:n=>{if(n.ctrlKey||n.metaKey){if(n.stopPropagation(),n.preventDefault(),!p)return;t(e.includes(h)?e.filter((e=>h!==e)):[...e,h])}},children:[(0,s.jsx)("div",{className:"dataviews-view-grid__media",children:w}),(0,s.jsx)(m,{item:i,selection:e,onChangeSelection:t,getItemId:n,primaryField:o,disabled:!p}),(0,s.jsxs)(d.__experimentalHStack,{justify:"space-between",className:"dataviews-view-grid__title-actions",children:[(0,s.jsx)(d.__experimentalHStack,{className:"dataviews-view-grid__primary-field",children:b}),(0,s.jsx)(v.Ay,{item:i,actions:r,isCompact:!0})]}),!!c?.length&&(0,s.jsx)(d.__experimentalHStack,{className:"dataviews-view-grid__badge-fields",spacing:2,wrap:!0,alignment:"top",justify:"flex-start",children:c.map((e=>(0,s.jsx)(d.FlexItem,{className:"dataviews-view-grid__field-value",children:(0,s.jsx)(e.render,{item:i})},e.id)))}),!!a?.length&&(0,s.jsx)(d.__experimentalVStack,{className:"dataviews-view-grid__fields",spacing:1,children:a.map((e=>(0,s.jsx)(d.Flex,{className:(0,u.A)("dataviews-view-grid__field",f?.includes(e.id)?"is-column":"is-row"),gap:1,justify:"flex-start",expanded:!0,style:{height:"auto"},direction:f?.includes(e.id)?"column":"row",children:(0,s.jsxs)(s.Fragment,{children:[(0,s.jsx)(d.FlexItem,{className:"dataviews-view-grid__field-name",children:e.header}),(0,s.jsx)(d.FlexItem,{className:"dataviews-view-grid__field-value",style:{maxHeight:"none"},children:(0,s.jsx)(e.render,{item:i})})]})},e.id)))})]},h)}var O=n(29491),N=n(33879),P=n(47143);const{DropdownMenuV2:M}=(0,y.T)(d.privateApis);function F(e){return`${e}-item-wrapper`}function L(e){return`${e}-dropdown`}function D({idPrefix:e,primaryAction:t,item:n}){const i=(0,P.useRegistry)(),[r,l]=(0,f.useState)(!1),o=function(e,t){return`${e}-primary-action-${t}`}(e,t.id),a="string"==typeof t.label?t.label:t.label([n]);return"RenderModal"in t?(0,s.jsx)("div",{role:"gridcell",children:(0,s.jsx)(d.Composite.Item,{id:o,render:(0,s.jsx)(d.Button,{label:a,icon:t.icon,isDestructive:t.isDestructive,size:"small",onClick:()=>l(!0)}),children:r&&(0,s.jsx)(v.cX,{action:t,items:[n],closeModal:()=>l(!1)})})},t.id):(0,s.jsx)("div",{role:"gridcell",children:(0,s.jsx)(d.Composite.Item,{id:o,render:(0,s.jsx)(d.Button,{label:a,icon:t.icon,isDestructive:t.isDestructive,size:"small",onClick:()=>{t.callback([n],{registry:i})}})})},t.id)}function T({actions:e,idPrefix:t,isSelected:n,item:r,mediaField:l,onSelect:o,primaryField:a,visibleFields:c,onDropdownTriggerKeyDown:m}){const p=(0,f.useRef)(null),g=`${t}-label`,h=`${t}-description`,[x,w]=(0,f.useState)(!1);(0,f.useEffect)((()=>{n&&p.current?.scrollIntoView({behavior:"auto",block:"nearest",inline:"nearest"})}),[n]);const{primaryAction:b,eligibleActions:y}=(0,f.useMemo)((()=>{const t=e.filter((e=>!e.isEligible||e.isEligible(r))),n=t.filter((e=>e.isPrimary&&!!e.icon));return{primaryAction:n?.[0],eligibleActions:t}}),[e,r]),_=l?.render?(0,s.jsx)(l.render,{item:r}):(0,s.jsx)("div",{className:"dataviews-view-list__media-placeholder"}),C=a?.render?(0,s.jsx)(a.render,{item:r}):null;return(0,s.jsx)(d.Composite.Row,{ref:p,render:(0,s.jsx)("li",{}),role:"row",className:(0,u.A)({"is-selected":n,"is-hovered":x}),onMouseEnter:()=>{w(!0)},onMouseLeave:()=>{w(!1)},children:(0,s.jsxs)(d.__experimentalHStack,{className:"dataviews-view-list__item-wrapper",alignment:"center",spacing:0,children:[(0,s.jsx)("div",{role:"gridcell",children:(0,s.jsx)(d.Composite.Item,{render:(0,s.jsx)("div",{}),role:"button",id:F(t),"aria-pressed":n,"aria-labelledby":g,"aria-describedby":h,className:"dataviews-view-list__item",onClick:()=>o(r),children:(0,s.jsxs)(d.__experimentalHStack,{spacing:3,justify:"start",alignment:"flex-start",children:[(0,s.jsx)("div",{className:"dataviews-view-list__media-wrapper",children:_}),(0,s.jsxs)(d.__experimentalVStack,{spacing:1,className:"dataviews-view-list__field-wrapper",children:[(0,s.jsx)("span",{className:"dataviews-view-list__primary-field",id:g,children:C}),(0,s.jsx)("div",{className:"dataviews-view-list__fields",id:h,children:c.map((e=>(0,s.jsxs)("div",{className:"dataviews-view-list__field",children:[(0,s.jsx)(d.VisuallyHidden,{as:"span",className:"dataviews-view-list__field-label",children:e.label}),(0,s.jsx)("span",{className:"dataviews-view-list__field-value",children:(0,s.jsx)(e.render,{item:r})})]},e.id)))})]})]})})}),y?.length>0&&(0,s.jsxs)(d.__experimentalHStack,{spacing:3,justify:"flex-end",className:"dataviews-view-list__item-actions",style:{flexShrink:"0",width:"auto"},children:[b&&(0,s.jsx)(D,{idPrefix:t,primaryAction:b,item:r}),(0,s.jsx)("div",{role:"gridcell",children:(0,s.jsx)(M,{trigger:(0,s.jsx)(d.Composite.Item,{id:L(t),render:(0,s.jsx)(d.Button,{size:"small",icon:N.A,label:(0,i.__)("Actions"),accessibleWhenDisabled:!0,disabled:!e.length,onKeyDown:m})}),placement:"bottom-end",children:(0,s.jsx)(v.DT,{actions:y,item:r})})})]})]})})}const H=[{type:p.Ad,label:(0,i.__)("Table"),component:function({actions:e,data:t,fields:n,getItemId:r,isLoading:l=!1,onChangeView:o,onChangeSelection:a,selection:c,setOpenedFilter:m,view:v}){const h=(0,f.useRef)(new Map),x=(0,f.useRef)(),[w,b]=(0,f.useState)(),y=(0,g.fx)(e,t);(0,f.useEffect)((()=>{x.current&&(x.current.focus(),x.current=void 0)}));const _=(0,f.useId)();if(w)return x.current=w,void b(void 0);const C=e=>{const t=h.current.get(e.id),n=t?h.current.get(t.fallback):void 0;b(n?.node)},S=z(v,n),I=!!t?.length,E=n.find((e=>e.id===v.layout?.primaryField));return(0,s.jsxs)(s.Fragment,{children:[(0,s.jsxs)("table",{className:"dataviews-view-table","aria-busy":l,"aria-describedby":_,children:[(0,s.jsx)("thead",{children:(0,s.jsxs)("tr",{className:"dataviews-view-table__row",children:[y&&(0,s.jsx)("th",{className:"dataviews-view-table__checkbox-column",style:{width:"1%"},scope:"col",children:(0,s.jsx)(g.a4,{selection:c,onChangeSelection:a,data:t,actions:e,getItemId:r})}),S.map(((e,t)=>{var i;const{width:r,maxWidth:l,minWidth:a}=null!==(i=v.layout?.styles?.[e])&&void 0!==i?i:{};return(0,s.jsx)("th",{style:{width:r,maxWidth:l,minWidth:a},"aria-sort":v.sort?.field===e?p.LW[v.sort.direction]:void 0,scope:"col",children:(0,s.jsx)(j,{ref:n=>{n?h.current.set(e,{node:n,fallback:S[t>0?t-1:1]}):h.current.delete(e)},fieldId:e,view:v,fields:n,onChangeView:o,onHide:C,setOpenedFilter:m})},e)})),!!e?.length&&(0,s.jsx)("th",{className:"dataviews-view-table__actions-column",children:(0,s.jsx)("span",{className:"dataviews-view-table-header",children:(0,i.__)("Actions")})})]})}),(0,s.jsx)("tbody",{children:I&&t.map(((t,i)=>(0,s.jsx)(k,{item:t,hasBulkActions:y,actions:e,fields:n,id:r(t)||i.toString(),view:v,primaryField:E,selection:c,getItemId:r,onChangeSelection:a},r(t))))})]}),(0,s.jsx)("div",{className:(0,u.A)({"dataviews-loading":l,"dataviews-no-results":!I&&!l}),id:_,children:!I&&(0,s.jsx)("p",{children:l?(0,s.jsx)(d.Spinner,{}):(0,i.__)("No results")})})]})},icon:l},{type:p.Ul,label:(0,i.__)("Grid"),component:function({actions:e,data:t,fields:n,getItemId:r,isLoading:l,onChangeSelection:o,selection:a,view:c,density:f}){const m=n.find((e=>e.id===c.layout?.mediaField)),v=n.find((e=>e.id===c.layout?.primaryField)),p=c.fields||n.map((e=>e.id)),{visibleFields:g,badgeFields:h}=n.reduce(((e,t)=>(!p.includes(t.id)||[c.layout?.mediaField,c?.layout?.primaryField].includes(t.id)||e[c.layout?.badgeFields?.includes(t.id)?"badgeFields":"visibleFields"].push(t),e)),{visibleFields:[],badgeFields:[]}),x=!!t?.length,w=f?{gridTemplateColumns:`repeat(${f}, minmax(0, 1fr))`}:{};return(0,s.jsxs)(s.Fragment,{children:[x&&(0,s.jsx)(d.__experimentalGrid,{gap:8,columns:2,alignment:"top",className:"dataviews-view-grid",style:w,"aria-busy":l,children:t.map((t=>(0,s.jsx)(A,{selection:a,onChangeSelection:o,getItemId:r,item:t,actions:e,mediaField:m,primaryField:v,visibleFields:g,badgeFields:h,columnFields:c.layout?.columnFields},r(t))))}),!x&&(0,s.jsx)("div",{className:(0,u.A)({"dataviews-loading":l,"dataviews-no-results":!l}),children:(0,s.jsx)("p",{children:l?(0,s.jsx)(d.Spinner,{}):(0,i.__)("No results")})})]})},icon:o},{type:p.mA,label:(0,i.__)("List"),component:function e(t){const{actions:n,data:r,fields:l,getItemId:o,isLoading:a,onChangeSelection:c,selection:m,view:v}=t,p=(0,O.useInstanceId)(e,"view-list"),g=r?.findLast((e=>m.includes(o(e)))),h=l.find((e=>e.id===v.layout?.mediaField)),x=l.find((e=>e.id===v.layout?.primaryField)),w=v.fields||l.map((e=>e.id)),b=l.filter((e=>w.includes(e.id)&&![v.layout?.primaryField,v.layout?.mediaField].includes(e.id))),y=e=>c([o(e)]),_=(0,f.useCallback)((e=>`${p}-${o(e)}`),[p,o]),C=(0,f.useCallback)(((e,t)=>t.startsWith(_(e))),[_]),[S,j]=(0,f.useState)(void 0);(0,f.useEffect)((()=>{g&&j(F(_(g)))}),[g,_]);const I=r.findIndex((e=>C(e,null!=S?S:""))),E=(0,O.usePrevious)(I),V=-1!==I,k=(0,f.useCallback)(((e,t)=>{const n=Math.min(r.length-1,Math.max(0,e));if(!r[n])return;const i=t(_(r[n]));j(i),document.getElementById(i)?.focus()}),[r,_]);(0,f.useEffect)((()=>{!V&&void 0!==E&&-1!==E&&k(E,F)}),[V,k,E]);const A=(0,f.useCallback)((e=>{"ArrowDown"===e.key&&(e.preventDefault(),k(I+1,L)),"ArrowUp"===e.key&&(e.preventDefault(),k(I-1,L))}),[k,I]),N=r?.length;return N?(0,s.jsx)(d.Composite,{id:p,render:(0,s.jsx)("ul",{}),className:"dataviews-view-list",role:"grid",activeId:S,setActiveId:j,children:r.map((e=>{const t=_(e);return(0,s.jsx)(T,{idPrefix:t,actions:n,item:e,isSelected:e===g,onSelect:y,mediaField:h,primaryField:x,visibleFields:b,onDropdownTriggerKeyDown:A},t)}))}):(0,s.jsx)("div",{className:(0,u.A)({"dataviews-loading":a,"dataviews-no-results":!N&&!a}),children:!N&&(0,s.jsx)("p",{children:a?(0,s.jsx)(d.Spinner,{}):(0,i.__)("No results")})})},icon:(0,i.isRTL)()?a:c}];function B(e){var t;return"table"===e.type?[e.layout?.primaryField].concat(null!==(t=e.layout?.combinedFields?.flatMap((e=>e.children)))&&void 0!==t?t:[]).filter((e=>!!e)):"grid"===e.type||"list"===e.type?[e.layout?.primaryField,e.layout?.mediaField].filter((e=>!!e)):[]}function R(e){const t=[];return e.type===p.Ad&&e.layout?.combinedFields&&e.layout.combinedFields.forEach((e=>{t.push(...e.children)})),t}function z(e,t){const n=R(e);if(e.fields)return e.fields.filter((e=>!n.includes(e)));const i=[];return e.type===p.Ad&&e.layout?.combinedFields&&i.push(...e.layout.combinedFields.map((({id:e})=>e))),i.push(...t.filter((({id:e})=>!n.includes(e))).map((({id:e})=>e))),i}function K(e,t){const n=[...R(e),...z(e,t)];return e.type===p.Ul&&e.layout?.mediaField&&n.push(e.layout?.mediaField),e.type===p.mA&&e.layout?.mediaField&&n.push(e.layout?.mediaField),t.filter((({id:e,enableHiding:t})=>!n.includes(e)&&t)).map((({id:e})=>e))}},28509:(e,t,n)=>{"use strict";n.d(t,{T:()=>s});var i=n(35434);const{lock:r,unlock:s}=(0,i.__dangerousOptInToUnstableAPIsOnlyForCoreModules)("I acknowledge private features are not for use in themes or plugins and doing so will break in the next version of WordPress.","@wordpress/dataviews")},34183:(e,t,n)=>{"use strict";n.d(t,{t:()=>f});const i={sort:function(e,t,n){return"asc"===n?e-t:t-e},isValid:function(e,t){if(""===e)return!1;if(!Number.isInteger(Number(e)))return!1;if(t?.elements){const n=t?.elements.map((e=>e.value));if(!n.includes(Number(e)))return!1}return!0},Edit:"integer"},r={sort:function(e,t,n){return"asc"===n?e.localeCompare(t):t.localeCompare(e)},isValid:function(e,t){if(t?.elements){const n=t?.elements?.map((e=>e.value));if(!n.includes(e))return!1}return!0},Edit:"text"},s={sort:function(e,t,n){const i=new Date(e).getTime(),r=new Date(t).getTime();return"asc"===n?i-r:r-i},isValid:function(e,t){if(t?.elements){const n=t?.elements.map((e=>e.value));if(!n.includes(e))return!1}return!0},Edit:"datetime"};var l=n(56427),o=n(86087),a=n(39793),c=n(27723);const u={datetime:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){const{id:r,label:s}=t,c=t.getValue({item:e}),u=(0,o.useCallback)((e=>n({[r]:e})),[r,n]);return(0,a.jsxs)("fieldset",{className:"dataviews-controls__datetime",children:[!i&&(0,a.jsx)(l.BaseControl.VisualLabel,{as:"legend",children:s}),i&&(0,a.jsx)(l.VisuallyHidden,{as:"legend",children:s}),(0,a.jsx)(l.TimePicker,{currentTime:c,onChange:u,hideLabelFromVision:!0})]})},integer:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){var r;const{id:s,label:c,description:u}=t,d=null!==(r=t.getValue({item:e}))&&void 0!==r?r:"",f=(0,o.useCallback)((e=>n({[s]:Number(e)})),[s,n]);return(0,a.jsx)(l.__experimentalNumberControl,{label:c,help:u,value:d,onChange:f,__next40pxDefaultSize:!0,hideLabelFromVision:i})},radio:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){const{id:r,label:s}=t,c=t.getValue({item:e}),u=(0,o.useCallback)((e=>n({[r]:e})),[r,n]);return t.elements?(0,a.jsx)(l.RadioControl,{label:s,onChange:u,options:t.elements,selected:c,hideLabelFromVision:i}):null},select:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){var r,s;const{id:u,label:d}=t,f=null!==(r=t.getValue({item:e}))&&void 0!==r?r:"",m=(0,o.useCallback)((e=>n({[u]:e})),[u,n]),v=[{label:(0,c.__)("Select item"),value:""},...null!==(s=t?.elements)&&void 0!==s?s:[]];return(0,a.jsx)(l.SelectControl,{label:d,value:f,options:v,onChange:m,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:i})},text:function({data:e,field:t,onChange:n,hideLabelFromVision:i}){const{id:r,label:s,placeholder:c}=t,u=t.getValue({item:e}),d=(0,o.useCallback)((e=>n({[r]:e})),[r,n]);return(0,a.jsx)(l.TextControl,{label:s,placeholder:c,value:null!=u?u:"",onChange:d,__next40pxDefaultSize:!0,__nextHasNoMarginBottom:!0,hideLabelFromVision:i})}};function d(e){if(Object.keys(u).includes(e))return u[e];throw"Control "+e+" not found"}function f(e){return e.map((e=>{var t,n,l,o;const a="integer"===(c=e.type)?i:"text"===c?r:"datetime"===c?s:{sort:(e,t,n)=>"number"==typeof e&&"number"==typeof t?"asc"===n?e-t:t-e:"asc"===n?e.localeCompare(t):t.localeCompare(e),isValid:(e,t)=>{if(t?.elements){const n=t?.elements?.map((e=>e.value));if(!n.includes(e))return!1}return!0},Edit:()=>null};var c;const u=e.getValue||(({item:t})=>t[e.id]),f=null!==(t=e.sort)&&void 0!==t?t:function(e,t,n){return a.sort(u({item:e}),u({item:t}),n)},m=null!==(n=e.isValid)&&void 0!==n?n:function(e,t){return a.isValid(u({item:e}),t)},v=function(e,t){return"function"==typeof e.Edit?e.Edit:"string"==typeof e.Edit?d(e.Edit):e.elements?d("select"):"string"==typeof t.Edit?d(t.Edit):t.Edit}(e,a),p=e.render||(e.elements?({item:t})=>{const n=u({item:t});return e?.elements?.find((e=>e.value===n))?.label||u({item:t})}:u);return{...e,label:e.label||e.id,header:e.header||e.label||e.id,getValue:u,render:p,sort:f,isValid:m,Edit:v,enableHiding:null===(l=e.enableHiding)||void 0===l||l,enableSorting:null===(o=e.enableSorting)||void 0===o||o}}))}},37870:(e,t,n)=>{"use strict";n.d(t,{Z:()=>r});var i=n(36655);function r(e){let t=e.filterBy?.operators;return t&&Array.isArray(t)||(t=[i.ld,i.Vw]),t=t.filter((e=>i.CD.includes(e))),(t.includes(i.gm)||t.includes(i._k))&&(t=t.filter((e=>[i.gm,i._k].includes(e)))),t}},75288:(e,t,n)=>{"use strict";n.d(t,{q:()=>r});var i=n(34183);function r(e,t,n){return(0,i.t)(t.filter((({id:e})=>!!n.fields?.includes(e)))).every((t=>t.isValid(e,{elements:t.elements})))}},73463:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var i=n(5573),r=n(39793);const s=(0,r.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(i.Path,{d:"M12 13.06l3.712 3.713 1.061-1.06L13.061 12l3.712-3.712-1.06-1.06L12 10.938 8.288 7.227l-1.061 1.06L10.939 12l-3.712 3.712 1.06 1.061L12 13.061z"})})},49749:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var i=n(5573),r=n(39793);const s=(0,r.jsx)(i.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(i.Path,{d:"M10 17.5H14V16H10V17.5ZM6 6V7.5H18V6H6ZM8 12.5H16V11H8V12.5Z"})})},33879:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var i=n(5573),r=n(39793);const s=(0,r.jsx)(i.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(i.Path,{d:"M13 19h-2v-2h2v2zm0-6h-2v-2h2v2zm0-6h-2V5h2v2z"})})},96601:(e,t,n)=>{"use strict";n.d(t,{A:()=>s});var i=n(5573),r=n(39793);const s=(0,r.jsx)(i.SVG,{viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:(0,r.jsx)(i.Path,{d:"M20.7 12.7s0-.1-.1-.2c0-.2-.2-.4-.4-.6-.3-.5-.9-1.2-1.6-1.8-.7-.6-1.5-1.3-2.6-1.8l-.6 1.4c.9.4 1.6 1 2.1 1.5.6.6 1.1 1.2 1.4 1.6.1.2.3.4.3.5v.1l.7-.3.7-.3Zm-5.2-9.3-1.8 4c-.5-.1-1.1-.2-1.7-.2-3 0-5.2 1.4-6.6 2.7-.7.7-1.2 1.3-1.6 1.8-.2.3-.3.5-.4.6 0 0 0 .1-.1.2s0 0 .7.3l.7.3V13c0-.1.2-.3.3-.5.3-.4.7-1 1.4-1.6 1.2-1.2 3-2.3 5.5-2.3H13v.3c-.4 0-.8-.1-1.1-.1-1.9 0-3.5 1.6-3.5 3.5s.6 2.3 1.6 2.9l-2 4.4.9.4 7.6-16.2-.9-.4Zm-3 12.6c1.7-.2 3-1.7 3-3.5s-.2-1.4-.6-1.9L12.4 16Z"})})},9303:e=>{var t={À:"A",Á:"A",Â:"A",Ã:"A",Ä:"A",Å:"A",Ấ:"A",Ắ:"A",Ẳ:"A",Ẵ:"A",Ặ:"A",Æ:"AE",Ầ:"A",Ằ:"A",Ȃ:"A",Ả:"A",Ạ:"A",Ẩ:"A",Ẫ:"A",Ậ:"A",Ç:"C",Ḉ:"C",È:"E",É:"E",Ê:"E",Ë:"E",Ế:"E",Ḗ:"E",Ề:"E",Ḕ:"E",Ḝ:"E",Ȇ:"E",Ẻ:"E",Ẽ:"E",Ẹ:"E",Ể:"E",Ễ:"E",Ệ:"E",Ì:"I",Í:"I",Î:"I",Ï:"I",Ḯ:"I",Ȋ:"I",Ỉ:"I",Ị:"I",Ð:"D",Ñ:"N",Ò:"O",Ó:"O",Ô:"O",Õ:"O",Ö:"O",Ø:"O",Ố:"O",Ṍ:"O",Ṓ:"O",Ȏ:"O",Ỏ:"O",Ọ:"O",Ổ:"O",Ỗ:"O",Ộ:"O",Ờ:"O",Ở:"O",Ỡ:"O",Ớ:"O",Ợ:"O",Ù:"U",Ú:"U",Û:"U",Ü:"U",Ủ:"U",Ụ:"U",Ử:"U",Ữ:"U",Ự:"U",Ý:"Y",à:"a",á:"a",â:"a",ã:"a",ä:"a",å:"a",ấ:"a",ắ:"a",ẳ:"a",ẵ:"a",ặ:"a",æ:"ae",ầ:"a",ằ:"a",ȃ:"a",ả:"a",ạ:"a",ẩ:"a",ẫ:"a",ậ:"a",ç:"c",ḉ:"c",è:"e",é:"e",ê:"e",ë:"e",ế:"e",ḗ:"e",ề:"e",ḕ:"e",ḝ:"e",ȇ:"e",ẻ:"e",ẽ:"e",ẹ:"e",ể:"e",ễ:"e",ệ:"e",ì:"i",í:"i",î:"i",ï:"i",ḯ:"i",ȋ:"i",ỉ:"i",ị:"i",ð:"d",ñ:"n",ò:"o",ó:"o",ô:"o",õ:"o",ö:"o",ø:"o",ố:"o",ṍ:"o",ṓ:"o",ȏ:"o",ỏ:"o",ọ:"o",ổ:"o",ỗ:"o",ộ:"o",ờ:"o",ở:"o",ỡ:"o",ớ:"o",ợ:"o",ù:"u",ú:"u",û:"u",ü:"u",ủ:"u",ụ:"u",ử:"u",ữ:"u",ự:"u",ý:"y",ÿ:"y",Ā:"A",ā:"a",Ă:"A",ă:"a",Ą:"A",ą:"a",Ć:"C",ć:"c",Ĉ:"C",ĉ:"c",Ċ:"C",ċ:"c",Č:"C",č:"c",C̆:"C",c̆:"c",Ď:"D",ď:"d",Đ:"D",đ:"d",Ē:"E",ē:"e",Ĕ:"E",ĕ:"e",Ė:"E",ė:"e",Ę:"E",ę:"e",Ě:"E",ě:"e",Ĝ:"G",Ǵ:"G",ĝ:"g",ǵ:"g",Ğ:"G",ğ:"g",Ġ:"G",ġ:"g",Ģ:"G",ģ:"g",Ĥ:"H",ĥ:"h",Ħ:"H",ħ:"h",Ḫ:"H",ḫ:"h",Ĩ:"I",ĩ:"i",Ī:"I",ī:"i",Ĭ:"I",ĭ:"i",Į:"I",į:"i",İ:"I",ı:"i",Ĳ:"IJ",ĳ:"ij",Ĵ:"J",ĵ:"j",Ķ:"K",ķ:"k",Ḱ:"K",ḱ:"k",K̆:"K",k̆:"k",Ĺ:"L",ĺ:"l",Ļ:"L",ļ:"l",Ľ:"L",ľ:"l",Ŀ:"L",ŀ:"l",Ł:"l",ł:"l",Ḿ:"M",ḿ:"m",M̆:"M",m̆:"m",Ń:"N",ń:"n",Ņ:"N",ņ:"n",Ň:"N",ň:"n",ŉ:"n",N̆:"N",n̆:"n",Ō:"O",ō:"o",Ŏ:"O",ŏ:"o",Ő:"O",ő:"o",Œ:"OE",œ:"oe",P̆:"P",p̆:"p",Ŕ:"R",ŕ:"r",Ŗ:"R",ŗ:"r",Ř:"R",ř:"r",R̆:"R",r̆:"r",Ȓ:"R",ȓ:"r",Ś:"S",ś:"s",Ŝ:"S",ŝ:"s",Ş:"S",Ș:"S",ș:"s",ş:"s",Š:"S",š:"s",Ţ:"T",ţ:"t",ț:"t",Ț:"T",Ť:"T",ť:"t",Ŧ:"T",ŧ:"t",T̆:"T",t̆:"t",Ũ:"U",ũ:"u",Ū:"U",ū:"u",Ŭ:"U",ŭ:"u",Ů:"U",ů:"u",Ű:"U",ű:"u",Ų:"U",ų:"u",Ȗ:"U",ȗ:"u",V̆:"V",v̆:"v",Ŵ:"W",ŵ:"w",Ẃ:"W",ẃ:"w",X̆:"X",x̆:"x",Ŷ:"Y",ŷ:"y",Ÿ:"Y",Y̆:"Y",y̆:"y",Ź:"Z",ź:"z",Ż:"Z",ż:"z",Ž:"Z",ž:"z",ſ:"s",ƒ:"f",Ơ:"O",ơ:"o",Ư:"U",ư:"u",Ǎ:"A",ǎ:"a",Ǐ:"I",ǐ:"i",Ǒ:"O",ǒ:"o",Ǔ:"U",ǔ:"u",Ǖ:"U",ǖ:"u",Ǘ:"U",ǘ:"u",Ǚ:"U",ǚ:"u",Ǜ:"U",ǜ:"u",Ứ:"U",ứ:"u",Ṹ:"U",ṹ:"u",Ǻ:"A",ǻ:"a",Ǽ:"AE",ǽ:"ae",Ǿ:"O",ǿ:"o",Þ:"TH",þ:"th",Ṕ:"P",ṕ:"p",Ṥ:"S",ṥ:"s",X́:"X",x́:"x",Ѓ:"Г",ѓ:"г",Ќ:"К",ќ:"к",A̋:"A",a̋:"a",E̋:"E",e̋:"e",I̋:"I",i̋:"i",Ǹ:"N",ǹ:"n",Ồ:"O",ồ:"o",Ṑ:"O",ṑ:"o",Ừ:"U",ừ:"u",Ẁ:"W",ẁ:"w",Ỳ:"Y",ỳ:"y",Ȁ:"A",ȁ:"a",Ȅ:"E",ȅ:"e",Ȉ:"I",ȉ:"i",Ȍ:"O",ȍ:"o",Ȑ:"R",ȑ:"r",Ȕ:"U",ȕ:"u",B̌:"B",b̌:"b",Č̣:"C",č̣:"c",Ê̌:"E",ê̌:"e",F̌:"F",f̌:"f",Ǧ:"G",ǧ:"g",Ȟ:"H",ȟ:"h",J̌:"J",ǰ:"j",Ǩ:"K",ǩ:"k",M̌:"M",m̌:"m",P̌:"P",p̌:"p",Q̌:"Q",q̌:"q",Ř̩:"R",ř̩:"r",Ṧ:"S",ṧ:"s",V̌:"V",v̌:"v",W̌:"W",w̌:"w",X̌:"X",x̌:"x",Y̌:"Y",y̌:"y",A̧:"A",a̧:"a",B̧:"B",b̧:"b",Ḑ:"D",ḑ:"d",Ȩ:"E",ȩ:"e",Ɛ̧:"E",ɛ̧:"e",Ḩ:"H",ḩ:"h",I̧:"I",i̧:"i",Ɨ̧:"I",ɨ̧:"i",M̧:"M",m̧:"m",O̧:"O",o̧:"o",Q̧:"Q",q̧:"q",U̧:"U",u̧:"u",X̧:"X",x̧:"x",Z̧:"Z",z̧:"z",й:"и",Й:"И",ё:"е",Ё:"Е"},n=Object.keys(t).join("|"),i=new RegExp(n,"g"),r=new RegExp(n,"");function s(e){return t[e]}var l=function(e){return e.replace(i,s)};e.exports=l,e.exports.has=function(e){return!!e.match(r)},e.exports.remove=l},26459:(e,t,n)=>{"use strict";var i=n(51609),r="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},s=i.useState,l=i.useEffect,o=i.useLayoutEffect,a=i.useDebugValue;function c(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!r(e,n)}catch(e){return!0}}var u="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),i=s({inst:{value:n,getSnapshot:t}}),r=i[0].inst,u=i[1];return o((function(){r.value=n,r.getSnapshot=t,c(r)&&u({inst:r})}),[e,n,t]),l((function(){return c(r)&&u({inst:r}),e((function(){c(r)&&u({inst:r})}))}),[e]),a(n),n};t.useSyncExternalStore=void 0!==i.useSyncExternalStore?i.useSyncExternalStore:u},94652:(e,t,n)=>{"use strict";e.exports=n(26459)}}]);