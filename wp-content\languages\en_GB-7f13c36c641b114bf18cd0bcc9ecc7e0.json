{"translation-revision-date": "2025-04-06 12:37:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "December": ["December"], "November": ["November"], "October": ["October"], "September": ["September"], "August": ["August"], "July": ["July"], "June": ["June"], "May": ["May"], "April": ["April"], "March": ["March"], "February": ["February"], "January": ["January"], "input control\u0004Show %s": ["Show %s"], "Border color picker. The currently selected color has a value of \"%s\".": ["Border colour picker. The currently selected colour has a value of \"%s\"."], "Border color picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\".": ["Border colour picker. The currently selected colour is called \"%1$s\" and has a value of \"%2$s\"."], "Border color and style picker. The currently selected color has a value of \"%s\".": ["Border colour and style picker. The currently selected colour has a value of \"%s\"."], "Border color and style picker. The currently selected color has a value of \"%1$s\". The currently selected style is \"%2$s\".": ["Border colour and style picker. The currently selected colour has a value of \"%1$s\". The currently selected style is \"%2$s\"."], "Border color and style picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\".": ["Border colour and style picker. The currently selected colour is called \"%1$s\" and has a value of \"%2$s\"."], "Border color and style picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\". The currently selected style is \"%3$s\".": ["Border colour and style picker. The currently selected colour is called \"%1$s\" and has a value of \"%2$s\". The currently selected style is \"%3$s\"."], "%s items selected": ["%s items selected"], "Select AM or PM": ["Select AM or PM"], "Select an item": ["Select an item"], "No items found": ["No items found"], "Remove color: %s": ["Remove colour: %s"], "authors\u0004All": ["All"], "categories\u0004All": ["All"], "Edit: %s": ["Edit: %s"], "Left and right sides": ["Left and right sides"], "Top and bottom sides": ["Top and bottom sides"], "Right side": ["Right side"], "Left side": ["Left side"], "Bottom side": ["Bottom side"], "Top side": ["Top side"], "Large viewport largest dimension (lvmax)": ["Large viewport largest dimension (lvmax)"], "Small viewport largest dimension (svmax)": ["Small viewport largest dimension (svmax)"], "Dynamic viewport largest dimension (dvmax)": ["Dynamic viewport largest dimension (dvmax)"], "Dynamic viewport smallest dimension (dvmin)": ["Dynamic viewport smallest dimension (dvmin)"], "Dynamic viewport width or height (dvb)": ["Dynamic viewport width or height (dvb)"], "Dynamic viewport width or height (dvi)": ["Dynamic viewport width or height (dvi)"], "Dynamic viewport height (dvh)": ["Dynamic viewport height (dvh)"], "Dynamic viewport width (dvw)": ["Dynamic viewport width (dvw)"], "Large viewport smallest dimension (lvmin)": ["Large viewport smallest dimension (lvmin)"], "Large viewport width or height (lvb)": ["Large viewport width or height (lvb)"], "Large viewport width or height (lvi)": ["Large viewport width or height (lvi)"], "Large viewport height (lvh)": ["Large viewport height (lvh)"], "Large viewport width (lvw)": ["Large viewport width (lvw)"], "Small viewport smallest dimension (svmin)": ["Small viewport smallest dimension (svmin)"], "Small viewport width or height (svb)": ["Small viewport width or height (svb)"], "Viewport smallest size in the block direction (svb)": ["Viewport smallest size in the block direction (svb)"], "Small viewport width or height (svi)": ["Small viewport width or height (svi)"], "Viewport smallest size in the inline direction (svi)": ["Viewport smallest size in the inline direction (svi)"], "Small viewport height (svh)": ["Small viewport height (svh)"], "Small viewport width (svw)": ["Small viewport width (svw)"], "No color selected": ["No colour selected"], "Notice": ["Notice"], "Error notice": ["Error notice"], "Information notice": ["Information notice"], "Warning notice": ["Warning notice"], "Focal point top position": ["Focal point top position"], "Focal point left position": ["Focal point left position"], "Scrollable section": ["Scrollable section"], "Initial %d result loaded. Type to filter all available results. Use up and down arrow keys to navigate.": ["Initial %d result loaded. Type to filter all available results. Use up and down arrow keys to navigate.", "Initial %d results loaded. Type to filter all available results. Use up and down arrow keys to navigate."], "Extra Extra Large": ["Extra Extra Large"], "Show details": ["Show details"], "Decrement": ["Decrement"], "Increment": ["Increment"], "All options reset": ["All options reset"], "All options are currently hidden": ["All options are currently hidden"], "%s is now visible": ["%s is now visible"], "%s hidden and reset to default": ["%s hidden and reset to default"], "%s reset to default": ["%s reset to default"], "XXL": ["XXL"], "XL": ["XL"], "L": ["L"], "M": ["M"], "S": ["S"], "Unset": ["Unset"], "%1$s. Selected": ["%1$s. selected"], "%1$s. Selected. There is %2$d event": ["%1$s. Selected. There is %2$d event", "%1$s. Selected. There are %2$d events"], "View next month": ["View next month"], "View previous month": ["View previous month"], "Border color and style picker": ["Border colour and style picker"], "Loading …": ["Loading …"], "All sides": ["All sides"], "Bottom border": ["Bottom border"], "Right border": ["Right border"], "Left border": ["Left border"], "Top border": ["Top border"], "Border color picker.": ["Border colour picker."], "Border color and style picker.": ["Border colour and style picker."], "Custom color picker. The currently selected color is called \"%1$s\" and has a value of \"%2$s\".": ["Custom colour picker. The currently selected colour is called \"%1$s\" and has a value of \"%2$s\"."], "Link sides": ["Link sides"], "Unlink sides": ["Unlink sides"], "Reset all": ["Reset all"], "Button label to reveal tool panel options\u0004%s options": ["%s options"], "Hide and reset %s": ["Hide and reset %s"], "Reset %s": ["Reset %s"], "Search %s": ["Search %s"], "Set custom size": ["Set custom size"], "Use size preset": ["Use size preset"], "Currently selected font size: %s": ["Currently selected font size: %s"], "Highlights": ["Highlights"], "Size of a UI element\u0004Extra Large": ["Extra Large"], "Size of a UI element\u0004Large": ["Large"], "Size of a UI element\u0004Medium": ["Medium"], "Size of a UI element\u0004Small": ["Small"], "Size of a UI element\u0004None": ["None"], "Currently selected: %s": ["Currently selected: %s"], "Reset colors": ["Reset colours"], "Reset gradient": ["Reset gradient"], "Remove all colors": ["Remove all colours"], "Remove all gradients": ["Remove all gradients"], "Color options": ["Colour options"], "Gradient options": ["Gradient options"], "Add color": ["Add colour"], "Add gradient": ["Add gradient"], "Gradient name": ["Gradient name"], "Color %s": ["Colour %s"], "Color format": ["Colour format"], "Hex color": ["Hex colour"], "Invalid item": ["Invalid item"], "Shadows": ["Shadows"], "Duotone: %s": ["Duotone: %s"], "Duotone code: %s": ["Duotone code: %s"], "%1$s. There is %2$d event": ["%1$s. There is %2$d event", "%1$s. There are %2$d events"], "Relative to root font size (rem)\u0004rems": ["rems"], "Relative to parent font size (em)\u0004ems": ["ems"], "Points (pt)": ["Points (pt)"], "Picas (pc)": ["Picas (pc)"], "Inches (in)": ["Inches (in)"], "Millimeters (mm)": ["Millimetres (mm)"], "Centimeters (cm)": ["Centimetres (cm)"], "x-height of the font (ex)": ["x-height of the font (ex)"], "Width of the zero (0) character (ch)": ["Width of the zero (0) character (ch)"], "Viewport largest dimension (vmax)": ["Viewport largest dimension (vmax)"], "Viewport smallest dimension (vmin)": ["Viewport smallest dimension (vmin)"], "Percent (%)": ["Percent (%)"], "Border width": ["Border width"], "Dotted": ["Dotted"], "Dashed": ["Dashed"], "Viewport height (vh)": ["Viewport height (vh)"], "Viewport width (vw)": ["Viewport width (vw)"], "Relative to root font size (rem)": ["Relative to root font size (rem)"], "Relative to parent font size (em)": ["Relative to parent font size (em)"], "Pixels (px)": ["Pixels (px)"], "Percentage (%)": ["Percentage (%)"], "Close search": ["Close search"], "Search in %s": ["Search in %s"], "Select unit": ["Select unit"], "Radial": ["Radial"], "Linear": ["Linear"], "Media preview": ["Media preview"], "Coordinated Universal Time": ["Coordinated Universal Time"], "Color name": ["Colour name"], "Reset search": ["Reset search"], "Box Control": ["Box Control"], "Alignment Matrix Control": ["Alignment Matrix Control"], "Bottom Center": ["Bottom Centre"], "Center Right": ["Centre Right"], "Center Left": ["Centre Left"], "Top Center": ["Top Centre"], "Solid": ["Solid"], "Finish": ["Finish"], "Page %1$d of %2$d": ["Page %1$d of %2$d"], "Guide controls": ["Guide controls"], "Gradient: %s": ["Gradient: %s"], "Gradient code: %s": ["Gradient code: %s"], "Remove Control Point": ["Remove Control Point"], "Use your left or right arrow keys or drag and drop with the mouse to change the gradient position. Press the button to change the color or remove the control point.": ["Use your left or right arrow keys or drag and drop with the mouse to change the gradient position. Press the button to change the colour or remove the control point."], "Gradient control point at position %1$s%% with color code %2$s.": ["Gradient control point at position %1$s%% with colour code %2$s."], "Extra Large": ["Extra Large"], "Small": ["Small"], "Angle": ["<PERSON><PERSON>"], "Separate with commas or the Enter key.": ["Separate with commas or the Enter key."], "Separate with commas, spaces, or the Enter key.": ["Separate with commas, spaces, or the Enter key."], "Copied!": ["Copied!"], "%d result found.": ["%d result found.", "%d results found."], "Number of items": ["Number of items"], "Category": ["Category"], "Z → A": ["Z \t A"], "A → Z": ["A \t Z"], "Oldest to newest": ["Oldest to newest"], "Newest to oldest": ["Newest to oldest"], "Order by": ["Order by"], "Dismiss this notice": ["Dismiss this notice"], "%1$s (%2$s of %3$s)": ["%1$s (%2$s of %3$s)"], "Remove item": ["Remove item"], "Item removed.": ["Item removed."], "Item added.": ["<PERSON><PERSON> added."], "Add item": ["Add item"], "Reset": ["Reset"], "(opens in a new tab)": ["(opens in a new tab)"], "Minutes": ["Minutes"], "Color code: %s": ["Colour code: %s"], "Custom color picker": ["Custom colour picker"], "No results.": ["No results."], "%d result found, use up and down arrow keys to navigate.": ["%d result found, use up and down arrow keys to navigate.", "%d results found, use up and down arrow keys to navigate."], "Time": ["Time"], "Day": ["Day"], "Month": ["Month"], "Date": ["Date"], "Hours": ["Hours"], "Item selected.": ["Item selected."], "Previous": ["Previous"], "Year": ["Year"], "Custom Size": ["Custom Size"], "Back": ["Back"], "Style": ["Style"], "Large": ["Large"], "Drop files to upload": ["Drop files to upload"], "Clear": ["Clear"], "Mixed": ["Mixed"], "Custom": ["Custom"], "Next": ["Next"], "PM": ["PM"], "AM": ["AM"], "Bottom Right": ["Bottom Right"], "Bottom Left": ["Bottom Left"], "Top Right": ["Top Right"], "Top Left": ["Top Left"], "Type": ["Type"], "Top": ["Top"], "Copy": ["Copy"], "Font size": ["Font size"], "Calendar": ["Calendar"], "No results found.": ["No results found."], "Default": ["<PERSON><PERSON><PERSON>"], "Close": ["Close"], "Search": ["Search"], "OK": ["OK"], "Size": ["Size"], "Medium": ["Medium"], "Center": ["Centre"], "Left": ["Left"], "Cancel": ["Cancel"], "Done": ["Done"], "None": ["None"], "Categories": ["Categories"], "Author": ["Author"]}}, "comment": {"reference": "wp-includes/js/dist/components.js"}}