(()=>{"use strict";const e=window.wc.customerEffortScore;function t(e){const t=e.querySelectorAll("input, select, textarea"),o={};for(const e of t){const t=e.name||e.id;if("button"!==e.type&&"image"!==e.type&&"submit"!==e.type&&"hidden"!==e.type&&t)switch(e.type){case"checkbox":o[t]=+e.checked;break;case"radio":void 0===o[t]&&(o[t]=""),e.checked&&(o[t]=e.value);break;case"select-multiple":const r=[];for(const t of e.options)t.selected&&r.push(t.value);o[t]=r;break;default:o[t]=e.value}}return o}const o=document.forms;if(o?.post||o?.order){let r=!1;const c=document.querySelector(".save_order"),s=document.querySelector(".submitdelete");c&&c.addEventListener("click",(()=>{r=!0})),s&&s.addEventListener("click",(()=>{r=!0}));const n=t(o?.post||o?.order);(0,e.addCustomerEffortScoreExitPageListener)("shop_order_update",(()=>{if(r)return!1;const e=o?.post||o?.order?t(o?.post||o?.order):{};for(const t of Object.keys(n))if(("object"==typeof n[t]?JSON.stringify(n[t]):n[t])!==("object"==typeof e[t]?JSON.stringify(e[t]):e[t]))return!0;return!1}))}(window.wc=window.wc||{}).orderTracking={}})();