"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.PlaceHolder=PlaceHolder;const element_1=require("@wordpress/element"),i18n_1=require("@wordpress/i18n"),products_1=require("./imgs/products"),product_1=require("./imgs/product");function PlaceHolder({multiple:e=!0}){return(0,element_1.createElement)("div",{className:"woocommerce-image-placeholder__wrapper"},e?(0,element_1.createElement)(products_1.Products,null):(0,element_1.createElement)(product_1.Product,null),(0,element_1.createElement)("p",null,e?(0,i18n_1.__)("For best results, offer a variety of product images, like close-up details, lifestyle scenes, and color variations.","woocommerce"):(0,i18n_1.__)("Add an image which displays the unique characteristics of this variation.","woocommerce")))}