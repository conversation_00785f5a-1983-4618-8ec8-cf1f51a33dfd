{"translation-revision-date": "2025-04-06 12:37:11+0000", "generator": "GlotPress/4.0.1", "domain": "messages", "locale_data": {"messages": {"": {"domain": "messages", "plural-forms": "nplurals=2; plural=n != 1;", "lang": "en_GB"}, "Uncategorized": ["Uncategorised"], "The requested page could not be found. Please check the URL.": ["The requested page could not be found. Please check the URL."], "Preview your website's visual identity: colors, typography, and blocks.": ["Preview your website's visual identity: colours, typography, and blocks."], "Status & Visibility": ["Status & Visibility"], "This field can't be moved down": ["This field can't be moved down"], "This field can't be moved up": ["This field can't be moved up"], "Density option for DataView layout\u0004Compact": ["Compact"], "Density option for DataView layout\u0004Balanced": ["Balanced"], "Density option for DataView layout\u0004Comfortable": ["Comfortable"], "Density": ["Density"], "Navigate to item": ["Navigate to item"], "verb\u0004Filter": ["Filter"], "Global Styles pagination": ["Global Styles pagination"], "Apply the selected revision to your site.": ["Apply the selected revision to your site."], "Indicates these doutone filters are created by the user.\u0004Custom": ["Custom"], "Indicates these duotone filters come from WordPress.\u0004Default": ["<PERSON><PERSON><PERSON>"], "Indicates these duotone filters come from the theme.\u0004Theme": ["Theme"], "Additionally, paragraphs help structure the flow of information and provide logical breaks between different concepts or pieces of information. In terms of formatting, paragraphs in websites are commonly denoted by a vertical gap or indentation between each block of text. This visual separation helps visually distinguish one paragraph from another, creating a clear and organized layout that guides the reader through the content smoothly.": ["Additionally, paragraphs help structure the flow of information and provide logical breaks between different concepts or pieces of information. In terms of formatting, paragraphs in websites are commonly denoted by a vertical gap or indentation between each block of text. This visual separation helps visually distinguish one paragraph from another, creating a clear and organised layout that guides the reader through the content smoothly."], "A paragraph in a website refers to a distinct block of text that is used to present and organize information. It is a fundamental unit of content in web design and is typically composed of a group of related sentences or thoughts focused on a particular topic or idea. Paragraphs play a crucial role in improving the readability and user experience of a website. They break down the text into smaller, manageable chunks, allowing readers to scan the content more easily.": ["A paragraph in a website refers to a distinct block of text that is used to present and organise information. It is a fundamental unit of content in web design and is typically composed of a group of related sentences or thoughts focused on a particular topic or idea. Paragraphs play a crucial role in improving the readability and user experience of a website. They break down the text into smaller, manageable chunks, allowing readers to scan the content more easily."], "AaBbCcDdEeFfGgHhiiJjKkLIMmNnOoPpQakRrssTtUuVVWwXxxYyZzOl23356789X{(…)},2!*&:/A@HELFO™": ["AaBbCcDdEeFfGgHhiiJjKkLIMmNnOoPpQakRrssTtUuVVWwXxxYyZzOl23356789X{(…)},2!*&:/A@HELFO™"], "Default Gradients": ["De<PERSON><PERSON>"], "Default Colors": ["Default Colours"], "Duotones": ["Duotones"], "Custom Gradients": ["Custom Gradients"], "Theme Gradients": ["Theme Gradients"], "Theme Colors": ["Theme Colours"], "Are you sure you want to delete \"%s\" shadow preset?": ["Are you sure you want to delete \"%s\" shadow preset?"], "Remove all custom shadows": ["Remove all custom shadows"], "Shadow options": ["Shadow options"], "Are you sure you want to remove all custom shadows?": ["Are you sure you want to remove all custom shadows?"], "noun\u0004Upload": ["Upload"], "The theme you are currently using does not support this screen.": ["The theme you are currently using does not support this screen."], "Explore block styles and patterns to refine your site.": ["Explore block styles and patterns to refine your site."], "field\u0004Edit %s": ["Edit %s"], "navigation menu\u0004%s (Copy)": ["%s (Copy)"], "menu label\u0004%1$s (%2$s)": ["%1$s (%2$s)"], "Previewing %1$s: %2$s": ["Previewing %1$s: %2$s"], "taxonomy menu label\u0004%1$s (%2$s)": ["%1$s (%2$s)"], "taxonomy template menu label\u0004%1$s (%2$s)": ["%1$s (%2$s)"], "post type menu label\u0004Single item: %1$s (%2$s)": ["Single item: %1$s (%2$s)"], "post type menu label\u0004%1$s (%2$s)": ["%1$s (%2$s)"], "pattern category\u0004Delete \"%s\"?": ["Delete \"%s\"?"], "pattern category\u0004\"%s\" deleted.": ["\"%s\" deleted."], "field\u0004Show %s": ["Show %s"], "field\u0004Hide %s": ["Hide %s"], "breadcrumb trail\u0004%1$s ‹ %2$s": ["%1$s ‹ %2$s"], "variation label\u0004%1$s (%2$s)": ["%1$s (%2$s)"], "There was an error updating the font family. %s": ["There was an error updating the font family. %s"], "Select a page to edit": ["Select a page to edit"], "Post Edit": ["Post edit"], "Draft new: %s": ["Draft new: %s"], "All items": ["All items"], "View is used as a noun\u0004View options": ["View options"], "Properties": ["Properties"], "Move %s down": ["Move %s down"], "Move %s up": ["Move %s up"], "Preview size": ["Preview size"], "paging\u0004<div>Page</div>%1$s<div>of %2$s</div>": ["<div>Page</div>%1$s<div>of %2$s</div>"], "Page %1$s of %2$s": ["Page %1$s of %2$s"], "Hide column": ["Hide column"], "%d Item": ["%d <PERSON>em", "%d Items"], "Select item": ["Select item"], "Set styles for the site’s background.": ["Set styles for the site’s background."], "Create and edit the presets used for font sizes across the site.": ["Create and edit the presets used for font sizes across the site."], "New Font Size %d": ["New font size %d"], "Reset font size presets": ["Reset font size presets"], "Remove font size presets": ["Remove font size presets"], "Font size presets options": ["Font size presets options"], "Add font size": ["Add font size"], "Are you sure you want to reset all font size presets to their default values?": ["Are you sure you want to reset all font size presets to their default values?"], "Are you sure you want to remove all custom font size presets?": ["Are you sure you want to remove all custom font size presets?"], "Maximum": ["Maximum"], "Minimum": ["Minimum"], "Set custom min and max values for the fluid font size.": ["Set custom minimum and maximum values for the fluid font size."], "Custom fluid values": ["Custom fluid values"], "Scale the font size dynamically to fit the screen or viewport.": ["Scale the font size dynamically to fit the screen or viewport."], "Fluid typography": ["Fluid typography"], "Font size options": ["Font size options"], "Manage the font size %s.": ["Manage the font size %s."], "Font size preset name": ["Font size preset name"], "Are you sure you want to delete \"%s\" font size preset?": ["Are you sure you want to delete \"%s\" font size preset?"], "All headings": ["All headings"], "Typesets": ["Typesets"], "Available fonts, typographic styles, and the application of those styles.": ["Available fonts, typographic styles, and the application of those styles."], "No fonts activated.": ["No fonts activated."], "font source\u0004Custom": ["Custom"], "font source\u0004Theme": ["Theme"], "Font family updated successfully.": ["Font family updated successfully."], "Font size presets": ["Font size presets"], "Background styles": ["Background styles"], "Go to Site Editor": ["Go to Site Editor"], "No items found": ["No items found"], "Loading items…": ["Loading items…"], "pattern (singular)\u0004Not synced": ["Not synced"], "Includes every template part defined for any area.": ["Includes every template part defined for any area."], "All Template Parts": ["All template parts"], "%d Item selected": ["%d <PERSON><PERSON> selected", "%d Items selected"], "<Name>%1$s is not: </Name><Value>%2$s</Value>": ["<Name>%1$s is not: </Name><Value>%2$s</Value>"], "<Name>%1$s is: </Name><Value>%2$s</Value>": ["<Name>%1$s is: </Name><Value>%2$s</Value>"], "<Name>%1$s is not all: </Name><Value>%2$s</Value>": ["<Name>%1$s is not all: </Name><Value>%2$s</Value>"], "<Name>%1$s is all: </Name><Value>%2$s</Value>": ["<Name>%1$s is all: </Name><Value>%2$s</Value>"], "<Name>%1$s is none: </Name><Value>%2$s</Value>": ["<Name>%1$s is none: </Name><Value>%2$s</Value>"], "<Name>%1$s is any: </Name><Value>%2$s</Value>": ["<Name>%1$s is any: </Name><Value>%2$s</Value>"], "List of: %1$s": ["List of: %1$s"], "Is not all": ["Is not all"], "Is all": ["Is all"], "Is none": ["Is none"], "Is any": ["Is any"], "Are you sure you want to apply this revision? Any unsaved changes will be lost.": ["Are you sure you want to apply this revision? Any unsaved changes will be lost."], "Spread": ["Spread"], "Blur": ["Blur"], "Y Position": ["Y position"], "X Position": ["X position"], "Inset": ["Inset"], "Outset": ["Outset"], "Inner shadow": ["Inner shadow"], "Remove shadow": ["Remove shadow"], "Shadow name": ["Shadow name"], "Add shadow": ["Add shadow"], "Shadow %s": ["Shadow %s"], "Manage and create shadow styles for use across the site.": ["Manage and create shadow styles for use across the site."], "The combination of colors used across the site and in color pickers.": ["The combination of colours used across the site and in colour pickers."], "Palettes": ["Palettes"], "Palette colors and the application of those colors on site elements.": ["Palette colours and the application of those colours on site elements."], "Add colors": ["Add colours"], "Edit palette": ["Edit palette"], "Lowercase letter A\u0004a": ["a"], "Uppercase letter A\u0004A": ["A"], "pattern (singular)\u0004Synced": ["Synced"], "https://developer.wordpress.org/advanced-administration/wordpress/css/": ["https://developer.wordpress.org/advanced-administration/wordpress/css/"], "Font library\u0004Library": ["Library"], "There was an error installing fonts.": ["There was an error installing fonts."], "Empty template": ["Empty template"], "Action menu for %s pattern category": ["Action menu for %s pattern category"], "Are you sure you want to delete the category \"%s\"? The patterns will not be deleted.": ["Are you sure you want to delete the category \"%s\"? The patterns will not be deleted."], "An error occurred while deleting the pattern category.": ["An error occurred while deleting the pattern category."], "Filter by: %1$s": ["Filter by: %1$s"], "Conditions": ["Conditions"], "Unknown status for %1$s": ["Unknown status for %1$s"], "Search items": ["Search items"], "Sort by": ["Sort by"], "Items per page": ["Items per page"], "%1$s ‹ %2$s ‹ Editor — WordPress": ["%1$s ‹ %2$s ‹ Editor – WordPress"], "Click on previously saved styles to preview them. To restore a selected version to the editor, hit \"Apply.\" When you're ready, use the Save button to save your changes.": ["Click on previously saved styles to preview them. To restore a selected version to the editor, hit \"Apply.\" When you're ready, use the “Save” button to save your changes."], "Revisions (%s)": ["Revisions (%s)"], "These styles are already applied to your site.": ["These styles are already applied to your site."], "(Unsaved)": ["(Unsaved)"], "Changes saved by %1$s on %2$s. This revision matches current editor styles.": ["Changes saved by %1$s on %2$s. This revision matches current editor styles."], "heading levels\u0004All": ["All"], "Add fonts": ["Add fonts"], "No fonts installed.": ["No fonts installed."], "Uploaded fonts appear in your library and can be used in your theme. Supported formats: .ttf, .otf, .woff, and .woff2.": ["Uploaded fonts appear in your library and can be used in your theme. Supported formats: .ttf, .otf, .woff, and .woff2."], "No fonts found to install.": ["No fonts found to install."], "Current page": ["Current page"], "Revoke access to Google Fonts": ["Revoke access to Google Fonts"], "Error installing the fonts, could not be downloaded.": ["Error installing the fonts, could not be downloaded."], "font categories\u0004All": ["All"], "Connect to Google Fonts": ["Connect to Google Fonts"], "Are you sure you want to delete \"%s\" font and all its variants and assets?": ["Are you sure you want to delete \"%s\" font and all its variants and assets?"], "There was an error uninstalling the font family.": ["There was an error uninstalling the font family."], "%d variant": ["%d variant", "%d variants"], "Reset styles": ["Reset styles"], "Saving your changes will change your active theme from %1$s to %2$s.": ["Saving your changes will change your active theme from %1$s to %2$s."], "Custom Views": ["Custom views"], "Add new view": ["Add new view"], "New view": ["New view"], "My view": ["My view"], "Drafts": ["Drafts"], "Activate %s": ["Activate %s"], "Activate %s & Save": ["Activate %s and save"], "Activating %s": ["Activating %s"], "Sort descending": ["Sort descending"], "Sort ascending": ["Sort ascending"], "Is not": ["Is not"], "Is": ["Is"], "No results": ["No results"], "Deselect all": ["Deselect all"], "Add filter": ["Add filter"], "Style Revisions": ["Style revisions"], "Drop shadow": ["Drop shadow"], "Empty template part": ["Empty template part"], "Style revisions": ["Style revisions"], "Customize CSS": ["Customise CSS"], "Default styles": ["Default styles"], "Reset the styles to the theme defaults": ["Reset the styles to the theme defaults"], "Manage fonts": ["Manage fonts"], "Fonts": ["Fonts"], "Install Fonts": ["Install Fonts"], "Upload font": ["Upload font"], "No fonts found. Try with a different search term": ["No fonts found. Try with a different search term"], "Font name…": ["Font name…"], "Select font variants to install.": ["Select font variants to install."], "Allow access to Google Fonts": ["Allow access to Google Fonts"], "You can alternatively upload files directly on the Upload tab.": ["You can alternatively upload files directly on the Upload tab."], "To install fonts from Google you must give permission to connect directly to Google servers. The fonts you install will be downloaded from Google and stored on your site. Your site will then use these locally-hosted fonts.": ["To install fonts from Google you must give permission to connect directly to Google servers. The fonts you install will be downloaded from Google and stored on your site. Your site will then use these locally-hosted fonts."], "Choose font variants. Keep in mind that too many variants could make your site slower.": ["Choose font variants. Keep in mind that too many variants could make your site slower."], "Font family uninstalled successfully.": ["<PERSON>ont family uninstalled successfully."], "Fonts were installed successfully.": ["Fonts were installed successfully."], "%1$s/%2$s variants active": ["%1$s/%2$s variants active"], "font style\u0004Normal": ["Normal"], "font weight\u0004Extra-bold": ["Extra-bold"], "font weight\u0004Semi-bold": ["Semi-bold"], "font weight\u0004Normal": ["Normal"], "font weight\u0004Extra-light": ["Extra-light"], "Add your own CSS to customize the appearance of the %s block. You do not need to include a CSS selector, just add the property and value.": ["Add your own CSS to customise the appearance of the %s block. You do not need to include a CSS selector, just add the property and value."], "Imported \"%s\" from JSON.": ["Imported \"%s\" from JSON."], "Import pattern from JSON": ["Import pattern from JSON"], "A list of all patterns from all sources.": ["A list of all patterns from all sources."], "No results found": ["No results found"], "All patterns": ["All patterns"], "Changes saved by %1$s on %2$s": ["Changes saved by %1$s on %2$s"], "Unsaved changes by %s": ["Unsaved changes by %s"], "Sync status": ["Sync status"], "Patterns content": ["Patterns content"], "Patterns that can be changed freely without affecting the site.": ["Patterns that can be changed freely without affecting the site."], "Patterns that are kept in sync across the site.": ["Patterns that are kept in sync across the site."], "Last page": ["Last page"], "paging\u0004%1$s of %2$s": ["%1$s of %2$s"], "First page": ["First page"], "Empty pattern": ["Empty pattern"], "Learn about styles": ["Learn about styles"], "Open styles": ["Open styles"], "Save panel": ["Save panel"], "Use left and right arrow keys to resize the canvas. Hold shift to resize in larger increments.": ["Use left and right arrow keys to resize the canvas. Hold shift to resize in larger increments."], "Open command palette": ["Open command palette"], "View site (opens in a new tab)": ["View site (opens in a new tab)"], "Open Navigation": ["Open Navigation"], "Note that the same template can be used by multiple pages, so any changes made here may affect other pages on the site. To switch back to editing the page content click the ‘Back’ button in the toolbar.": ["Note that the same template can be used by multiple pages, so any changes made here may affect other pages on the site. To switch back to editing the page content click the ‘Back’ button in the toolbar."], "Editing a template": ["Editing a template"], "It’s now possible to edit page content in the site editor. To customise other parts of the page like the header and footer switch to editing the template using the settings sidebar.": ["It’s now possible to edit page content in the site editor. To customise other parts of the page like the header and footer switch to editing the template using the settings sidebar."], "Continue": ["Continue"], "Editing a page": ["Editing a page"], "Close Styles": ["Close Styles"], "Close revisions": ["Close revisions"], "Manage the fonts and typography used on captions.": ["Manage the fonts and typography used on captions."], "Create draft": ["Create draft"], "No title": ["No title"], "Review %d change…": ["Review %d change…", "Review %d changes…"], "Activate & Save": ["Activate & Save"], "Manage your Navigation Menus.": ["Manage your navigation menus."], "No Navigation Menus found.": ["No Navigation Menus found."], "Unable to duplicate Navigation Menu (%s).": ["Unable to duplicate navigation menu (%s)."], "Duplicated Navigation Menu": ["Duplicated navigation menu"], "Unable to rename Navigation Menu (%s).": ["Unable to rename navigation menu (%s)."], "Renamed Navigation Menu": ["Renamed navigation menu"], "Unable to delete Navigation Menu (%s).": ["Unable to delete navigation menu (%s)."], "Navigation Menu missing.": ["Navigation Menu missing."], "Navigation Menus are a curated collection of blocks that allow visitors to get around your site.": ["Navigation menus are a curated collection of blocks that allow visitors to get around your site."], "Are you sure you want to delete this Navigation Menu?": ["Are you sure you want to delete this navigation menu?"], "Navigation title": ["Navigation title"], "Go to %s": ["Go to %s"], "Manage what patterns are available when editing the site.": ["Manage what patterns are available when editing the site."], "Select what the new template should apply to:": ["Select what the new template should apply to:"], "E.g. %s": ["E.g. %s"], "Examples of blocks": ["Examples of blocks"], "Global styles revisions list": ["Global styles revisions list"], "Go to the Dashboard": ["Go to the Dashboard"], "My patterns": ["My patterns"], "Grid": ["Grid"], "Examples of blocks in the %s category": ["Examples of blocks in the %s category"], "A custom template can be manually applied to any post or page.": ["A custom template can be manually applied to any post or page."], "Create new templates, or reset any customizations made to the templates supplied by your theme.": ["Create new templates, or reset any customisations made to the templates supplied by your theme."], "Customize the appearance of your website using the block editor.": ["Customise the appearance of your website using the block editor."], "Template part": ["Template part"], "CSS": ["CSS"], "Open %s styles in Styles panel": ["Open %s styles in Styles panel"], "Style Book": ["Style Book"], "Choose a variation to change the look of the site.": ["Choose a variation to change the look of the site."], "Randomize colors": ["Randomise colours"], "Add your own CSS to customize the appearance and layout of your site.": ["Add your own CSS to customise the appearance and layout of your site."], "Style Variations": ["Style Variations"], "All template parts": ["All Template Parts"], "Apply globally": ["Apply globally"], "Apply this block’s typography, spacing, dimensions, and color styles to all %s blocks.": ["Apply this block’s typography, spacing, dimensions, and colour styles to all %s blocks."], "%s styles applied.": ["%s styles applied."], "(no title %s)": ["(no title %s)"], "Pattern": ["Pattern"], "Custom template": ["Custom template"], "\"%s\" successfully created.": ["\"%s\" successfully created."], "This template will be used only for the specific item chosen.": ["This template will be used only for the specific item chosen."], "For a specific item": ["For a specific item"], "For all items": ["For all items"], "Select whether to create a single template for all items or a specific one.": ["Select whether to create a single template for all items or a specific one."], "Add template: %s": ["Add template: %s"], "Suggestions list": ["Suggestions list"], "All Authors": ["All authors"], "No authors found.": ["No authors found."], "Search Authors": ["Search Authors"], "Displays taxonomy: %s.": ["Displays taxonomy: %s."], "Displays a single item: %s.": ["Displays a single item: %s."], "Single item: %s": ["Single item: %s"], "Displays an archive with the latest posts of type: %s.": ["Displays an archive with the latest posts of type: %s."], "Archive: %1$s (%2$s)": ["Archive: %1$s (%2$s)"], "Manage the fonts and typography used on buttons.": ["Manage the fonts and typography used on buttons."], "Manage the fonts and typography used on headings.": ["Manage the fonts and typography used on headings."], "H6": ["H6"], "H5": ["H5"], "H4": ["H4"], "H3": ["H3"], "H2": ["H2"], "H1": ["H1"], "Select heading level": ["Select heading level"], "View site": ["View site"], "Add template": ["Add template"], "Describe the template, e.g. \"Post with sidebar\". A custom template can be manually applied to any post or page.": ["Describe the template, e.g. \"Post with sidebar\". A custom template can be manually applied to any post or page."], "This Navigation Menu is empty.": ["This Navigation Menu is empty."], "Browse styles": ["Browse styles"], "Download your theme with updated templates and styles.": ["Download your theme with updated templates and styles."], "The \"%s\" plugin has encountered an error and cannot be rendered.": ["The \"%s\" plugin has encountered an error and cannot be rendered."], "Navigation Menu successfully deleted.": ["Navigation menu successfully deleted."], "font weight\u0004Black": ["Black"], "font weight\u0004Bold": ["Bold"], "font weight\u0004Medium": ["Medium"], "font weight\u0004Light": ["Light"], "font weight\u0004Thin": ["Thin"], "font style\u0004Italic": ["Italic"], "site exporter menu item\u0004Export": ["Export"], "Rename": ["<PERSON><PERSON>"], "An error occurred while creating the template.": ["An error occurred while creating the template."], "Here’s a detailed guide to learn how to make the most of it.": ["Here’s a detailed guide to learn how to make the most of it."], "https://wordpress.org/documentation/article/styles-overview/": ["https://wordpress.org/documentation/article/styles-overview/"], "New to block themes and styling your site?": ["New to block themes and styling your site?"], "You can adjust your blocks to ensure a cohesive experience across your site — add your unique colors to a branded Button block, or adjust the Heading block to your preferred size.": ["You can adjust your blocks to ensure a cohesive experience across your site – add your unique colours to a branded button block, or adjust the heading block to your preferred size."], "Personalize blocks": ["Personalise blocks"], "You can customize your site as much as you like with different colors, typography, and layouts. Or if you prefer, just leave it up to your theme to handle!": ["You can customise your site as much as you like with different colours, typography, and layouts. Or if you prefer, just leave it up to your theme to handle!"], "Set the design": ["Set the design"], "Tweak your site, or give it a whole new look! Get creative — how about a new color palette for your buttons, or choosing a new font? Take a look at what you can do here.": ["Tweak your site, or give it a whole new look! Get creative – how about a new colour palette for your buttons, or choosing a new font? Take a look at what you can do here."], "Welcome to Styles": ["Welcome to styles"], "styles": ["styles"], "Click <StylesIconImage /> to start designing your blocks, and choose your typography, layout, and colors.": ["Click <StylesIconImage /> to start designing your blocks, and choose your typography, layout, and colours."], "Design everything on your site — from the header right down to the footer — using blocks.": ["Design everything on your site – from the header right down to the footer – using blocks."], "Edit your site": ["Edit your site"], "Welcome to the site editor": ["Welcome to the site editor"], "Drag to resize": ["Drag to resize"], "Reset to defaults": ["Reset to defaults"], "Manage the fonts and typography used on the links.": ["Manage the fonts and typography used on the links."], "Manage the fonts used on the site.": ["Manage the fonts used on the site."], "Elements": ["Elements"], "Aa": ["Aa"], "Customize the appearance of specific blocks and for the whole site.": ["Customise the appearance of specific blocks and for the whole site."], "Customize the appearance of specific blocks for the whole site.": ["Customise the appearance of specific blocks for the whole site."], "An error occurred while creating the site export.": ["An error occurred while creating the site export."], "Remove %s": ["Remove %s"], "Sorry, you are not allowed to upload this file type.": ["Sorry, you are not allowed to upload this file type."], "Displays latest posts written by a single author.": ["Displays latest posts written by a single author."], "Create custom template": ["Create custom template"], "Custom Template": ["Custom Template"], "Shadows": ["Shadows"], "Layout": ["Layout"], "Duotone": ["Duotone"], "All templates": ["All templates"], "Templates": ["Templates"], "More": ["More"], "Captions": ["Captions"], "Appearance": ["Appearance"], "Design": ["Design"], "Open save panel": ["Open save panel"], "Patterns": ["Patterns"], "Typography": ["Typography"], "Review changes": ["Review changes"], "Buttons": ["Buttons"], "Get started": ["Get started"], "Welcome Guide": ["Welcome Guide"], "Gradient": ["Gradient"], "Move right": ["Move right"], "Move left": ["Move left"], "Posts": ["Posts"], "Details": ["Details"], "Custom Colors": ["Custom Colours"], "Scheduled": ["Scheduled"], "Create": ["Create"], "Pagination": ["Pagination"], "Heading %d": ["Heading %d"], "Embeds": ["Embeds"], "Save your changes.": ["Save your changes."], "Options": ["Options"], "Headings": ["Headings"], "Saving": ["Saving"], "%d Revision": ["%d Revision", "%d Revisions"], "Order": ["Order"], "%d result found.": ["%d result found.", "%d results found."], "Duplicate": ["Duplicate"], "Category": ["Category"], "Reset": ["Reset"], "(opens in a new tab)": ["(opens in a new tab)"], "No blocks found.": ["No blocks found."], "All Blocks": ["All Blocks"], "Blocks": ["Blocks"], "Pending": ["Pending"], "%s item": ["%s item", "%s items"], "Learn more about CSS": ["Learn more about CSS"], "Additional CSS": ["Additional CSS"], "Site Icon": ["Site Icon"], "Site Identity": ["Site Identity"], "Site Preview": ["Site Preview"], "Archive: %s": ["Archive: %s"], "Color": ["Colour"], "Font Sizes": ["Font Sizes"], "Back": ["Back"], "Trash": ["Bin"], "Table": ["Table"], "Move up": ["Move up"], "Move down": ["Move down"], "Overview": ["Overview"], "Menu": ["<PERSON><PERSON>"], "Colors": ["Colours"], "Saved": ["Saved"], "Remove": ["Remove"], "Template": ["Template"], "Navigation": ["Navigation"], "Styles": ["Styles"], "Learn more": ["Learn more"], "An error occurred while creating the item.": ["An error occurred while creating the item."], "User": ["User"], "Custom": ["Custom"], "Previous page": ["Previous page"], "Next page": ["Next page"], "Select all": ["Select all"], "Hidden": ["Hidden"], "Background": ["Background"], "List": ["List"], "Palette": ["Palette"], "Heading 6": ["Heading 6"], "Heading 5": ["Heading 5"], "Heading 4": ["Heading 4"], "Heading 3": ["Heading 3"], "Heading 2": ["Heading 2"], "Heading 1": ["Heading 1"], "Text": ["Text"], "Widgets": ["Widgets"], "Default": ["<PERSON><PERSON><PERSON>"], "Theme": ["Theme"], "(no title)": ["(no title)"], "Close": ["Close"], "Pages": ["Pages"], "Links": ["Links"], "Search": ["Search"], "Update": ["Update"], "Private": ["Private"], "Actions": ["Actions"], "Media": ["Media"], "Size": ["Size"], "Install": ["Install"], "Cancel": ["Cancel"], "Preview": ["Preview"], "Description": ["Description"], "Name": ["Name"], "Advanced": ["Advanced"], "Save": ["Save"], "Delete": ["Delete"], "Revisions": ["Revisions"], "Comments": ["Comments"], "Apply": ["Apply"], "Undo": ["Undo"], "Edit": ["Edit"], "Author": ["Author"], "Published": ["Published"], "Title": ["Title"], "Activate": ["Activate"]}}, "comment": {"reference": "wp-includes/js/dist/edit-site.js"}}