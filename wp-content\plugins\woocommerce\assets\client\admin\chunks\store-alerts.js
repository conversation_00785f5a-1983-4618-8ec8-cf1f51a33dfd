"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[1438],{46445:(e,s,o)=>{o.d(s,{A:()=>a});var t=o(5573),r=o(39793);const a=(0,r.jsx)(t.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(t.Path,{d:"M14.6 7l-1.2-1L8 12l5.4 6 1.2-1-4.6-5z"})})},45260:(e,s,o)=>{o.d(s,{A:()=>a});var t=o(5573),r=o(39793);const a=(0,r.jsx)(t.SVG,{xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 24 24",children:(0,r.jsx)(t.<PERSON>,{d:"M10.6 6L9.4 7l4.6 5-4.6 5 1.2 1 5.4-6z"})})},10837:(e,s,o)=>{o.d(s,{B:()=>t});const t=(e,s,o)=>{const t=!o||window.wcAdminFeatures.analytics;return"two_columns"===(e||s)&&t}},46189:(e,s,o)=>{o.r(s),o.d(s,{StoreAlerts:()=>B,default:()=>L});var t=o(27723),r=o(86087),a=o(56427),n=o(4921),l=o(36849),c=o(47143),i=o(76154),d=o.n(i),m=o(24148),u=o(46445),h=o(45260),_=o(47804),p=o(40314),w=o(83306),g=o(96476),x=o(12974),j=o(39793);class A extends r.Component{render(){const{hasMultipleAlerts:e,className:s}=this.props;return(0,j.jsxs)(a.Card,{className:(0,n.A)("woocommerce-store-alerts is-loading",s),"aria-hidden":!0,size:null,children:[(0,j.jsxs)(a.CardHeader,{isBorderless:!0,children:[(0,j.jsx)("span",{className:"is-placeholder"}),e&&(0,j.jsx)("span",{className:"is-placeholder"})]}),(0,j.jsx)(a.CardBody,{children:(0,j.jsxs)("div",{className:"woocommerce-store-alerts__message",children:[(0,j.jsx)("span",{className:"is-placeholder"}),(0,j.jsx)("span",{className:"is-placeholder"})]})}),(0,j.jsx)(a.CardFooter,{isBorderless:!0,children:(0,j.jsx)("span",{className:"is-placeholder"})})]})}}const v=A;A.defaultProps={hasMultipleAlerts:!1};var b=o(56109),N=o(24060),f=o(10837),y=o(99915);const S={page:1,per_page:p.QUERY_DEFAULTS.pageSize,type:"error,update",status:"unactioned"};function C(e){return(e||[]).filter((e=>"unactioned"===e.status&&!0!==e.is_deleted))}const B=()=>{const[e,s]=(0,r.useState)(0),{alerts:o=[],isLoading:i,defaultHomescreenLayout:A}=(0,c.useSelect)((e=>{const{getNotes:s,hasFinishedResolution:o}=e(p.notesStore),{getOption:t}=e(p.optionsStore);return{alerts:C(s(S)),isLoading:!o("getNotes",[S]),defaultHomescreenLayout:t("woocommerce_default_homepage_layout")||"single_column"}})),{triggerNoteAction:B,updateNote:L,removeNote:k}=(0,c.useDispatch)(p.notesStore),{createNotice:z}=(0,c.useDispatch)("core/notices"),M=(0,p.useUserPreferences)();function T(o){o?.stopPropagation(),e>0&&s(e-1)}const F=(0,b.Qk)("alertCount",0,(e=>parseInt(e,10))),P=(0,g.isWCAdmin)(),E=P&&"homescreen"===(0,g.getScreenFromPath)(),D=(0,f.B)(M.homepage_layout,A,(0,y.EM)("setup"));if(F>0&&i)return(0,j.jsx)(v,{className:(0,n.A)({"is-wc-admin-page":P,"is-homescreen":E,"two-columns":D&&E}),hasMultipleAlerts:F>1});if(0===o.length)return null;const H=o.length,R=o[e],U=R.type,W=(0,n.A)("woocommerce-store-alerts",{"is-alert-error":"error"===U,"is-alert-update":"update"===U,"is-wc-admin-page":P,"is-homescreen":E,"two-columns":D&&E});return(0,j.jsxs)(a.Card,{className:W,size:null,children:[(0,j.jsxs)(a.CardHeader,{className:"woocommerce-store-alerts__header",isBorderless:!0,children:[(0,j.jsx)("span",{className:"woocommerce-store-alerts__title",children:R.title}),H>1&&(0,j.jsxs)("div",{className:"woocommerce-store-alerts__pagination",children:[(0,j.jsx)("span",{className:"woocommerce-store-alerts__pagination-label",role:"status","aria-live":"polite",children:(0,l.A)({mixedString:(0,t.__)("{{current /}} of {{total /}}","woocommerce"),components:{current:(0,j.jsx)(r.Fragment,{children:e+1}),total:(0,j.jsx)(r.Fragment,{children:H})}})}),(0,j.jsx)(a.Button,{onClick:T,disabled:0===e,label:(0,t.__)("Previous Alert","woocommerce"),children:(0,j.jsx)(m.A,{icon:u.A,className:"arrow-left-icon"})}),(0,j.jsx)(a.Button,{onClick:function(t){t.stopPropagation(),e<o.length-1&&s(e+1)},disabled:H-1===e,label:(0,t.__)("Next Alert","woocommerce"),children:(0,j.jsx)(m.A,{icon:h.A,className:"arrow-right-icon"})})]}),(0,j.jsx)(a.Button,{className:"woocommerce-store-alerts__close",onClick:()=>(async e=>{const s=(0,N.s9)();(0,w.recordEvent)("inbox_action_dismiss",{note_name:e.name,note_title:e.title,note_name_dismiss_all:!1,note_name_dismiss_confirmation:!0,screen:s});const o=e.id;try{await k(o),z("success",(0,t.__)("Message dismissed","woocommerce"))}catch(e){z("error",(0,t._n)("Message could not be dismissed","Messages could not be dismissed",1,"woocommerce"))}})(R),children:(0,j.jsx)(m.A,{width:"18",height:"18",icon:_.A})})]}),(0,j.jsx)(a.CardBody,{children:(0,j.jsx)("div",{className:"woocommerce-store-alerts__message",dangerouslySetInnerHTML:(0,x.Ay)(R.content)})}),(0,j.jsx)(a.CardFooter,{isBorderless:!0,children:function(e){const s=e.actions.map(((s,o)=>{const r=0===o?"secondary":"tertiary";return(0,j.jsx)(a.Button,{variant:r,href:s.url||void 0,onClick:async o=>{const r=o.currentTarget.getAttribute("href");o.preventDefault(),T();try{await B(e.id,s.id),r&&"#"!==r&&(0,g.parseAdminUrl)(r).href!==window.location.href&&(0,g.navigateTo)({url:r})}catch(e){throw z("error",(0,t.__)("Something went wrong while triggering this note's action.","woocommerce")),e}},children:s.label},`${e.id}-${s.label}-${s.name}`)})),o=[{value:d()().add(4,"hours").unix().toString(),label:(0,t.__)("Later Today","woocommerce")},{value:d()().add(1,"day").hour(9).minute(0).second(0).millisecond(0).unix().toString(),label:(0,t.__)("Tomorrow","woocommerce")},{value:d()().add(1,"week").hour(9).minute(0).second(0).millisecond(0).unix().toString(),label:(0,t.__)("Next Week","woocommerce")},{value:d()().add(1,"month").hour(9).minute(0).second(0).millisecond(0).unix().toString(),label:(0,t.__)("Next Month","woocommerce")}],r=e.is_snoozable&&(0,j.jsx)(a.SelectControl,{className:"woocommerce-store-alerts__snooze",options:[{label:(0,t.__)("Remind Me Later","woocommerce"),value:"0"},...o],onChange:s=>{if("0"===s)return;const t=o.find((e=>e.value===s));(s=>{L(e.id,{status:"snoozed",date_reminder:s.value});const o={alert_name:e.name,alert_title:e.title,snooze_duration:s.value,snooze_label:s.label};(0,w.recordEvent)("store_alert_snooze",o)})({value:s,label:t&&t.label})}});if(s||r)return(0,j.jsxs)("div",{className:"woocommerce-store-alerts__actions",children:[s,r]})}(R)})]})},L=B},12974:(e,s,o)=>{o.d(s,{Ay:()=>n});var t=o(13240);const r=["a","b","em","i","strong","p","br"],a=["target","href","rel","name","download"],n=e=>({__html:(0,t.sanitize)(e,{ALLOWED_TAGS:r,ALLOWED_ATTR:a})})}}]);