<?php return array('dependencies' => array('lodash', 'moment', 'react', 'react-dom', 'wc-admin-layout', 'wc-components', 'wc-csv', 'wc-currency', 'wc-customer-effort-score', 'wc-date', 'wc-experimental', 'wc-explat', 'wc-navigation', 'wc-notices', 'wc-number', 'wc-product-editor', 'wc-remote-logging', 'wc-settings', 'wc-store-data', 'wc-tracks', 'wp-a11y', 'wp-api-fetch', 'wp-blob', 'wp-block-editor', 'wp-block-library', 'wp-blocks', 'wp-components', 'wp-compose', 'wp-core-data', 'wp-data', 'wp-data-controls', 'wp-date', 'wp-deprecated', 'wp-dom', 'wp-editor', 'wp-element', 'wp-hooks', 'wp-html-entities', 'wp-i18n', 'wp-keyboard-shortcuts', 'wp-keycodes', 'wp-media-utils', 'wp-notices', 'wp-plugins', 'wp-preferences', 'wp-primitives', 'wp-private-apis', 'wp-router', 'wp-url', 'wp-warning'), 'version' => '27d5d92e7587754e8ba1');
