(()=>{"use strict";var e={n:t=>{var o=t&&t.__esModule?()=>t.default:()=>t;return e.d(o,{a:o}),o},d:(t,o)=>{for(var n in o)e.o(o,n)&&!e.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:o[n]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t)};const t=window.wp.i18n,o=window.wp.data,n=window.wp.domReady;var a=e.n(n);const c=window.wc.wcSettings,i=e=>e&&!e.disabled?new Promise((e=>{window.requestAnimationFrame(e)})).then((()=>i(e))):Promise.resolve(!0),r=()=>{const e=document.querySelector(".woocommerce-save-button");e.classList.contains("has-tax")||i(e).then((()=>{document.querySelector(".wc_tax_rates .tips")&&(e.classList.add("has-tax"),(0,o.dispatch)("core/notices").createSuccessNotice((0,t.__)("You’ve added your first tax rate!","woocommerce"),{id:"WOOCOMMERCE_ONBOARDING_TAX_NOTICE",actions:[{url:(0,c.getAdminLink)("admin.php?page=wc-admin"),label:(0,t.__)("Continue setup.","woocommerce")}]}))}))};a()((()=>{const e=document.querySelector(".woocommerce-save-button");window.htmlSettingsTaxLocalizeScript&&window.htmlSettingsTaxLocalizeScript.rates&&!window.htmlSettingsTaxLocalizeScript.rates.length&&e&&e.addEventListener("click",r)})),(window.wc=window.wc||{}).onboardingTaxNotice={}})();