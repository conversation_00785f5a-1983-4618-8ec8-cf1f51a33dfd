/*! For license information please see product-category-metabox.js.LICENSE.txt */
(()=>{"use strict";var e,r,t={27655:(e,r,t)=>{t.d(r,{_z:()=>n,mL:()=>o});const o="product_cat";function n(e){return e?Array.from(e.querySelectorAll(":scope > input[type=hidden]")).map((e=>{const r=(t=e)&&t.dataset&&t.dataset.name?{term_id:parseInt(t.value,10),name:t.dataset.name}:null;var t;return e.remove(),r})):[]}},94931:(e,r,t)=>{var o=t(51609),n=Symbol.for("react.element"),a=Symbol.for("react.fragment"),i=Object.prototype.hasOwnProperty,c=o.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,s={key:!0,ref:!0,__self:!0,__source:!0};function p(e,r,t){var o,a={},p=null,l=null;for(o in void 0!==t&&(p=""+t),void 0!==r.key&&(p=""+r.key),void 0!==r.ref&&(l=r.ref),r)i.call(r,o)&&!s.hasOwnProperty(o)&&(a[o]=r[o]);if(e&&e.defaultProps)for(o in r=e.defaultProps)void 0===a[o]&&(a[o]=r[o]);return{$$typeof:n,type:e,key:p,ref:l,props:a,_owner:c.current}}r.Fragment=a,r.jsx=p,r.jsxs=p},39793:(e,r,t)=>{e.exports=t(94931)},51609:e=>{e.exports=window.React},98846:e=>{e.exports=window.wc.components},40314:e=>{e.exports=window.wc.data},83306:e=>{e.exports=window.wc.tracks},15703:e=>{e.exports=window.wc.wcSettings},1455:e=>{e.exports=window.wp.apiFetch},29491:e=>{e.exports=window.wp.compose},86087:e=>{e.exports=window.wp.element},27723:e=>{e.exports=window.wp.i18n},93832:e=>{e.exports=window.wp.url}},o={};function n(e){var r=o[e];if(void 0!==r)return r.exports;var a=o[e]={exports:{}};return t[e](a,a.exports,n),a.exports}n.m=t,n.n=e=>{var r=e&&e.__esModule?()=>e.default:()=>e;return n.d(r,{a:r}),r},n.d=(e,r)=>{for(var t in r)n.o(r,t)&&!n.o(e,t)&&Object.defineProperty(e,t,{enumerable:!0,get:r[t]})},n.f={},n.e=e=>Promise.all(Object.keys(n.f).reduce(((r,t)=>(n.f[t](e,r),r)),[])),n.u=e=>"chunks/category-metabox.js?ver=d28c25fa1719d9f7c19d",n.miniCssF=e=>{},n.g=function(){if("object"==typeof globalThis)return globalThis;try{return this||new Function("return this")()}catch(e){if("object"==typeof window)return window}}(),n.o=(e,r)=>Object.prototype.hasOwnProperty.call(e,r),e={},r="__wcAdmin_webpackJsonp:",n.l=(t,o,a,i)=>{if(e[t])e[t].push(o);else{var c,s;if(void 0!==a)for(var p=document.getElementsByTagName("script"),l=0;l<p.length;l++){var d=p[l];if(d.getAttribute("src")==t||d.getAttribute("data-webpack")==r+a){c=d;break}}c||(s=!0,(c=document.createElement("script")).charset="utf-8",c.timeout=120,n.nc&&c.setAttribute("nonce",n.nc),c.setAttribute("data-webpack",r+a),c.src=t),e[t]=[o];var u=(r,o)=>{c.onerror=c.onload=null,clearTimeout(w);var n=e[t];if(delete e[t],c.parentNode&&c.parentNode.removeChild(c),n&&n.forEach((e=>e(o))),r)return r(o)},w=setTimeout(u.bind(null,void 0,{type:"timeout",target:c}),12e4);c.onerror=u.bind(null,c.onerror),c.onload=u.bind(null,c.onload),s&&document.head.appendChild(c)}},n.r=e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},(()=>{var e;n.g.importScripts&&(e=n.g.location+"");var r=n.g.document;if(!e&&r&&(r.currentScript&&"SCRIPT"===r.currentScript.tagName.toUpperCase()&&(e=r.currentScript.src),!e)){var t=r.getElementsByTagName("script");if(t.length)for(var o=t.length-1;o>-1&&(!e||!/^http(s?):/.test(e));)e=t[o--].src}if(!e)throw new Error("Automatic publicPath is not supported in this browser");e=e.replace(/#.*$/,"").replace(/\?.*$/,"").replace(/\/[^\/]+$/,"/"),n.p=e+"../"})(),(()=>{var e={1164:0};n.f.j=(r,t)=>{var o=n.o(e,r)?e[r]:void 0;if(0!==o)if(o)t.push(o[2]);else{var a=new Promise(((t,n)=>o=e[r]=[t,n]));t.push(o[2]=a);var i=n.p+n.u(r),c=new Error;n.l(i,(t=>{if(n.o(e,r)&&(0!==(o=e[r])&&(e[r]=void 0),o)){var a=t&&("load"===t.type?"missing":t.type),i=t&&t.target&&t.target.src;c.message="Loading chunk "+r+" failed.\n("+a+": "+i+")",c.name="ChunkLoadError",c.type=a,c.request=i,o[1](c)}}),"chunk-"+r,r)}};var r=(r,t)=>{var o,a,[i,c,s]=t,p=0;if(i.some((r=>0!==e[r]))){for(o in c)n.o(c,o)&&(n.m[o]=c[o]);s&&s(n)}for(r&&r(t);p<i.length;p++)a=i[p],n.o(e,a)&&e[a]&&e[a][0](),e[a]=0},t=globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[];t.forEach(r.bind(null,0)),t.push=r.bind(null,t.push.bind(t))})();var a={};n.r(a);var i=n(86087),c=n(27655),s=n(39793);const p=(0,i.lazy)((()=>n.e(6666).then(n.bind(n,57173)))),l=document.querySelector("#taxonomy-product_cat-metabox");if(l){const e=(0,c._z)(l.parentElement);(0,i.createRoot)(l).render((0,s.jsx)(i.Suspense,{fallback:null,children:(0,s.jsx)(p,{initialSelected:e})}),l)}(window.wc=window.wc||{}).productCategoryMetabox=a})();