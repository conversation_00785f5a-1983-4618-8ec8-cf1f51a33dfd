(()=>{"use strict";var e={d:(t,n)=>{for(var i in n)e.o(n,i)&&!e.o(t,i)&&Object.defineProperty(t,i,{enumerable:!0,get:n[i]})},o:(e,t)=>Object.prototype.hasOwnProperty.call(e,t),r:e=>{"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})}},t={};e.r(t),e.d(t,{createErrorNotice:()=>f,createInfoNotice:()=>l,createNotice:()=>a,createSuccessNotice:()=>d,createWarningNotice:()=>p,removeNotice:()=>w});var n={};e.r(n),e.d(n,{getNotices:()=>b}),window.wp.notices;const i=window.wp.data,o=window.lodash,r=(u="context",e=>(t={},n)=>{const i=n[u];if(void 0===i)return t;const o=e(t[i],n);return o===t[i]?t:{...t,[i]:o}})(((e=[],t)=>{switch(t.type){case"CREATE_NOTICE":return[...(0,o.reject)(e,{id:t.notice.id}),t.notice];case"REMOVE_NOTICE":return(0,o.reject)(e,{id:t.id})}return e})),c="global",s="info";var u;function a(e=s,t,n={}){const{speak:i=!0,isDismissible:r=!0,context:u=c,id:a=(0,o.uniqueId)(u),actions:d=[],type:l="default",__unstableHTML:f,icon:p=null,explicitDismiss:w=!1,onDismiss:y=null}=n;return{type:"CREATE_NOTICE",context:u,notice:{id:a,status:e,content:t=String(t),spokenMessage:i?t:null,__unstableHTML:f,isDismissible:r,actions:d,type:l,icon:p,explicitDismiss:w,onDismiss:y}}}function d(e,t){return a("success",e,t)}function l(e,t){return a("info",e,t)}function f(e,t){return a("error",e,t)}function p(e,t){return a("warning",e,t)}function w(e,t=c){return{type:"REMOVE_NOTICE",id:e,context:t}}const y=[];function b(e,t=c){return e[t]||y}(0,i.registerStore)("core/notices2",{reducer:r,actions:t,selectors:n}),(window.wc=window.wc||{}).notices={}})();