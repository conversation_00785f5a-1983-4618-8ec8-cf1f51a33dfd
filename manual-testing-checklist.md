# 🧪 Manual Testing Checklist for Film Collectables Competition Website

## **Pre-Testing Setup**
- [ ] Ensure test environment is set up with fresh data
- [ ] Create test user accounts with verified emails
- [ ] Set up test competitions with different configurations
- [ ] Verify payment gateway is in test mode
- [ ] Clear browser cache and cookies

---

## **1. User Registration & Authentication**

### Registration Process
- [ ] Register new user with all required fields
- [ ] Verify phone number validation (UK format)
- [ ] Test date of birth validation (18+ requirement)
- [ ] Check email verification email is sent
- [ ] Verify email verification link works
- [ ] Test registration with duplicate email/phone
- [ ] Test registration with invalid data

### Login Process
- [ ] Login with verified account
- [ ] Login with unverified account
- [ ] Test "Remember Me" functionality
- [ ] Test password reset flow
- [ ] Test account lockout after failed attempts

---

## **2. Competition Browsing & Display**

### Competition Listings
- [ ] View active competitions on homepage
- [ ] Check competition cards display correctly
- [ ] Verify countdown timers are accurate
- [ ] Test pagination on competition listings
- [ ] Check "Awaiting Draw" page displays correctly
- [ ] Check "Completed" page shows winners

### Individual Competition Pages
- [ ] Competition details display correctly
- [ ] Image gallery works (lightbox, thumbnails)
- [ ] Countdown timer updates in real-time
- [ ] Progress bar shows correct percentage
- [ ] Instant win information displays (if enabled)
- [ ] Recent winners section shows correctly
- [ ] Skill question displays properly

---

## **3. Ticket Purchasing Workflow**

### Pre-Purchase Validation
- [ ] Cannot purchase without answering skill question
- [ ] Cannot purchase with unverified email
- [ ] Correct answer validation works
- [ ] Incorrect answer shows appropriate message
- [ ] Ticket quantity slider works (1-999 range)
- [ ] Price calculation updates correctly

### Purchase Process
- [ ] Add tickets to cart successfully
- [ ] Cart displays competition items correctly
- [ ] Proceed to checkout without redirect issues
- [ ] Apply wallet credit at checkout (optional)
- [ ] Complete purchase with test payment
- [ ] Receive order confirmation email

### Post-Purchase Verification
- [ ] Tickets generated within 3 seconds
- [ ] Correct number of tickets assigned
- [ ] Ticket numbers are unique and sequential
- [ ] Order confirmation page displays tickets
- [ ] Instant wins processed correctly (if applicable)
- [ ] Competition stock updated correctly

---

## **4. Competition Status Management**

### Active Competitions
- [ ] New competitions start correctly at scheduled time
- [ ] Ticket sales work during active period
- [ ] Competition displays as "Active" status
- [ ] End date countdown works correctly

### Sellout Detection
- [ ] Competition automatically moves to "Awaiting Draw" when sold out
- [ ] No more tickets can be purchased after sellout
- [ ] Stock status updates to "Out of Stock"
- [ ] Competition card shows "Sold Out" status

### End Date Handling
- [ ] Competition moves to "Awaiting Draw" after end date
- [ ] Ticket sales stop after end date
- [ ] Countdown timer shows "Ended" message

---

## **5. Instant Win System**

### Configuration Testing
- [ ] Instant wins can be enabled/disabled per competition
- [ ] Winning ticket numbers can be set
- [ ] Prize types (site credit/physical) work correctly
- [ ] Prize values are validated

### Instant Win Processing
- [ ] Instant wins trigger immediately after purchase
- [ ] Winning tickets display on order confirmation
- [ ] Site credit is added to user wallet
- [ ] Physical prizes are recorded correctly
- [ ] Multiple instant wins per order work
- [ ] Instant win notifications display correctly

---

## **6. Wallet Credit System**

### Credit Management
- [ ] Wallet balance displays correctly in account
- [ ] Credit can be applied at checkout
- [ ] Partial credit application works
- [ ] Credit application is optional (not automatic)
- [ ] Transaction history is recorded

### Admin Management
- [ ] Admin can add credit to user accounts
- [ ] Admin can subtract credit from accounts
- [ ] Admin can set specific balance amounts
- [ ] Bulk credit operations work
- [ ] Transaction logs are maintained

---

## **7. User Account Features**

### My Account Dashboard
- [ ] Account dashboard loads correctly
- [ ] User details can be updated
- [ ] Password change functionality works
- [ ] Email change requires verification

### My Tickets Section
- [ ] All purchased tickets display correctly
- [ ] Tickets grouped by competition
- [ ] Ticket status shows correctly
- [ ] Order details are accessible

### Wins Section
- [ ] Competition wins display correctly
- [ ] Instant wins show separately
- [ ] Win dates and details are accurate
- [ ] Prize information is complete

---

## **8. Admin Dashboard Testing**

### Competition Management
- [ ] Create new competition products
- [ ] Configure competition settings
- [ ] Set skill questions and answers
- [ ] Enable/disable instant wins
- [ ] Schedule competition dates

### Winner Selection
- [ ] Export ticket data to CSV
- [ ] CSV format is correct for external randomizers
- [ ] Manual winner selection works
- [ ] Winner details auto-populate
- [ ] Winner announcement updates competition

### Data Management
- [ ] View competition statistics
- [ ] Export user data
- [ ] Manage wallet balances
- [ ] Clear test data functionality

---

## **9. Email System Testing**

### Registration Emails
- [ ] Welcome email sent after registration
- [ ] Email verification link works
- [ ] Email templates display correctly
- [ ] Unsubscribe links work

### Order Confirmation Emails
- [ ] Order confirmation sent after purchase
- [ ] Ticket details included in email
- [ ] Instant win notifications included
- [ ] Email formatting is correct

### Newsletter System
- [ ] Mailing list signup works
- [ ] Opt-in/opt-out preferences respected
- [ ] Newsletter emails send correctly
- [ ] Unsubscribe functionality works

---

## **10. Mobile Responsiveness**

### Mobile Navigation
- [ ] Burger menu works on mobile
- [ ] Navigation slides out correctly
- [ ] All menu items accessible
- [ ] Search functionality works

### Competition Pages
- [ ] Competition cards display correctly on mobile
- [ ] Image galleries work on touch devices
- [ ] Ticket purchasing form is usable
- [ ] Countdown timers display properly

### Checkout Process
- [ ] Mobile checkout is user-friendly
- [ ] Payment forms work on mobile
- [ ] Order confirmation displays correctly
- [ ] Account pages are mobile-optimized

---

## **11. Performance Testing**

### Page Load Times
- [ ] Homepage loads within 3 seconds
- [ ] Competition pages load quickly
- [ ] Image galleries load efficiently
- [ ] Checkout process is responsive

### Concurrent Users
- [ ] Multiple users can purchase tickets simultaneously
- [ ] No duplicate ticket numbers assigned
- [ ] Database handles concurrent transactions
- [ ] Site remains stable under load

---

## **12. Security Testing**

### Input Validation
- [ ] SQL injection protection works
- [ ] XSS protection is effective
- [ ] CSRF tokens are validated
- [ ] File upload restrictions work

### Access Control
- [ ] Admin areas require proper permissions
- [ ] User data is properly isolated
- [ ] Payment data is secure
- [ ] Session management is secure

---

## **13. Browser Compatibility**

### Desktop Browsers
- [ ] Chrome (latest version)
- [ ] Firefox (latest version)
- [ ] Safari (latest version)
- [ ] Edge (latest version)

### Mobile Browsers
- [ ] Chrome Mobile
- [ ] Safari Mobile
- [ ] Samsung Internet
- [ ] Firefox Mobile

---

## **14. Error Handling**

### User-Facing Errors
- [ ] Friendly error messages display
- [ ] 404 pages are customized
- [ ] Payment failures are handled gracefully
- [ ] Form validation errors are clear

### System Errors
- [ ] Database connection failures handled
- [ ] Payment gateway errors logged
- [ ] Email sending failures logged
- [ ] File system errors handled

---

## **Testing Sign-Off**

### Pre-Launch Checklist
- [ ] All critical bugs fixed
- [ ] Performance meets requirements
- [ ] Security vulnerabilities addressed
- [ ] Mobile experience optimized
- [ ] Payment processing tested thoroughly
- [ ] Email deliverability confirmed
- [ ] Backup and recovery tested
- [ ] Monitoring and logging configured

### Final Approval
- [ ] **Functional Testing Complete** - Signed: _____________ Date: _______
- [ ] **Performance Testing Complete** - Signed: _____________ Date: _______
- [ ] **Security Testing Complete** - Signed: _____________ Date: _______
- [ ] **User Acceptance Testing Complete** - Signed: _____________ Date: _______

---

**Notes:**
- Document any issues found during testing
- Retest after fixes are implemented
- Keep testing logs for future reference
- Update checklist based on new features
