{"$schema": "https://schemas.wp.org/trunk/block.json", "apiVersion": 3, "name": "woocommerce/product-custom-fields", "title": "Product custom fields control", "category": "woocommerce", "description": "The product custom fields.", "keywords": ["products", "custom", "fields"], "textdomain": "default", "attributes": {"name": {"type": "string", "role": "content"}}, "supports": {"align": false, "html": false, "multiple": true, "reusable": true, "inserter": false, "lock": false, "__experimentalToolbar": false}}