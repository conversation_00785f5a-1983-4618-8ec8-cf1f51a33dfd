(globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp=globalThis.webpackChunkwebpackWcBlocksCartCheckoutFrontendJsonp||[]).push([[4037],{9184:(e,s,t)=>{"use strict";t.d(s,{A:()=>l});var d=t(7723),i=t(3993),r=t(5703),n=t(2785),o=t(1069),a=t(8537),c=(t(8796),t(790));const l=({address:e,onEdit:s,target:t,isExpanded:l})=>{const p=(0,r.getSetting)("countryData",{});let m=(0,r.getSetting)("defaultAddressFormat","{name}\n{company}\n{address_1}\n{address_2}\n{city}\n{state}\n{postcode}\n{country}");(0,i.objectHasProp)(p,e?.country)&&(0,i.objectHasProp)(p[e.country],"format")&&(0,i.isString)(p[e.country].format)&&(m=p[e.country].format);const{name:h,address:g}=(0,n.M0)(e,m),u="shipping"===t?(0,d.__)("Edit shipping address","woocommerce"):(0,d.__)("Edit billing address","woocommerce");return(0,c.jsxs)("div",{className:"wc-block-components-address-card",children:[(0,c.jsxs)("address",{children:[(0,c.jsx)("span",{className:"wc-block-components-address-card__address-section",children:(0,a.decodeEntities)(h)}),(0,c.jsx)("div",{className:"wc-block-components-address-card__address-section",children:g.filter((e=>!!e)).map(((e,s)=>(0,c.jsx)("span",{children:(0,a.decodeEntities)(e)},"address-"+s)))}),e.phone?(0,c.jsx)("div",{className:"wc-block-components-address-card__address-section",children:e.phone},"address-phone"):""]}),s&&(0,c.jsx)(o.$,{render:(0,c.jsx)("span",{}),className:"wc-block-components-address-card__edit","aria-controls":t,"aria-expanded":l,"aria-label":u,onClick:e=>{e.preventDefault(),s()},type:"button",children:(0,d.__)("Edit","woocommerce")})]})}},7403:(e,s,t)=>{"use strict";t.d(s,{A:()=>r});var d=t(4921),i=(t(1121),t(790));const r=({isEditing:e=!1,addressCard:s,addressForm:t})=>{const r=(0,d.A)("wc-block-components-address-address-wrapper",{"is-editing":e});return(0,i.jsxs)("div",{className:r,children:[(0,i.jsx)("div",{className:"wc-block-components-address-card-wrapper",children:s}),(0,i.jsx)("div",{className:"wc-block-components-address-form-wrapper",children:t})]})}},5299:(e,s,t)=>{"use strict";t.d(s,{A:()=>i});var d=t(7723);const i=({defaultTitle:e=(0,d.__)("Step","woocommerce"),defaultDescription:s=(0,d.__)("Step description text.","woocommerce"),defaultShowStepNumber:t=!0})=>({title:{type:"string",default:e},description:{type:"string",default:s},showStepNumber:{type:"boolean",default:t}})},4600:(e,s,t)=>{"use strict";t.r(s),t.d(s,{default:()=>B});var d=t(4921),i=t(1616),r=t(4656),n=t(7792),o=t(7143),a=t(7594),c=t(6087),l=t(5929),p=t(7370),m=t(8696),h=t(3001),g=t(814),u=t(7052),b=t(8331),_=t(7403),S=t(9184),w=t(790);const f=()=>{const{billingAddress:e,setShippingAddress:s,setBillingAddress:t,useBillingAsShipping:d,editingBillingAddress:i,setEditingBillingAddress:r}=(0,n.C)(),{dispatchCheckoutEvent:l}=(0,u.y)(),{hasValidationErrors:p,getValidationErrorSelector:m}=(0,o.useSelect)((e=>{const s=e(a.validationStore);return{hasValidationErrors:s.hasValidationErrors(),getValidationErrorSelector:s.getValidationError}}),[]),h=(0,c.useMemo)((()=>Object.keys(e).filter((e=>"email"!==e&&void 0!==m("billing_"+e))).filter(Boolean)),[e,m]);(0,c.useEffect)((()=>{h.length>0&&!1===i&&r(!0)}),[i,p,h.length,r]);const f=(0,c.useCallback)((e=>{t(e),d&&(s(e),l("set-shipping-address")),l("set-billing-address")}),[l,t,s,d]);return(0,w.jsx)(_.A,{isEditing:i,addressCard:(0,w.jsx)(S.A,{address:e,target:"billing",onEdit:()=>{r(!0)},isExpanded:i}),addressForm:(0,w.jsx)(g.l,{id:"billing",addressType:"billing",onChange:f,values:e,fields:b.Hw,isEditing:i})})},E=()=>{const{defaultFields:e,billingAddress:s,setShippingAddress:t,useBillingAsShipping:d}=(0,n.C)(),{isEditor:i}=(0,p.m)();(0,l.Su)((()=>{if(d){const{email:d,...i}=s,r={...i};e?.phone?.hidden&&delete r.phone,e?.company?.hidden&&delete r.company,t(r)}}));const g=i?h.A:c.Fragment,u=d?[m.tG.BILLING_ADDRESS,m.tG.SHIPPING_ADDRESS]:[m.tG.BILLING_ADDRESS],{cartDataLoaded:b}=(0,o.useSelect)((e=>({cartDataLoaded:e(a.cartStore).hasFinishedResolution("getCartData")})));return(0,w.jsxs)(w.Fragment,{children:[(0,w.jsx)(r.StoreNoticesContainer,{context:u}),(0,w.jsx)(g,{children:b?(0,w.jsx)(f,{}):null})]})};var k=t(5299),x=t(7723);const A=(0,x.__)("Billing address","woocommerce"),j=(0,x.__)("Enter the billing address that matches your payment method.","woocommerce"),y=(0,x.__)("Billing and shipping address","woocommerce"),v=(0,x.__)("Enter the billing and shipping address that matches your payment method.","woocommerce"),N={...(0,k.A)({defaultTitle:A,defaultDescription:j}),className:{type:"string",default:""},lock:{type:"object",default:{move:!0,remove:!0}}};var C=t(4199);const B=(0,i.withFilteredAttributes)(N)((({title:e,description:s,children:t,className:i})=>{const{showFormStepNumbers:c}=(0,C.O)(),l=(0,o.useSelect)((e=>e(a.checkoutStore).isProcessing())),{showBillingFields:p,forcedBillingAddress:m,useBillingAsShipping:h}=(0,n.C)();return p||h?(e=((e,s)=>s?e===A?y:e:e===y?A:e)(e,m),s=((e,s)=>s?e===j?v:e:e===v?j:e)(s,m),(0,w.jsxs)(r.FormStep,{id:"billing-fields",disabled:l,className:(0,d.A)("wc-block-checkout__billing-fields",i),title:e,description:s,showStepNumber:c,children:[(0,w.jsx)(E,{}),t]})):null}))},8796:()=>{},1121:()=>{}}]);