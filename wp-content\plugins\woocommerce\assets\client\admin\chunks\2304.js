"use strict";(globalThis.webpackChunk_wcAdmin_webpackJsonp=globalThis.webpackChunk_wcAdmin_webpackJsonp||[]).push([[2304],{55737:(e,t,r)=>{r.d(t,{A:()=>h});var o=r(27723),a=r(86087),l=r(29491),c=r(38443),s=r(47143),i=r(66087),n=r(98846),m=r(40314),u=r(77374),d=r(94111),p=r(96476);function _(e,t,r={}){if(!e||0===e.length)return null;const o=e.slice(0),a=o.pop();if(a.showFilters(t,r)){const e=(0,p.flattenFilters)(a.filters),r=t[a.param]||a.defaultValue||"all";return(0,i.find)(e,{value:r})}return _(o,t,r)}function f(e){return t=>(0,c.format)(e,t)}function y(e){if(e?.data?.intervals?.length>1){const t=e.data.intervals[0].date_start,r=e.data.intervals[e.data.intervals.length-1].date_end;if((0,u.containsLeapYear)(t,r))return!0}return!1}var w=r(39793);class g extends a.Component{shouldComponentUpdate(e){return e.isRequesting!==this.props.isRequesting||e.primaryData.isRequesting!==this.props.primaryData.isRequesting||e.secondaryData.isRequesting!==this.props.secondaryData.isRequesting||!(0,i.isEqual)(e.query,this.props.query)}getItemChartData(){const{primaryData:e,selectedChart:t}=this.props;return e.data.intervals.map((function(e){const r={};return e.subtotals.segments.forEach((function(e){if(e.segment_label){const o=r[e.segment_label]?e.segment_label+" (#"+e.segment_id+")":e.segment_label;r[e.segment_id]={label:o,value:e.subtotals[t.key]||0}}})),{date:(0,c.format)("Y-m-d\\TH:i:s",e.date_start),...r}}))}getTimeChartData(){const{query:e,primaryData:t,secondaryData:r,selectedChart:o,defaultDateRange:a}=this.props,l=(0,u.getIntervalForQuery)(e,a),{primary:s,secondary:i}=(0,u.getCurrentDates)(e,a);return function(e,t,r,o,a,l,s){const i=y(e),n=y(t),m=[...e.data.intervals],d=[...t.data.intervals],p=[];for(let e=0;e<m.length;e++){const t=m[e],_=(0,c.format)("Y-m-d\\TH:i:s",t.date_start),f=`${r.label} (${r.range})`,y=t.date_start,w=t.subtotals[l]||0,g=d[e],h=`${o.label} (${o.range})`;let v=(0,u.getPreviousDate)(t.date_start,r.after,o.after,a,s).format("YYYY-MM-DD HH:mm:ss"),b=g&&g.subtotals[l]||0;if("day"===s&&i&&!n&&d?.[e]){const r=new Date(t.date_start),o=new Date(d[e].date_start);(0,u.isLeapYear)(r.getFullYear())&&1===r.getMonth()&&29===r.getDate()&&2===o.getMonth()&&1===o.getDate()&&(v="-",b=0,d.splice(e,0,d[e]))}p.push({date:_,primary:{label:f,labelDate:y,value:w},secondary:{label:h,labelDate:v,value:b}})}return p}(t,r,s,i,e.compare,o.key,l)}getTimeChartTotals(){const{primaryData:e,secondaryData:t,selectedChart:r}=this.props;return{primary:(0,i.get)(e,["data","totals",r.key],null),secondary:(0,i.get)(t,["data","totals",r.key],null)}}renderChart(e,t,r,a){const{emptySearchResults:l,filterParam:c,interactiveLegend:s,itemsLabel:i,legendPosition:d,path:p,query:_,selectedChart:y,showHeaderControls:g,primaryData:h,defaultDateRange:v}=this.props,b=(0,u.getIntervalForQuery)(_,v),C=(0,u.getAllowedIntervalsForQuery)(_,v),S=(0,u.getDateFormatsForInterval)(b,h.data.intervals.length,{type:"php"}),x=l?(0,o.__)("No data for the current search","woocommerce"):(0,o.__)("No data for the selected date range","woocommerce"),{formatAmount:D,getCurrencyConfig:R}=this.context;return(0,w.jsx)(n.Chart,{allowedIntervals:C,data:r,dateParser:"%Y-%m-%dT%H:%M:%S",emptyMessage:x,filterParam:c,interactiveLegend:s,interval:b,isRequesting:t,itemsLabel:i,legendPosition:d,legendTotals:a,mode:e,path:p,query:_,screenReaderFormat:f(S.screenReaderFormat),showHeaderControls:g,title:y.label,tooltipLabelFormat:f(S.tooltipLabelFormat),tooltipTitle:"time-comparison"===e&&y.label||null,tooltipValueFormat:(0,m.getTooltipValueFormat)(y.type,D),chartType:(0,u.getChartTypeForQuery)(_),valueType:y.type,xFormat:f(S.xFormat),x2Format:f(S.x2Format),currency:R()})}renderItemComparison(){const{isRequesting:e,primaryData:t}=this.props;if(t.isError)return(0,w.jsx)(n.AnalyticsError,{});const r=e||t.isRequesting,o=this.getItemChartData();return this.renderChart("item-comparison",r,o)}renderTimeComparison(){const{isRequesting:e,primaryData:t,secondaryData:r}=this.props;if(!t||t.isError||r.isError)return(0,w.jsx)(n.AnalyticsError,{});const o=e||t.isRequesting||r.isRequesting,a=this.getTimeChartData(),l=this.getTimeChartTotals();return this.renderChart("time-comparison",o,a,l)}render(){const{mode:e}=this.props;return"item-comparison"===e?this.renderItemComparison():this.renderTimeComparison()}}g.contextType=d.CurrencyContext,g.defaultProps={isRequesting:!1,primaryData:{data:{intervals:[]},isError:!1,isRequesting:!1},secondaryData:{data:{intervals:[]},isError:!1,isRequesting:!1}};const h=(0,l.compose)((0,s.withSelect)(((e,t)=>{const{charts:r,endpoint:o,filters:a,isRequesting:l,limitProperties:c,query:s,advancedFilters:n}=t,u=c||[o],d=_(a,s),p=(0,i.get)(d,["settings","param"]),f=t.mode||function(e,t){if(e&&t){const r=(0,i.get)(e,["settings","param"]);if(!r||Object.keys(t).includes(r))return(0,i.get)(e,["chartMode"])}return null}(d,s)||"time-comparison",{woocommerce_default_date_range:y}=e(m.settingsStore).getSetting("wc_admin","wcAdminSettings"),w={mode:f,filterParam:p,defaultDateRange:y};if(l)return w;const g=u.some((e=>s[e]&&s[e].length));if(s.search&&!g)return{...w,emptySearchResults:!0};const h=e(m.reportsStore),v=r&&r.map((e=>e.key)),b=(0,m.getReportChartData)({endpoint:o,dataType:"primary",query:s,selector:h,limitBy:u,filters:a,advancedFilters:n,defaultDateRange:y,fields:v});if("item-comparison"===f)return{...w,primaryData:b};const C=(0,m.getReportChartData)({endpoint:o,dataType:"secondary",query:s,selector:h,limitBy:u,filters:a,advancedFilters:n,defaultDateRange:y,fields:v});return{...w,primaryData:b,secondaryData:C}})))(g)},13560:(e,t,r)=>{r.d(t,{Qc:()=>n,eg:()=>s,uW:()=>i});var o=r(27723),a=r(52619),l=r(33958),c=r(56109);const s=(0,a.applyFilters)("woocommerce_admin_orders_report_charts",[{key:"orders_count",label:(0,o.__)("Orders","woocommerce"),type:"number"},{key:"net_revenue",label:(0,o.__)("Net sales","woocommerce"),order:"desc",orderby:"net_total",type:"currency"},{key:"avg_order_value",label:(0,o.__)("Average order value","woocommerce"),type:"currency"},{key:"avg_items_per_order",label:(0,o.__)("Average items per order","woocommerce"),order:"desc",orderby:"num_items_sold",type:"average"}]),i=(0,a.applyFilters)("woocommerce_admin_orders_report_filters",[{label:(0,o.__)("Show","woocommerce"),staticParams:["chartType","paged","per_page"],param:"filter",showFilters:()=>!0,filters:[{label:(0,o.__)("All orders","woocommerce"),value:"all"},{label:(0,o.__)("Advanced filters","woocommerce"),value:"advanced"}]}]),n=(0,a.applyFilters)("woocommerce_admin_orders_report_advanced_filters",{title:(0,o._x)("Orders match <select/> filters","A sentence describing filters for Orders. See screen shot for context: https://cloudup.com/cSsUY9VeCVJ","woocommerce"),filters:{status:{labels:{add:(0,o.__)("Order status","woocommerce"),remove:(0,o.__)("Remove order status filter","woocommerce"),rule:(0,o.__)("Select an order status filter match","woocommerce"),title:(0,o.__)("<title>Order status</title> <rule/> <filter/>","woocommerce"),filter:(0,o.__)("Select an order status","woocommerce")},rules:[{value:"is",label:(0,o._x)("Is","order status","woocommerce")},{value:"is_not",label:(0,o._x)("Is Not","order status","woocommerce")}],input:{component:"SelectControl",options:Object.keys(c.wm).map((e=>({value:e,label:c.wm[e]})))}},product:{labels:{add:(0,o.__)("Product","woocommerce"),placeholder:(0,o.__)("Search products","woocommerce"),remove:(0,o.__)("Remove product filter","woocommerce"),rule:(0,o.__)("Select a product filter match","woocommerce"),title:(0,o.__)("<title>Product</title> <rule/> <filter/>","woocommerce"),filter:(0,o.__)("Select products","woocommerce")},rules:[{value:"includes",label:(0,o._x)("Includes","products","woocommerce")},{value:"excludes",label:(0,o._x)("Excludes","products","woocommerce")}],input:{component:"Search",type:"products",getLabels:l.p0}},variation:{labels:{add:(0,o.__)("Product variation","woocommerce"),placeholder:(0,o.__)("Search product variations","woocommerce"),remove:(0,o.__)("Remove product variation filter","woocommerce"),rule:(0,o.__)("Select a product variation filter match","woocommerce"),title:(0,o.__)("<title>Product variation</title> <rule/> <filter/>","woocommerce"),filter:(0,o.__)("Select variation","woocommerce")},rules:[{value:"includes",label:(0,o._x)("Includes","variations","woocommerce")},{value:"excludes",label:(0,o._x)("Excludes","variations","woocommerce")}],input:{component:"Search",type:"variations",getLabels:l.b8}},coupon:{labels:{add:(0,o.__)("Coupon code","woocommerce"),placeholder:(0,o.__)("Search coupons","woocommerce"),remove:(0,o.__)("Remove coupon filter","woocommerce"),rule:(0,o.__)("Select a coupon filter match","woocommerce"),title:(0,o.__)("<title>Coupon code</title> <rule/> <filter/>","woocommerce"),filter:(0,o.__)("Select coupon codes","woocommerce")},rules:[{value:"includes",label:(0,o._x)("Includes","coupon code","woocommerce")},{value:"excludes",label:(0,o._x)("Excludes","coupon code","woocommerce")}],input:{component:"Search",type:"coupons",getLabels:l.U4}},customer_type:{labels:{add:(0,o.__)("Customer type","woocommerce"),remove:(0,o.__)("Remove customer filter","woocommerce"),rule:(0,o.__)("Select a customer filter match","woocommerce"),title:(0,o.__)("<title>Customer is</title> <filter/>","woocommerce"),filter:(0,o.__)("Select a customer type","woocommerce")},input:{component:"SelectControl",options:[{value:"new",label:(0,o.__)("New","woocommerce")},{value:"returning",label:(0,o.__)("Returning","woocommerce")}],defaultOption:"new"}},refunds:{labels:{add:(0,o.__)("Refund","woocommerce"),remove:(0,o.__)("Remove refund filter","woocommerce"),rule:(0,o.__)("Select a refund filter match","woocommerce"),title:(0,o.__)("<title>Refund</title> <filter/>","woocommerce"),filter:(0,o.__)("Select a refund type","woocommerce")},input:{component:"SelectControl",options:[{value:"all",label:(0,o.__)("All","woocommerce")},{value:"partial",label:(0,o.__)("Partially refunded","woocommerce")},{value:"full",label:(0,o.__)("Fully refunded","woocommerce")},{value:"none",label:(0,o.__)("None","woocommerce")}],defaultOption:"all"}},tax_rate:{labels:{add:(0,o.__)("Tax rate","woocommerce"),placeholder:(0,o.__)("Search tax rates","woocommerce"),remove:(0,o.__)("Remove tax rate filter","woocommerce"),rule:(0,o.__)("Select a tax rate filter match","woocommerce"),title:(0,o.__)("<title>Tax Rate</title> <rule/> <filter/>","woocommerce"),filter:(0,o.__)("Select tax rates","woocommerce")},rules:[{value:"includes",label:(0,o._x)("Includes","tax rate","woocommerce")},{value:"excludes",label:(0,o._x)("Excludes","tax rate","woocommerce")}],input:{component:"Search",type:"taxes",getLabels:l.jx}},attribute:{allowMultiple:!0,labels:{add:(0,o.__)("Product attribute","woocommerce"),placeholder:(0,o.__)("Search product attributes","woocommerce"),remove:(0,o.__)("Remove product attribute filter","woocommerce"),rule:(0,o.__)("Select a product attribute filter match","woocommerce"),title:(0,o.__)("<title>Product attribute</title> <rule/> <filter/>","woocommerce"),filter:(0,o.__)("Select attributes","woocommerce")},rules:[{value:"is",label:(0,o._x)("Is","product attribute","woocommerce")},{value:"is_not",label:(0,o._x)("Is Not","product attribute","woocommerce")}],input:{component:"ProductAttribute"}}}})},32639:(e,t,r)=>{r.d(t,{H:()=>a});var o=r(27723);function a(e){return[e.country,e.state,e.name||(0,o.__)("TAX","woocommerce"),e.priority].map((e=>e.toString().toUpperCase().trim())).filter(Boolean).join("-")}},33958:(e,t,r)=>{r.d(t,{Dn:()=>d,U4:()=>_,aG:()=>p,b8:()=>h,jx:()=>w,p0:()=>y,wd:()=>f,xP:()=>g});var o=r(27723),a=r(93832),l=r(1455),c=r.n(l),s=r(66087),i=r(96476),n=r(40314),m=r(32639),u=r(56109);function d(e,t=s.identity){return function(r="",o){const l="function"==typeof e?e(o):e,s=(0,i.getIdsFromQuery)(r);if(s.length<1)return Promise.resolve([]);const n={include:s.join(","),per_page:s.length};return c()({path:(0,a.addQueryArgs)(l,n)}).then((e=>e.map(t)))}}d(n.NAMESPACE+"/products/attributes",(e=>({key:e.id,label:e.name})));const p=d(n.NAMESPACE+"/products/categories",(e=>({key:e.id,label:e.name}))),_=d(n.NAMESPACE+"/coupons",(e=>({key:e.id,label:e.code}))),f=d(n.NAMESPACE+"/customers",(e=>({key:e.id,label:e.name}))),y=d(n.NAMESPACE+"/products",(e=>({key:e.id,label:e.name}))),w=d(n.NAMESPACE+"/taxes",(e=>({key:e.id,label:(0,m.H)(e)})));function g({attributes:e,name:t}){const r=(0,u.Qk)("variationTitleAttributesSeparator"," - ");if(t&&t.indexOf(r)>-1)return t;const a=(e||[]).map((({name:e,option:t})=>(t||(e=e.charAt(0).toUpperCase()+e.slice(1),t=(0,o.sprintf)((0,o.__)("Any %s","woocommerce"),e)),t))).join(", ");return a?t+r+a:t}const h=d((({products:e})=>e?n.NAMESPACE+`/products/${e}/variations`:n.NAMESPACE+"/variations"),(e=>({key:e.id,label:g(e)})))}}]);